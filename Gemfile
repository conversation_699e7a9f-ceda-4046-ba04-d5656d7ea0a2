source "https://rubygems.org"

# ruby '3.3.0'
# 10 may 2025 - thought going to 3.4.3 would be easy
# Turned out to be a bit of a pain with sassc, tailwindcss-rails
# and administrate issues..
# 1 july 2025 - tried again with 3.4.3 and it worked!
ruby "3.4.3"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
# gem 'rails', '~> 7.1.3', '>= *******'
# changing above to below and running bundle update
#  only updated from rails ******* to *******
gem "rails"

# gem 'rails', '~> 7.2.0'

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
# gem 'sprockets-rails'

# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"

# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"

# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"

# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"

# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"

# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"

# Use Redis adapter to run Action Cable in production
# gem "redis", ">= 4.0.1"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[windows jruby]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
gem "image_processing" # , "~> 1.2"
# vips took forever to install on mba 3 and then failed so will try below:
gem "mini_magick"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[mri windows]
  gem "rspec-rails" # , '~> 5.0.0'
  gem "factory_bot_rails"
end

# need below for rake task to guess prices
gem "faker"

group :test do
  # gem 'capybara'
  # gem 'selenium-webdriver'
  # gem 'webdrivers'
  gem "webmock"
end

group :development do
  gem "meta_request"
  # above needed for below
  # https://github.com/dejan/rails_panel
  gem "annotate"

  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

gem "activemodel-serializers-xml"
gem "globalize", git: "https://github.com/globalize/globalize"
gem "money-rails"

gem "cloudinary"
gem "devise"
gem "flag_shih_tzu"
gem "geocoder"
gem "omniauth"

gem "active_hash" # , "~> 3.1"
# gem 'bootstrap-sass'
gem "carrierwave"
gem "globalize-accessors"
gem "i18n-active_record"
gem "jquery-rails" # , "~> 4.5"
gem "paloma"

# gem 'sassc-rails'
# removed above because of conflict with tailwindcss-rails
# https://github.com/rails/tailwindcss-rails#conflict-with-sassc-rails
# gem 'vite_rails'
# gem "liquid", "~> 5.3"
gem "acts_as_tenant"
gem "liquid"
gem "rswag"

gem "store_attribute"

# gem "eu_central_bank", "~> 1.7"

# # considered using below but realised its easier to just
# # get rates directly from json api
# # gem "money-open-exchange-rates", "~> 1.4"

# gem "acts-as-taggable-on" #, "~> 9.0"

# gem "friendly_uuid", "~> 0.3.1"

# gem "acts_as_list", "~> 1.0"

# gem "maintenance_tasks", "~> 2.0"

# gem "firebase_id_token" #, "~> 2.5"

# gem "newrelic_rpm", "~> 8.15"

# gem "bullet", "~> 7.0", group: "development"
# gem "geocoder", "~> 1.8"
# gem "cosine-similarity", "~> 1.0"
# gem "ruby-openai" #, "~> 3.5"
# gem "notable", "~> 0.4.1"
gem "rack-cors" # , '~> 1.1'

# gem "strong_migrations", "~> 0.7.6"

gem "ahoy_matey" #, "~> 4.0"

# gem "devise-jwt", "~> 0.9.0"

# gem "devise-tailwindcssed"
# gem "tailwindcss-rails"

gem "redis"
gem "logster"

gem "firebase_id_token"
gem "jwt"

gem "haikunator"
gem "counter_culture"

gem "trailblazer-rails"

gem "dry-monads"
gem "dry-struct"
gem "dry-types"
gem "dry-rails"
gem "aws-sdk-s3"

gem "discard"
gem "mobility"
gem "safely_block"
gem "paper_trail"
gem "fastimage"
gem "resonad"

gem "ruby-openai"
# gem "openai", "~> 0.3.0"

gem "down", "~> 5.4"
gem "langchainrb_rails"

# gem "administrate" # , '0.18.0'
# when I tried changing administrate ver above
# needed to re-add below
gem "sprockets-rails"
# gem "administrate-field-active_storage"
# 1 july 2025 - trying again:
gem "administrate", "1.0.0.beta3"
# 1 july 2025 - had disabled sprockets-rails so had to reenable again
# and had to disable below:
# https://github.com/Dreamersoul/administrate-field-active_storage
gem "mission_control-jobs"
gem "solid_queue"
gem "activerecord-postgis-adapter"
gem "underpass"
gem "amatch"
gem "rgeo"
gem "rgeo-geojson"
# had considered below for 2025_01_12_import_uprn_details.rake
# to convert easting and northing to latitude and longitude but
# decided to use PostGIS instead
# gem 'rgeo-proj4'
# TODO - experiment with akane gems like below:
# gem "mapkick-rb"
# gem 'mapbox-sdk'??
# gem 'lightgbm'
# gem 'daru'
# gem 'daru-view'
# app/ml_models/price_model.txt
# and especially below for which I have above
gem "eps"
gem "chartkick"
gem "groupdate"
gem "playwright-ruby-client"
# Have to remember to do below:
# bundle install --no-cache
gem "execjs"

gem "terminalwire-rails"
gem "terminalwire", group: :development

gem "ruby_llm"
gem "ferrum"
gem "imgkit"
# previously vcr had been in test group
gem "vcr"
# bundle install --no-cache

gem "tailwindcss-rails", "~> 4.3"
