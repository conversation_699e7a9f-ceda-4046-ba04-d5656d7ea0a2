GIT
  remote: https://github.com/globalize/globalize
  revision: 065cb55c46c74fd92a2d709c50020546bde33da3
  specs:
    globalize (7.0.0)
      activemodel (>= 7.0, < 8.1)
      activerecord (>= 7.0, < 8.1)
      activesupport (>= 7.0, < 8.1)
      request_store (~> 1.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_hash (3.3.1)
      activesupport (>= 5.0.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.3)
      activemodel (>= 5.0.0.a)
      activesupport (>= 5.0.0.a)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-postgis-adapter (10.0.1)
      activerecord (~> 7.2.0)
      rgeo-activerecord (~> 8.0.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    acts_as_tenant (1.0.1)
      rails (>= 6.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    administrate (1.0.0.beta3)
      actionpack (>= 6.0, < 9.0)
      actionview (>= 6.0, < 9.0)
      activerecord (>= 6.0, < 9.0)
      kaminari (~> 1.2.2)
    ahoy_matey (5.4.0)
      activesupport (>= 7.1)
      device_detector (>= 1)
      safely_block (>= 0.4)
    amatch (0.4.1)
      mize
      tins (~> 1.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    async (2.24.0)
      console (~> 1.29)
      fiber-annotation
      io-event (~> 1.9)
      metrics (~> 0.12)
      traces (~> 0.15)
    async-http (0.89.0)
      async (>= 2.10.2)
      async-pool (~> 0.9)
      io-endpoint (~> 0.14)
      io-stream (~> 0.6)
      metrics (~> 0.12)
      protocol-http (~> 0.49)
      protocol-http1 (~> 0.30)
      protocol-http2 (~> 0.22)
      traces (~> 0.10)
    async-pool (0.10.3)
      async (>= 1.25)
    async-websocket (0.30.0)
      async-http (~> 0.76)
      protocol-http (~> 0.34)
      protocol-rack (~> 0.7)
      protocol-websocket (~> 0.17)
    aws-eventstream (1.3.2)
    aws-partitions (1.1102.0)
    aws-sdk-core (3.223.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.100.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.185.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    baran (0.1.12)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    builder (3.3.0)
    carrierwave (3.1.2)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    chartkick (5.1.5)
    childprocess (5.1.0)
      logger (~> 1.5)
    cloudinary (2.3.0)
      faraday (>= 2.0.1, < 3.0.0)
      faraday-follow_redirects (~> 0.3.0)
      faraday-multipart (~> 1.0, >= 1.0.4)
      ostruct
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    console (1.30.2)
      fiber-annotation
      fiber-local (~> 1.1)
      json
    counter_culture (3.10.1)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.4)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    declarative (0.0.20)
    device_detector (1.1.3)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.1)
    discard (1.4.0)
      activerecord (>= 4.2, < 9.0)
    disposable (0.6.3)
      declarative (>= 0.0.9, < 1.0.0)
      representable (>= 3.1.1, < 4)
    down (5.4.2)
      addressable (~> 2.8)
    drb (2.2.3)
    dry-auto_inject (1.1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-configurable (1.3.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    dry-inflector (1.2.0)
    dry-initializer (3.2.0)
    dry-logic (1.6.0)
      bigdecimal
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-monads (1.8.3)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-rails (0.7.0)
      dry-schema (~> 1.7)
      dry-system (~> 1.0, < 2)
      dry-validation (~> 1.5)
    dry-schema (1.14.1)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.1)
      dry-initializer (~> 3.2)
      dry-logic (~> 1.5)
      dry-types (~> 1.8)
      zeitwerk (~> 2.6)
    dry-struct (1.8.0)
      dry-core (~> 1.1)
      dry-types (~> 1.8, >= 1.8.2)
      ice_nine (~> 0.11)
      zeitwerk (~> 2.6)
    dry-system (1.2.2)
      dry-auto_inject (~> 1.1)
      dry-configurable (~> 1.3)
      dry-core (~> 1.1)
      dry-inflector (~> 1.1)
    dry-types (1.8.2)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    dry-validation (1.11.1)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      dry-initializer (~> 3.2)
      dry-schema (~> 1.14)
      zeitwerk (~> 2.6)
    eps (0.6.0)
      lightgbm (>= 0.1.7)
      matrix
      nokogiri
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    execjs (2.10.0)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    faraday-retry (2.3.1)
      faraday (~> 2.0)
    fastimage (2.4.0)
    ferrum (0.16)
      addressable (~> 2.5)
      base64 (~> 0.2)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (~> 0.7)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    fiber-annotation (0.2.0)
    fiber-local (1.1.0)
      fiber-storage
    fiber-storage (1.0.1)
    firebase_id_token (3.0.0)
      activesupport (~> 7.0, >= *******)
      httparty (~> 0.21.0)
      json (~> 2.6, >= 2.6.3)
      jwt (~> 2.7)
      redis (~> 5.0, >= 5.0.6)
      redis-namespace (~> 1.10)
    flag_shih_tzu (0.3.23)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    globalize-accessors (0.3.0)
      globalize (>= 5.0.0)
    groupdate (6.6.0)
      activesupport (>= 7.1)
    haikunator (1.1.1)
    hashdiff (1.1.2)
    hashie (5.0.0)
    hirb (0.7.3)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    i18n-active_record (1.4.0)
      i18n (>= 0.5.0)
    ice_nine (0.11.2)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    imgkit (1.6.3)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    io-endpoint (0.15.2)
    io-event (1.10.0)
    io-stream (0.6.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.11.3)
    json-schema (4.3.1)
      addressable (>= 2.8)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    langchainrb (0.16.1)
      baran (~> 0.1.9)
      json-schema (~> 4)
      matrix
      pragmatic_segmenter (~> 0.3.0)
      zeitwerk (~> 2.5)
    langchainrb_rails (0.1.12)
      langchainrb (>= 0.7, < 0.17)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    lightgbm (0.4.1)
      ffi
    liquid (5.8.6)
      bigdecimal
      strscan (>= 3.1.1)
    logger (1.7.0)
    logster (2.20.1)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    meta_request (0.8.5)
      rack-contrib (>= 1.1, < 3)
      railties (>= 3.0.0, < 9)
    metrics (0.12.2)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0507)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    mission_control-jobs (1.0.2)
      actioncable (>= 7.1)
      actionpack (>= 7.1)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      importmap-rails (>= 1.2.1)
      irb (~> 1.13)
      railties (>= 7.1)
      stimulus-rails
      turbo-rails
    mize (0.6.1)
    mobility (1.3.2)
      i18n (>= 0.6.10, < 2)
      request_store (~> 1.0)
    monetize (1.13.0)
      money (~> 6.12)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    net-http (0.6.0)
      uri
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    paloma (6.1.0)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    pg (1.5.9)
    playwright-ruby-client (1.51.0)
      concurrent-ruby (>= 1.1.6)
      mime-types (>= 3.0)
    pp (0.6.2)
      prettyprint
    pragmatic_segmenter (0.3.24)
    prettyprint (0.2.0)
    protocol-hpack (1.5.1)
    protocol-http (0.50.1)
    protocol-http1 (0.34.0)
      protocol-http (~> 0.22)
    protocol-http2 (0.22.1)
      protocol-hpack (~> 1.4)
      protocol-http (~> 0.47)
    protocol-rack (0.13.0)
      protocol-http (~> 0.43)
      rack (>= 1.0)
    protocol-websocket (0.20.2)
      protocol-http (~> 0.2)
    psych (5.2.5)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.14)
    rack-contrib (2.5.0)
      rack (< 4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    rdoc (6.13.1)
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    reform (2.6.2)
      disposable (>= 0.5.0, < 1.0.0)
      representable (>= 3.1.1, < 4)
      uber (< 0.2.0)
    reline (0.6.1)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    resonad (1.4.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.1)
    rgeo (3.0.1)
    rgeo-activerecord (8.0.0)
      activerecord (>= 7.0)
      rgeo (>= 3.0)
    rgeo-geojson (2.2.0)
      multi_json (~> 1.15)
      rgeo (>= 1.0.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.0)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.3)
    rswag (2.16.0)
      rswag-api (= 2.16.0)
      rswag-specs (= 2.16.0)
      rswag-ui (= 2.16.0)
    rswag-api (2.16.0)
      activesupport (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rswag-specs (2.16.0)
      activesupport (>= 5.2, < 8.1)
      json-schema (>= 2.2, < 6.0)
      railties (>= 5.2, < 8.1)
      rspec-core (>= 2.14)
    rswag-ui (2.16.0)
      actionpack (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    ruby-openai (8.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby_llm (1.2.0)
      base64
      event_stream_parser (~> 1)
      faraday (~> 2)
      faraday-multipart (~> 1)
      faraday-net_http (~> 3)
      faraday-retry (~> 2)
      zeitwerk (~> 2)
    safely_block (0.5.0)
    securerandom (0.4.1)
    solid_queue (1.1.5)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (~> 1.3.1)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    ssrf_filter (1.2.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    store_attribute (2.0.1)
      activerecord (>= 6.1)
    stringio (3.1.7)
    strscan (3.1.5)
    sync (0.5.0)
    tailwindcss-rails (4.3.0)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 4.0)
    tailwindcss-ruby (4.1.12-arm64-darwin)
    tailwindcss-ruby (4.1.12-x86_64-darwin)
    tailwindcss-ruby (4.1.12-x86_64-linux-gnu)
    terminalwire (0.3.4)
      terminalwire-client (= 0.3.4)
    terminalwire-client (0.3.4)
      launchy (~> 3.0)
      terminalwire-core (= 0.3.4)
    terminalwire-core (0.3.4)
      async-websocket (~> 0.30)
      base64 (~> 0.2.0)
      msgpack (~> 1.7)
      uri-builder (~> 0.1.9)
      zeitwerk (~> 2.0)
    terminalwire-rails (0.3.4)
      jwt (~> 2.0)
      terminalwire-server (= 0.3.4)
    terminalwire-server (0.3.4)
      terminalwire-core (= 0.3.4)
      thor (~> 1.3)
    thor (1.3.2)
    timeout (0.4.3)
    tins (1.38.0)
      bigdecimal
      sync
    traces (0.15.2)
    trailblazer (2.1.3)
      trailblazer-activity-dsl-linear (>= 1.2.3, < 1.3.0)
      trailblazer-developer (>= 0.1.0, < 0.2.0)
      trailblazer-macro (>= 2.1.15, < 2.2.0)
      trailblazer-macro-contract (>= 2.1.4, < 2.2.0)
      trailblazer-operation (>= 0.9.0, < 1.0.0)
    trailblazer-activity (0.17.0)
      trailblazer-context (~> 0.5.0)
      trailblazer-option (~> 0.1.0)
    trailblazer-activity-dsl-linear (1.2.6)
      trailblazer-activity (>= 0.17.0, < 0.18.0)
      trailblazer-declarative (>= 0.0.1, < 0.1.0)
    trailblazer-context (0.5.1)
      hashie (>= 3.0.0)
    trailblazer-declarative (0.0.2)
    trailblazer-developer (0.1.0)
      hirb
      trailblazer-activity-dsl-linear (>= 1.2.0, < 1.3.0)
    trailblazer-macro (2.1.16)
      trailblazer-activity-dsl-linear (>= 1.2.0, < 1.3.0)
      trailblazer-operation (>= 0.10.1)
    trailblazer-macro-contract (2.1.5)
      reform (>= 2.2.0, < 3.0.0)
      trailblazer-activity-dsl-linear (>= 1.2.0, < 1.3.0)
    trailblazer-operation (0.11.0)
      trailblazer-activity-dsl-linear (>= 1.2.3, < 1.4.0)
      trailblazer-developer (>= 0.1.0, < 0.2.0)
    trailblazer-option (0.1.2)
    trailblazer-rails (2.4.4)
      railties (>= 6.0.0)
      trailblazer (>= 2.1.0, < 2.2.0)
    turbo-rails (2.0.13)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    underpass (0.0.2)
      rgeo
    uri (1.0.3)
    uri-builder (0.1.13)
    useragent (0.16.11)
    vcr (6.3.1)
      base64
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.2)

PLATFORMS
  arm64-darwin-24
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  active_hash
  activemodel-serializers-xml
  activerecord-postgis-adapter
  acts_as_tenant
  administrate (= 1.0.0.beta3)
  ahoy_matey
  amatch
  annotate
  aws-sdk-s3
  bootsnap
  carrierwave
  chartkick
  cloudinary
  counter_culture
  debug
  devise
  discard
  down (~> 5.4)
  dry-monads
  dry-rails
  dry-struct
  dry-types
  eps
  execjs
  factory_bot_rails
  faker
  fastimage
  ferrum
  firebase_id_token
  flag_shih_tzu
  geocoder
  globalize!
  globalize-accessors
  groupdate
  haikunator
  i18n-active_record
  image_processing
  imgkit
  importmap-rails
  jbuilder
  jquery-rails
  jwt
  langchainrb_rails
  liquid
  logster
  meta_request
  mini_magick
  mission_control-jobs
  mobility
  money-rails
  omniauth
  paloma
  paper_trail
  pg (~> 1.1)
  playwright-ruby-client
  puma (>= 5.0)
  rack-cors
  rails
  redis
  resonad
  rgeo
  rgeo-geojson
  rspec-rails
  rswag
  ruby-openai
  ruby_llm
  safely_block
  solid_queue
  sprockets-rails
  stimulus-rails
  store_attribute
  tailwindcss-rails (~> 4.3)
  terminalwire
  terminalwire-rails
  trailblazer-rails
  turbo-rails
  tzinfo-data
  underpass
  vcr
  web-console
  webmock

RUBY VERSION
   ruby 3.4.3p32

BUNDLED WITH
   2.6.6
