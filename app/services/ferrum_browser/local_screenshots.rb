module FerrumBrowser
  class LocalScreenshots
    def initialize(options = {})
      @headless = options[:headless].nil? ? false : options[:headless]
      @viewport = options[:viewport] || { width: 1280, height: 720 }
      @locale = options[:locale] || 'en-GB'
    end

    # Captures screenshot of dossier asset images for a specific sale listing
    def capture_dossier_asset_images(dossier, sale_listing)
      retrieval_endpoint = "http://damp-violet.lvh.me:3333/api_public/v4/dossiers/show_image/#{dossier.uuid}/listing/#{sale_listing.uuid}"
      # retrieval_endpoint = "http://damp-violet.lvh.me:9100/rawdossiers/#{dossier.uuid}/listing_photos/#{sale_listing.uuid}"
      destination_filename = "dossier_#{dossier.id}_listing_#{sale_listing.id}"
      capture(retrieval_endpoint, destination_filename)
    end

    private

    # Prepares the file path for screenshot storage
    def prepare_screenshot_path(destination_filename)
      file_extension = 'jpeg'
      destination_directory = Rails.root.join('app', 'assets', 'images', 'h2c', 'screens_for_ai')
      FileUtils.mkdir_p(destination_directory)
      destination_directory.join("#{destination_filename}.#{file_extension}")
    end

    # Creates and configures a new browser instance
    def create_browser
      Ferrum::Browser.new(
        headless: @headless,
        window_size: [@viewport[:width], @viewport[:height]],
        browser_options: {
          'lang' => @locale,
          'no-sandbox' => true,
          'disable-blink-features' => 'AutomationControlled'
        },
        timeout: 130, # seconds
        process_timeout: 130 # Add process_timeout to allow Chrome more time to start
      )
    end

    # Ensures all images are loaded on the page
    def wait_for_images(browser)
      browser.evaluate_async(<<~JS, 300)
        new Promise((resolve) => {
          const images = document.querySelectorAll('img');
          let loaded = 0;

          function checkImages() {
            loaded = [...images].filter(img => img.complete && img.naturalHeight !== 0).length;
            if (loaded === images.length) {
              resolve();
            }
          }

          images.forEach(img => {
            if (img.complete) {
              checkImages();
            } else {
              img.addEventListener('load', checkImages);
              img.addEventListener('error', checkImages);
            }
          });
          if (images.length === 0) resolve();
        })
      JS
    end

    # Captures the screenshot from the given endpoint
    def capture(retrieval_endpoint, destination_filename)
      screenshot_path = prepare_screenshot_path(destination_filename)
      browser = create_browser
      puts "Capturing screenshot from #{retrieval_endpoint} to #{screenshot_path}"
      begin
        # Navigate to the page and wait for network to be idle
        browser.goto(retrieval_endpoint)
        browser.network.wait_for_idle

        # Scroll to bottom of page
        browser.evaluate('window.scrollTo(0, document.body.scrollHeight)')
        sleep(1)

        # Wait for all images to load
        # wait_for_images(browser)
        # browser.wait_for_selector('img', timeout: 30_000)
        # browser.evaluate('document.querySelector("img").complete')
        sleep(0.5) # Buffer delay

        # Capture the screenshot
        browser.screenshot(
          path: screenshot_path,
          full: true,
          format: :jpeg
        )

        puts "Screenshot saved to: #{screenshot_path}"
      rescue Ferrum::TimeoutError => e
        puts "Error: Timeout waiting for page or images to load - #{e.message}"
      rescue StandardError => e
        puts "Error: Failed to take screenshot - #{e.message}"
      ensure
        browser.quit
      end

      screenshot_path
    end
  end
end
