module AiGen
  class GenericPropertyFromGeoCluster
    def self.process(cluster)
      new(cluster).process
    end

    def initialize(cluster)
      @cluster = cluster
    end

    def process
      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
      cluster_json = @cluster.as_json(
        include: {
          postcode_areas: { only: %i[uuid postal_code] },
          sold_transactions: {
            include: {
              listing_photos: {
                methods: [:image_details],
                only: %i[uuid sort_order]
              }
            },
            methods: [:formatted_sold_price],
            only: %i[uuid new_home sold_date sold_transaction_reference sold_transaction_title]
          }
        },
        only: %w[
          cluster_name
          cluster_slug
          cluster_description
          center_latitude
          center_longitude
          bbox_max_latitude
          bbox_max_longitude
          bbox_min_latitude
          bbox_min_longitude
          cluster_city
          cluster_country
          cluster_region
        ]
      )

      # Check if there are any sold transactions before proceeding
      raise StandardError, "No sold transactions found for GeoCluster with ID #{@cluster.id}" if @cluster.real_sold_transactions.empty?

      prompt = PromptGenerators::GenericPropertyFromGeoCluster.create_prompt(
        cluster_json
      )

      response = openai_chat(prompt)

      if content = response.dig('choices', 0, 'message', 'content')
        begin
          json_response = JSON.parse(content)
          create_or_update_property(json_response, prompt)
        rescue JSON::ParserError => e
          cleaned_json = clean_json(content)
          if cleaned_json
            create_or_update_property(cleaned_json, prompt)
          else
            puts "Failed to parse or clean JSON for GeoCluster with ID #{@cluster.id}. Error: #{e.message}"
          end
        end
      else
        puts "No valid content response for GeoCluster with ID #{@cluster.id}"
      end
    rescue StandardError => e
      puts "Error processing GeoCluster with ID #{@cluster.id}: #{e.message}"
      raise # Re-throw the error so it can be handled by the caller if necessary
    end

    private

    def openai_chat(prompt)
      client = OpenAI::Client.new(access_token: Rails.application.credentials.config[:openai_api_key])
      postcode_summary = @cluster.postcode_areas.pluck(:postcode_area_slug).compact.first
      VCR.use_cassette("cluster_benchmark_property_from_#{postcode_summary}") do
        client.chat(
          parameters: {
            # model: 'gpt-4o',
            model: 'gpt-4o-mini',
            messages: [{ role: 'user', content: prompt }],
            # max_tokens: 1500,
            response_format: { type: 'json_object' }
          }
        )
      end
    end

    def create_or_update_property(data, generic_property_gen_prompt)
      gen_prop = GenericProperty.find_or_initialize_by(
        generic_property_reference: data['reference']
      )
      gen_prop.update(
        generic_property_gen_data: data,
        generic_property_gen_prompt:,
        generic_property_title: data['property_title'],
        generic_property_description: data['property_description'],
        no_of_bedrooms: data['count_bedrooms'],
        street_name: data['street_address'],
        postal_code: data['postal_code'],
        city: data['city']
      )

      # 29 dec 2024 - now using clusters rather than postcode areas
      # postcode_area = PostcodeArea.find_or_create_by(postal_code: data['postal_code'])

      data['sold_transactions'].each do |transaction_data|
        # price_in_cents = (transaction_data['sale_price'].to_f * 100).to_i
        price_in_cents = (transaction_data['formatted_sold_price'].gsub(/[^\d.]/, '').to_i * 100).to_i
        sold_date = transaction_data['transaction_date'] || transaction_data['sold_date']
        sold_transaction = gen_prop.sold_transactions.find_or_create_by(
          sold_date:
        )
        sold_transaction.update!(
          generic_property: gen_prop,
          is_synthetic_transaction: true,
          # postcode_area_uuid: postcode_area.uuid,
          sold_price_cents: price_in_cents,
          sold_price_currency: 'GBP'
        )
      end
      # last_sold_transaction = gen_prop.sold_transactions.order(sold_date: :asc).last
      last_sold_transaction = gen_prop.sold_transactions.where.not(sold_date: nil).order(sold_date: :asc).last
      gen_prop.update!(
        # postcode_area_uuid: postcode_area.uuid,
        reasonable_sale_price_cents: last_sold_transaction.sold_price_cents,
        reasonable_sale_price_currency: last_sold_transaction.sold_price_currency
      )

      @cluster.update!(
        cluster_average_property_price_cents: gen_prop.reasonable_sale_price_cents,
        cluster_average_property_price_currency: gen_prop.reasonable_sale_price_currency,
        cluster_city: @cluster.cluster_city || data['city'],
        cluster_region: @cluster.cluster_region || data['region'],
        cluster_description: @cluster.cluster_description || data['neighborhood_description'],
        benchmark_property_uuid: gen_prop.uuid
      )
      # @cluster.update!(generic_property: gen_prop)

      puts "Processed GeoCluster with ID #{@cluster.id} into GenericProperty"
    end

    def clean_json(json_string)
      cleaned = json_string.gsub(/\\"/, '"').gsub(/'/, '"').gsub(/\s+/, ' ').strip
      begin
        JSON.parse(cleaned)
      rescue JSON::ParserError
        nil
      end
    end
  end
end
