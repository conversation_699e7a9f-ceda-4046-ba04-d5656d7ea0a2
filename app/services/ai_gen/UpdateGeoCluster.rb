module AiGen
  class UpdateGeoCluster
    def self.process(cluster)
      new(cluster).process
    end

    def initialize(cluster)
      @cluster = cluster
    end

    def process
      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
      # Utilize Jbuilder to create the JSON structure and assign it to a variable
      # json_data = Jbuilder.new do |json|
      #   json.partial! '/api_public/v4/postcode_clusters/ai_gen_view',
      #                 postcode_area_cluster: GeoCluster.last
      # end.target!

      # Use the Rails controller to render the Jbuilder template
      cluster_json = ApplicationController.render(
        template: 'api_public/v4/postcode_clusters/ai_gen_view',
        assigns: {
          postcode_area_cluster: @cluster
        },
        formats: [:json]
      )

      # Check if there are any sold transactions before proceeding
      raise StandardError, "No sold transactions found for GeoCluster with ID #{@cluster.id}" if @cluster.real_sold_transactions.empty?

      prompt = PromptGenerators::UpdateGeoCluster.create_prompt(
        cluster_json
      )

      response = openai_chat(prompt)

      if content = response.dig('choices', 0, 'message', 'content')
        begin
          json_response = JSON.parse(content)
          update_geocluster(json_response, prompt)
        rescue JSON::ParserError => e
          cleaned_json = clean_json(content)
          if cleaned_json
            update_geocluster(cleaned_json, prompt)
          else
            puts "Failed to parse or clean JSON for GeoCluster with ID #{@cluster.id}. Error: #{e.message}"
          end
        end
      else
        puts "No valid content response for GeoCluster with ID #{@cluster.id}"
      end
    rescue StandardError => e
      puts "Error processing GeoCluster with ID #{@cluster.id}: #{e.message}"
      raise # Re-throw the error so it can be handled by the caller if necessary
    end

    private

    def openai_chat(prompt)
      client = OpenAI::Client.new(access_token: Rails.application.credentials.config[:openai_api_key])
      postcode_summary = @cluster.postcode_areas.pluck(:postcode_area_slug).compact.join('-')
      VCR.use_cassette("update_geocluster_for_#{postcode_summary}") do
        client.chat(
          parameters: {
            # model: 'gpt-4o',
            model: 'gpt-4o-mini',
            messages: [{ role: 'user', content: prompt }],
            # max_tokens: 1500,
            response_format: { type: 'json_object' }
          }
        )
      end
    end

    def update_geocluster(data, _generic_property_gen_prompt)
      @cluster.update!(
        cluster_name: data['neighborhood_title'],
        cluster_description: data['neighborhood_description'],
        neighborhood_pros: data['neighborhood_pros'],
        neighborhood_cons: data['neighborhood_cons'],
        outlier_transactions: data['outlier_transactions'],
        neighborhood_title: data['neighborhood_title'],
        neighborhood_description: data['neighborhood_description'],
        neighborhood_features: data['neighborhood_features'],
        neighborhood_amenities: data['neighborhood_amenities'],
        neighborhood_proximity: data['neighborhood_proximity'],
        places_that_add_value: data['places_that_add_value'],
        places_that_reduce_value: data['places_that_reduce_value'],
        similar_neighborhoods: data['similar_neighborhoods']
      )

      #   cluster_average_property_price_cents: gen_prop.reasonable_sale_price_cents,
      #   cluster_average_property_price_currency: gen_prop.reasonable_sale_price_currency,
      #   cluster_city: @cluster.cluster_city || data['city'],
      #   cluster_region: @cluster.cluster_region || data['region'],
      #   cluster_description: @cluster.cluster_description || data['neighborhood_description'],
      #   benchmark_property_uuid: gen_prop.uuid
      # )
      # @cluster.update!(generic_property: gen_prop)

      puts "Processed GeoCluster with ID #{@cluster.id} into GenericProperty"
    end

    def clean_json(json_string)
      cleaned = json_string.gsub(/\\"/, '"').gsub(/'/, '"').gsub(/\s+/, ' ').strip
      begin
        JSON.parse(cleaned)
      rescue JSON::ParserError
        nil
      end
    end
  end
end
