# app/services/exporters_and_loaders/sale_listings_exporter.rb
require 'json'
require 'fileutils'
require 'dry/monads'
require 'dry/monads/do'
require 'dry/struct'
require 'active_support/inflector' # For parameterize method to generate slug
require 'dry/types'

module ExportersAndLoaders
  class SaleListingsExporter
    include Dry::Monads[:result]
    include Dry::Monads::Do.for(:export)

    DEFAULT_LIMIT = 2

    def initialize(limit: DEFAULT_LIMIT, destination_dir: Rails.root.join('db', 'exports'))
      @limit = limit
      @destination_dir = destination_dir
    end

    def export(sale_listings)
      data = yield format_data(sale_listings)
      file_path = yield write_to_file(data)
      Success(file_path)
    end

    private

    attr_reader :limit, :destination_dir

    def format_data(sale_listings)
      formatted_data = sale_listings.map do |sale_listing|
        SaleListingData.new(
          sale_listing: sale_listing.attributes,
          realty_asset: sale_listing.realty_asset&.attributes,
          # attributes_with_full_urls on realty_asset_photos
          # will save the full_image details in json field
          # where I have uploaded an image to s3
          listing_photos: sale_listing.listing_photos.map(&:attributes_with_full_urls),
          slug: generate_slug(sale_listing.title)
        )
      end

      # if formatted_data.first.listing_photos.first['details'].blank?
      # if sale_listing.listing_photos.first.details.blank?
      # formatted_data.each do |sale_listing_data|
      #   sale_listing_data.listing_photos.each do |sl_photo|
      #     next unless sl_photo['details'].blank?

      #     photo_url
      #     sl_photo['remote_photo_url'] = photo_url
      #     sl_photo['details'][:remote_image] = remote_image_details(photo_url)
      #     sl_photo['photo_slug'] = photo_url
      #     # sl_photo.save!
      #   end
      #   # puts 'sale_listing.listing_photos.first.details.blank?'
      # end

      Success(formatted_data)
    rescue StandardError => e
      Failure("Error formatting data: #{e.message}")
    end

    def write_to_file(data)
      FileUtils.mkdir_p(destination_dir)
      data.each do |listing_data|
        file_name = "sale_listing_#{listing_data.slug}.json"
        file_path = File.join(destination_dir, file_name)

        File.open(file_path, 'w') do |file|
          file.write(JSON.pretty_generate(listing_data.to_h))
        end
      end
      Success(destination_dir)
    rescue StandardError => e
      Failure("Error writing to file: #{e.message}")
    end

    def generate_slug(title)
      title.to_s.parameterize
    end
  end

  # Define Types so it can be used in the dry-struct
  module Types
    include Dry.Types()
  end

  # Dry-struct to define the structure of the data for each sale listing
  class SaleListingData < Dry::Struct
    attribute :sale_listing, Types::Hash
    attribute :realty_asset, Types::Hash.optional
    attribute :listing_photos, Types::Array.of(Types::Hash)
    attribute :slug, Types::String
  end

  # # Dry-struct to define the structure of the data for each sale listing
  # class SaleListingData < Dry::Struct
  #   attribute :sale_listing, Types::Hash
  #   attribute :realty_asset, Types::Hash.optional
  #   attribute :listing_photos, Types::Array.of(Types::Hash)
  #   attribute :slug, Types::String
  # end
end
