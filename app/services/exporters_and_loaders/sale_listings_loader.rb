module ExportersAndLoaders
  class SaleListingsLoader
    attr_reader :file_path

    def initialize(file_path)
      @file_path = file_path
    end

    def load_sale_listings(tenant_to_use = nil)
      import_data = parse_json_file

      # import_data.each do |data|
      sale_listing_attributes = sanitize_attributes(import_data['sale_listing'])
      realty_asset_attributes = sanitize_attributes(import_data['realty_asset'])
      listing_photos_attributes = import_data['listing_photos']

      ActsAsTenant.current_tenant = tenant_to_use # AgencyTenant.unique_tenant

      # Find or create the SaleListing
      sale_listing = SaleListing.find_or_initialize_by(uuid: sale_listing_attributes['uuid'])
      sale_listing.update!(sale_listing_attributes)

      # Find or create the RealtyAsset
      if realty_asset_attributes
        realty_asset = RealtyAsset.find_or_initialize_by(uuid: realty_asset_attributes['uuid'])
        realty_asset.update!(realty_asset_attributes)
        sale_listing.realty_asset = realty_asset
      end

      # Clear existing photos and add new ones
      update_listing_photos(sale_listing, listing_photos_attributes)

      sale_listing.save!
      # end

      puts "Successfully imported sale listings from #{file_path}"
      sale_listing
    end

    private

    def parse_json_file
      file_content = File.read(file_path)
      JSON.parse(file_content)
    rescue Errno::ENOENT => e
      raise "File not found: #{file_path}. Error: #{e.message}"
    end

    def update_listing_photos(sale_listing, photos_attributes)
      # sale_listing.listing_photos.destroy_all
      photos_attributes.each do |photo_attributes|
        sanitized_photo_attributes = sanitize_attributes(photo_attributes)
        sale_listing.listing_photos.create!(sanitized_photo_attributes)
      end
      # debugger
    end

    def sanitize_attributes(attributes)
      return {} unless attributes

      attributes.except('agency_tenant_uuid', 'id', 'created_at', 'updated_at', 'uuid')
    end
  end
end
