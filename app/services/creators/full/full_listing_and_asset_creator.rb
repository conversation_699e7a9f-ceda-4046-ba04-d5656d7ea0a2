module Creators
  module Full
    class FullListingAndAssetCreator
      attr_accessor :save_images_locally

      def initialize(save_images_locally: false)
        @save_images_locally = save_images_locally
      end

      def create_from_standardised_hash(property_hash)
        property_hash.stringify_keys!
        import_url = property_hash['import_url'] || property_hash['listing_data']['import_url']

        raise ArgumentError, 'import_url cannot be empty' if import_url.nil? || import_url.strip.empty?

        #  "https://www.onthemarket.com/details/#{property_hash['reference']}/"
        # import_url = RealtyScrapers::SharedScraperHelpers.get_canonical_url_from_url(import_url)
        creator = Creators::Base::BaseListingAndAssetCreator.new
        target_sale_listing = creator.find_or_create_by_unique_url(
          import_url
        )

        has_been_successfully_populated = false # sale_listing.has_been_successfully_populated?
        unless has_been_successfully_populated
          target_sale_listing.realty_asset.update(
            property_hash['asset_data'].except('related_urls_from_otm', 'related_urls')
          )
          target_sale_listing.realty_asset.update(
            related_urls_from_otm: property_hash['asset_data']['related_urls_from_otm']
          )
          target_sale_listing.update(property_hash['listing_data'])
          # title and listing don't seem to have been updated
          # tidy_up_listing(sale_listing)
          photo_urls_with_description = []

          property_hash['listing_image_urls'].each do |img|
            photo_urls_with_description << {
              url: img,
              description: ''
            }
          end
          # photo_urls = property_hash['listing_image_urls']

          image_creator_result = create_and_update_asset_photos(
            photo_urls_with_description, target_sale_listing.realty_asset,
            target_sale_listing
          )
          puts "number of photos created by FLAAC: #{image_creator_result.count}"

          target_sale_listing.save!
          # target_sale_listing.related_urls = property_hash['related_urls']
        end
        target_sale_listing
      end

      def create_from_resales_hash(property_hash, request_host)
        # 18 june 2024 - perhaps should have something in the
        # name to indicate that sale_listings are uniquely
        # identified by import_url....
        import_url = property_hash['import_url'] || "https://costaspecialist.nl/en/#{property_hash['reference']}"
        creator = Creators::Base::BaseListingAndAssetCreator.new
        sale_listing = creator.find_or_create_by_url(
          import_url, request_host
        )

        has_been_successfully_populated = false # sale_listing.has_been_successfully_populated?

        unless has_been_successfully_populated
          listing_updater = Updaters::ResalesListingUpdater.new
          # will update listing
          updated_sale_listing = listing_updater.update_listing_from_hash(
            property_hash, sale_listing
          )
          # tidy_up_listing(sale_listing)
          photo_urls_with_description = property_hash['listing_images_with_descriptions']
          # photo_urls = property_hash['listing_image_urls']
          image_creator_result = create_and_update_asset_photos(
            photo_urls_with_description, updated_sale_listing.realty_asset,
            updated_sale_listing
          )
          puts "number of photos created by FLAAC: #{image_creator_result.count}"

          updated_sale_listing.save!
        end
        sale_listing
      end

      def create_and_update_data_only(property_hash, request_host)
        import_url = property_hash['import_url'] || "https://costaspecialist.nl/en/#{property_hash['reference']}"
        creator = Creators::Base::BaseListingAndAssetCreator.new
        sale_listing = creator.find_or_create_by_url(
          import_url, request_host
        )

        has_been_successfully_populated = false # sale_listing.has_been_successfully_populated?

        unless has_been_successfully_populated
          listing_updater = Updaters::ResalesListingUpdater.new
          # will update listing
          updated_sale_listing = listing_updater.update_listing_from_hash(
            property_hash, sale_listing
          )

          # photo_urls_with_description = property_hash['listing_images_with_descriptions']
          # image_creator_result = create_and_update_asset_photos(
          #   photo_urls_with_description, updated_sale_listing.realty_asset,
          #   updated_sale_listing
          # )
          # puts "number of photos created by FLAAC: #{image_creator_result.count}"

          updated_sale_listing.save!
        end
        sale_listing
      end

      def create_from_url(import_url, request_host)
        # Will overwrite existing listing if it exists
        # Won't be such a problem when I add concept of derived_listing..
        creator = Creators::Base::BaseListingAndAssetCreator.new
        sale_listing = creator.find_or_create_by_url(
          import_url,
          request_host
        )

        unless sale_listing.has_been_successfully_populated?
          scrape_model_loader = DataLoaders::ScrapeModelLoader.new
          scrape_model_result = scrape_model_loader.load_from_url(import_url, sale_listing.uuid)
          if scrape_model_result[:success]
            property_hash = scrape_model_result[:property_hash]
          else
            # TODO: log error
          end

          listing_updater = Updaters::ListingAndAssetUpdater.new
          # will update listing
          listing_updater.update_listing_from_hash(property_hash, sale_listing)
          # above is a bit silly as it returns the listing that is passed in

          # tidy_up_listing(sale_listing)
        end
        sale_listing
      end

      def create_from_html(import_url, request_host, import_html = nil)
        # Will overwrite existing listing if it exists
        # Won't be such a problem when I add concept of derived_listing..
        creator = Creators::Base::BaseListingAndAssetCreator.new
        sale_listing = creator.find_or_create_by_url(
          import_url,
          request_host
        )

        scrape_model_loader = DataLoaders::ScrapeModelLoader.new
        scrape_model_result = scrape_model_loader.load_from_html(import_url, import_html, sale_listing.uuid)
        if scrape_model_result[:success]
          property_hash = scrape_model_result[:property_hash]
        else
          # TODO: log error
        end

        listing_updater = Updaters::ListingAndAssetUpdater.new
        # will update listing
        listing_updater.update_listing_from_hash(property_hash, sale_listing)
        # above is a bit silly as it returns the listing that is passed in

        # TODO: - use Resonad
        # result_r_object = Resonad.Success({
        #   realty_asset: sale_listing.realty_asset,
        # })

        # tidy_up_listing(sale_listing)
        sale_listing
      end

      private

      def create_and_update_asset_photos(photo_urls_with_description, realty_asset, sale_listing)
        puts "photos to create count: #{photo_urls_with_description.count}"
        image_creator_result = Creators::ListingsPhotoCreator.new(
          photo_urls_with_description:,
          realty_asset:,
          sale_listing:,
          save_images_locally:
          # max_photos_to_process:
        ).create_and_update_photos
        # puts "number of photos created by FLAAC: #{image_creator_result.count}"
        image_creator_result.each do |new_realty_asset_photo|
          new_realty_asset_photo.resize_image
        end
        image_creator_result
      end
    end
  end
end
