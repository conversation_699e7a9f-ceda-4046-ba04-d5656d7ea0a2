require 'fastimage'

module Creators
  # app/services/photo_creation_service.rb
  class GenericPropertyPhotoCreator
    attr_reader :photo_urls, :photo_urls_with_description, :generic_property,
                :max_photos_to_process, :folder_name, :save_images_locally

    def initialize(
      generic_property:, photo_urls: nil,
      photo_urls_with_description: nil,
      max_photos_to_process: 25, folder_name: 'generic-props-photos',
      save_images_locally: false
    )
      @photo_urls = photo_urls
      @photo_urls_with_description = photo_urls_with_description
      @generic_property = generic_property
      @max_photos_to_process = max_photos_to_process
      @folder_name = folder_name
      @save_images_locally = save_images_locally
    end

    def create_and_update_photos
      photos = create_from_hash_with_urls
      associate_photos_with_property(photos)
      photos
    end

    private

    def create_from_hash_with_urls
      photos = []

      urls_to_process.each do |photo_data|
        break if photos.length >= max_photos_to_process

        photo_url = photo_data[:url]

        next if generic_property&.generic_property_photos&.find_by_remote_photo_url(photo_url)

        # This part can take a minute or more...
        photo = if save_images_locally
                  create_uploaded_photo(photo_data)
                else
                  create_external_photo(photo_data)
                end

        photos << photo if photo
      end
      photos
    end

    def create_uploaded_photo(photo_data)
      photo_data = photo_data.symbolize_keys
      photo_url = photo_data[:url]
      photo_title = photo_data[:title]
      description = photo_data[:description]
      gp_photo_gen_prompt = photo_data[:image_prompt] || ''
      fastimage = FastImage.new(photo_url)
      content_length = fastimage.content_length || 0
      width, height = fastimage.size || [0, 0]
      photo_slug = photo_data[:full_photo_slug]
      photo_filename = photo_slug || File.basename(photo_url)
      keyword_tags = photo_data[:keyword_tags] || []
      room_features = photo_data[:room_features] || []
      gp_photo_tags = keyword_tags + room_features

      # Skip small images
      unless content_length >= 10_000
        puts "Skipping small image: #{photo_url}"
        return
      end

      # Fetch the image from the remote URL
      file = URI.open(photo_url)

      GenericPropertyPhoto.create(
        details: photo_data,
        gp_photo_tags:,
        file_size: content_length,
        width:,
        height:,
        content_type: fastimage.type,
        gp_photo_gen_prompt:,
        gp_photo_title: photo_title,
        gp_photo_slug: photo_slug,
        gp_photo_description: description
      ).tap do |photo|
        photo.generic_property_image.attach(io: file, filename: photo_filename, content_type: fastimage.type)
      end
    rescue OpenURI::HTTPError, StandardError => e
      puts "Error creating photo from URL #{photo_url}: #{e.message}"
      Rails.logger.error "Error creating photo from URL #{photo_url}: #{e.message}"
      nil
    end

    def create_external_photo(photo_data)
      photo_url = photo_data[:url]
      description = photo_data[:description]

      GenericPropertyPhoto.create.tap do |photo|
        photo.remote_photo_url = photo_url
        photo.external_img_details[:remote_image] = remote_image_details(photo_url)
        photo.gp_photo_slug = photo_url
        photo.gp_photo_description = description
        photo.save!
      rescue StandardError => e
        puts "Error creating photo: #{e}"
        photo&.destroy
        nil
      end
    end

    def remote_image_details(url)
      {
        'url' => url,
        'small_fill' => { 'url' => url },
        'small_fit' => { 'url' => url }
      }
    end

    def associate_photos_with_property(photos)
      photos.each do |photo|
        photo.assign_attributes(
          generic_property_uuid: generic_property.uuid,
          folder: folder_name
          # generic_property_section_uuid: generic_property_section&.uuid
        )
        photo.save!
      end
    end

    def urls_to_process
      utp = if photo_urls_with_description.present?
              photo_urls_with_description
            elsif photo_urls.present?
              photo_urls.map { |url| { url:, description: nil } }
            else
              []
            end

      utp.each do |photo_data|
        photo_data[:url] = photo_data[:url].strip
        photo_data[:photo_title] = photo_data[:title] || photo_data[:photo_title] || photo_data[:description] || photo_data[:url]
      end
      utp
    end
  end
end
