module Creators
  class GenericPropertyCreator
    attr_accessor :save_images_locally

    def initialize(save_images_locally: false)
      @save_images_locally = save_images_locally
    end

    def create_and_update_data_only(property_hash: nil)
      generic_property_reference = property_hash['generic_property_reference']

      # Find or initialize the generic property based on the slug
      generic_property = GenericProperty.find_or_initialize_by(
        generic_property_reference:
      )

      # Update attributes from the property hash
      generic_property.assign_attributes(
        generic_property_title: property_hash['title'],
        generic_property_description: property_hash['description'],
        city: property_hash.dig('location', 'city'),
        country: property_hash.dig('location', 'country'),
        street_name: property_hash['street_address'],
        postal_code: property_hash['postal_code'],
        latitude: property_hash['latitude'],
        longitude: property_hash['longitude'],
        # property_type: property_hash['property_type'],
        # property_style: property_hash['property_style'],
        year_construction: property_hash['year_construction'],
        plot_area: property_hash['plot_area'],
        constructed_area: property_hash['constructed_area'],
        # constructed_area_sq_mt: property_hash['constructed_area_sq_mt'],
        # constructed_area_sq_ft: property_hash['constructed_area_sq_ft'],
        floor_of_flat: property_hash['floor_of_flat'],
        floors_in_building: property_hash['floors_in_building'],
        furnished: property_hash['furnished'],
        energy_performance: property_hash['energy_performance'],
        energy_rating: property_hash['energy_rating'],
        # generic_property_slug:,
        generic_property_tags: property_hash['property_tags'] || [],
        generic_property_currency: property_hash['price_sale_current_currency'] || 'EUR',
        generic_property_reference: property_hash['generic_property_reference'],
        is_ai_generated_gp: property_hash['is_ai_generated'] || true,
        map_url: property_hash['map_url'],
        primary_neighborhood: property_hash['primary_neighborhood'],
        region: property_hash['region'],
        province: property_hash['province'],
        translations: property_hash['translations'] || {},
        usefulness_rating: property_hash['usefulness_rating'],
        street_number: property_hash['street_number'],
        site_visitor_token: property_hash['visitor_token'],
        uuid: property_hash['uuid']
      )

      # Monetize fields that involve currency (converting to cents)
      generic_property.high_sale_price_cents = property_hash['high_sale_price'] * 100 if property_hash['high_sale_price']
      generic_property.low_sale_price_cents = property_hash['low_sale_price'] * 100 if property_hash['low_sale_price']
      generic_property.highest_sale_price_cents = property_hash['highest_sale_price'] * 100 if property_hash['highest_sale_price']
      generic_property.lowest_sale_price_cents = property_hash['lowest_sale_price'] * 100 if property_hash['lowest_sale_price']
      generic_property.likely_service_charge_yearly_cents = property_hash['likely_service_charge_yearly'] * 100 if property_hash['likely_service_charge_yearly']
      generic_property.potential_rental_monthly_cents = property_hash['potential_rental_monthly'] * 100 if property_hash['potential_rental_monthly']
      generic_property.potential_rental_daily_cents = property_hash['potential_rental_daily'] * 100 if property_hash['potential_rental_daily']
      generic_property.reasonable_sale_price_cents = property_hash['reasonable_sale_price'] * 100 if property_hash['reasonable_sale_price']

      # Save the generic property
      generic_property.save!

      # Handle associated rooms (generic_property_sections)
      property_hash['rooms']&.each do |room_data|
        section_slug = room_data['slug']
        section = generic_property.generic_property_sections.find_or_initialize_by(
          gp_section_slug: section_slug
        )
        room_type_map = {
          'bedroom' => :bedroom,
          'living room' => :living_room,
          'kitchen' => :kitchen,
          'bathroom' => :bathroom,
          'office' => :office,
          'storage' => :storage
        }
        gp_section_type = room_type_map[room_data['room_type']] || 0 # Map to enum key
        section.assign_attributes(
          gp_section_title: room_data['title'],
          gp_section_description: room_data['description'],
          gp_section_type:, # Map to enum key
          # gp_section_type: room_data['room_type'], # Updated key to match `gp_section_type`
          gp_section_area_sq_mt: room_data['room_size'], # Ensure `room_size` is provided in square meters
          gp_section_tags: room_data['keyword_tags'],
          gp_section_details: { # Updated to match `gp_section_details` JSONB attribute
            features: room_data['room_features']
          }
        )
        section.save!
      end
      generic_property
      # Handle associated photos (generic_property_photos)
      # return unless property_hash['photos']

      # property_hash['photos'].each_with_index do |photo_data, index|
      #   photo = generic_property.generic_property_photos.find_or_initialize_by(gp_photo_reference: photo_data['photo_reference'])

      #   photo.assign_attributes(
      #     gp_photo_title: photo_data['title'],
      #     gp_photo_description: photo_data['description'],
      #     is_ai_generated_photo: photo_data['is_ai_generated'],
      #     folder: photo_data['folder'] || 'generic-props-photos',
      #     file_size: photo_data['file_size'],
      #     process_options: photo_data['process_options'] || {},
      #     height: photo_data['height'],
      #     width: photo_data['width'],
      #     content_type: photo_data['content_type'],
      #     remote_photo_url: photo_data['remote_url'],
      #     gp_photo_slug: photo_data['slug'],
      #     gp_photo_flags: photo_data['flags'] || 0,
      #     gp_photo_tags: photo_data['tags'] || [],
      #     user_uuid: photo_data['user_uuid'],
      #     agency_tenant_uuid: photo_data['agency_tenant_uuid'],
      #     site_visitor_token: photo_data['visitor_token'],
      #     llm_interaction_uuid: photo_data['llm_interaction_uuid'],
      #     gp_photo_position_in_list: index + 1 # Ensure the position is ordered
      #   )

      #   photo.save!
      # end
    end

    # def create_and_update_data_only(property_hash)
    #   generic_property_slug = property_hash['import_url'] || "https://costaspecialist.nl/en/#{property_hash['reference']}"
    #   # creator = Creators::Base::BaseListingAndAssetCreator.new
    #   GenericProperty.find_or_create_by(
    #     generic_property_slug:
    #   )
    # end

    # def create_and_update_asset_photos(photo_urls_with_description, realty_asset, generic_property)
    #   puts "photos to create count: #{photo_urls_with_description.count}"
    #   image_creator_result = Creators::ListingsPhotoCreator.new(
    #     photo_urls_with_description:,
    #     realty_asset:,
    #     generic_property:,
    #     save_images_locally:
    #     # max_photos_to_process:
    #   ).create_and_update_photos
    #   # puts "number of photos created by FLAAC: #{image_creator_result.count}"
    #   image_creator_result.each do |new_realty_asset_photo|
    #     new_realty_asset_photo.resize_image
    #   end
    #   image_creator_result
    # end
  end
end
