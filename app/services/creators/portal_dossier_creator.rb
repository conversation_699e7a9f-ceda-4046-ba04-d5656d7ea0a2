# frozen_string_literal: true

# The Creators module encapsulates classes responsible for creating various entities.
module Creators
  # PortalDossierCreator is responsible for creating or updating RealtyDossier records
  # from property listing URLs from various real estate portals.
  # It handles fetching data from the portal, parsing it, and creating/updating
  # the necessary database records (RealtyDossier, SaleListing, DossierAsset).
  class PortalDossierCreator
    # PORTAL_CONFIG defines the specific configurations for each supported real estate portal.
    # Each portal entry includes:
    #   :scrape_class - The class responsible for scraping data from the portal.
    #   :connector - The connector class/method to use for making HTTP requests.
    #   :method - The specific method on the :scrape_class to invoke for finding or creating the scrape item.
    #   :include_trailing_slash - A boolean indicating whether URLs for this portal should have a trailing slash.
    PORTAL_CONFIG = {
      'purplebricks' => {
        scrape_class: 'ScrapeItemFromPurplebricks', connector: 'ScraperConnectors::Purplebricks',
        method: :find_or_create_for_h2c_purplebricks,
        include_trailing_slash: false
      },
      'onthemarket' => {
        scrape_class: 'ScrapeItemFromOtm', connector: 'ScraperConnectors::Regular',
        method: :find_or_create_for_h2c_onthemarket,
        include_trailing_slash: true
      },
      'zoopla' => {
        scrape_class: 'ScrapeItem', connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c,
        include_trailing_slash: false
      },
      'rightmove' => {
        scrape_class: 'ScrapeItemFromRightmove', connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_rightmove,
        include_trailing_slash: false
      },
      'buenavista' => {
        scrape_class: 'ScrapeItemFromBuenavista', connector: 'ScraperConnectors::Json',
        method: :find_or_create_for_h2c_buenavista,
        include_trailing_slash: false
      },
      'idealista' => {
        scrape_class: 'ScrapeItemFromIdealista', connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_idealista,
        include_trailing_slash: true
      },
      'zillow' => {
        scrape_class: 'ScrapeItemFromZillow', connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_zillow,
        include_trailing_slash: false
      },
      'redfin' => {
        scrape_class: 'ScrapeItemFromRedfin', connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_redfin,
        include_trailing_slash: false
      }
    }.freeze

    # Custom error class for when an unsupported portal is specified.
    class UnknownPortalError < StandardError; end

    # Creates a RealtyDossier from a given URL and portal name if it doesn't exist
    # or if it doesn't have a primary sale listing.
    # This method is intended for initial creation or completing partially created dossiers.
    #
    # @param url [String] The URL of the property listing.
    # @param portal [String] The name of the portal (e.g., 'zoopla', 'rightmove').
    # @return [RealtyDossier] The created or found RealtyDossier instance.
    # @raise [UnknownPortalError] if the portal is not configured.
    # @raise [StandardError] if any other error occurs during the process.
    def create_from_url(url, portal)
      puts "[PortalDossierCreator#create_from_url] Attempting to create dossier from URL: #{url}, Portal: #{portal}"

      validate_portal(portal)
      puts "[PortalDossierCreator#create_from_url] Portal '#{portal}' validated."

      dossier = initialize_dossier(url) # Finds or creates RealtyDossier
      puts "[PortalDossierCreator#create_from_url] Dossier initialized/found. ID: #{dossier.id}, UUID: #{dossier.uuid}"

      # If the dossier already has a primary sale listing, assume it's processed.
      # For forced updates, use `update_from_url`.
      if dossier.primary_sale_listing.present? # Assumes RealtyDossier has this association
        puts "[PortalDossierCreator#create_from_url] Dossier ID #{dossier.id} already has a primary sale listing. ID: #{begin
          dossier.primary_sale_listing.id
        rescue StandardError
          'N/A'
        end}. No action taken."
        return dossier
      end

      puts "[PortalDossierCreator#create_from_url] Dossier ID #{dossier.id} needs processing. Proceeding to sync listing data."
      _sync_listing_data(dossier, url, portal, force_retrieval: false) # `force_retrieval: false` uses cached scrape content if available

      puts "[PortalDossierCreator#create_from_url] Successfully created/processed dossier. ID: #{dossier.id}"
      dossier
    rescue StandardError => e
      error_message = "[PortalDossierCreator#create_from_url] Failed for #{portal} URL '#{url}'. Error: #{e.class} - #{e.message}"
      puts error_message
      Rails.logger.error(error_message)
      Rails.logger.error(e.backtrace.join("\n"))
      raise
    end

    # Updates an existing RealtyDossier from a given URL and portal name.
    # This method will re-scrape the data and update the dossier's primary sale listing.
    # If the dossier does not exist, it will log a message and return nil.
    #
    # @param url [String] The URL of the property listing.
    # @param portal [String] The name of the portal (e.g., 'zoopla', 'rightmove').
    # @return [RealtyDossier, nil] The updated RealtyDossier instance, or nil if not found.
    # @raise [UnknownPortalError] if the portal is not configured.
    # @raise [StandardError] if any other error occurs during the process.
    def update_from_url(url, portal)
      puts "[PortalDossierCreator#update_from_url] Attempting to update dossier from URL: #{url}, Portal: #{portal}"

      validate_portal(portal)
      puts "[PortalDossierCreator#update_from_url] Portal '#{portal}' validated."

      dossier = RealtyDossier.find_by(dossier_starting_url: url)

      unless dossier
        puts "[PortalDossierCreator#update_from_url] Dossier not found for URL: #{url}. Cannot update."
        return nil
      end

      puts "[PortalDossierCreator#update_from_url] Found existing Dossier ID: #{dossier.id} (UUID: #{dossier.uuid}) for update."
      _sync_listing_data(dossier, url, portal, force_retrieval: true) # `force_retrieval: true` ensures fresh data

      puts "[PortalDossierCreator#update_from_url] Successfully updated dossier. ID: #{dossier.id}"
      dossier
    rescue StandardError => e
      error_message = "[PortalDossierCreator#update_from_url] Failed for #{portal} URL '#{url}'. Error: #{e.class} - #{e.message}"
      puts error_message
      Rails.logger.error(error_message)
      Rails.logger.error(e.backtrace.join("\n"))
      raise
    end

    private

    # Validates if the given portal name is present in the PORTAL_CONFIG.
    # @param portal [String] The name of the portal.
    # @raise [UnknownPortalError] if the portal is not found in PORTAL_CONFIG.
    def validate_portal(portal)
      puts "[PortalDossierCreator] Validating portal: #{portal}"
      unless PORTAL_CONFIG.key?(portal)
        puts "[PortalDossierCreator] ERROR: Unknown portal: #{portal}"
        raise UnknownPortalError, "Unknown portal: #{portal}"
      end
      puts "[PortalDossierCreator] Portal '#{portal}' is known."
    end

    # Finds an existing RealtyDossier by its starting URL or creates a new one.
    # Ensures that the dossier has a UUID.
    # @param url [String] The dossier_starting_url.
    # @return [RealtyDossier] The found or created RealtyDossier instance.
    def initialize_dossier(url)
      puts "[PortalDossierCreator] Initializing dossier for URL: #{url}"
      dossier = RealtyDossier.find_or_create_by(dossier_starting_url: url) do |_d|
        puts "[PortalDossierCreator] Creating new RealtyDossier for URL: #{url}"
      end
      puts "[PortalDossierCreator] Found existing RealtyDossier with ID: #{dossier.id}" if dossier.persisted? && !dossier.previous_changes.key?('id') # Check if it was found, not just created

      if dossier.uuid.nil?
        puts "[PortalDossierCreator] Dossier ID #{dossier.id} has no UUID. Assigning a new one."
        dossier.update!(uuid: SecureRandom.uuid)
        puts "[PortalDossierCreator] Dossier ID #{dossier.id} updated with UUID: #{dossier.uuid}"
      else
        puts "[PortalDossierCreator] Dossier ID #{dossier.id} already has UUID: #{dossier.uuid}"
      end
      dossier
    end

    # Core logic to scrape data, create/update SaleListing, and manage DossierAsset.
    # @param dossier [RealtyDossier] The dossier to process.
    # @param url [String] The URL to scrape.
    # @param portal [String] The name of the portal.
    # @param force_retrieval [Boolean] Whether to force fresh scraping of content.
    # @return [RealtyDossier] The processed dossier.
    def _sync_listing_data(dossier, url, portal, force_retrieval:)
      puts "[PortalDossierCreator#_sync_listing_data] Syncing for Dossier ID: #{dossier.id}, URL: #{url}, Portal: #{portal}, Force Retrieval: #{force_retrieval}"

      p_config = PORTAL_CONFIG[portal]
      puts "[PortalDossierCreator#_sync_listing_data] Using portal config: #{p_config.inspect}"

      scrape_class_name = p_config[:scrape_class]
      scrape_class = scrape_class_name.constantize
      puts "[PortalDossierCreator#_sync_listing_data] Scraper class: #{scrape_class_name}"

      scrape_method = p_config[:method]
      puts "[PortalDossierCreator#_sync_listing_data] Calling scrape method: #{scrape_class_name}.#{scrape_method} with URL: #{url}"
      scrape_item = scrape_class.send(scrape_method, url) # Finds or creates ScrapeItem
      puts "[PortalDossierCreator#_sync_listing_data] ScrapeItem obtained/created. ID: #{begin
        scrape_item.id
      rescue StandardError
        'N/A'
      end}"

      puts "[PortalDossierCreator#_sync_listing_data] Retrieving content for ScrapeItem ID: #{begin
        scrape_item.id
      rescue StandardError
        'N/A'
      end} using connector: #{p_config[:connector]}, Force: #{force_retrieval}"
      scrape_result = scrape_item.retrieve_and_set_content_object(
        p_config[:connector],
        include_trailing_slash: p_config[:include_trailing_slash],
        force_retrieval: force_retrieval
      )
      puts "[PortalDossierCreator#_sync_listing_data] Content retrieval complete. Result length: #{scrape_result&.length || 0}"

      validate_scrape_result(scrape_result)
      puts '[PortalDossierCreator#_sync_listing_data] Scrape result validated.'

      puts "[PortalDossierCreator#_sync_listing_data] Creating/updating SaleListing from ScrapeItem ID: #{begin
        scrape_item.id
      rescue StandardError
        'N/A'
      end} for portal: #{portal}"
      # This method should ideally find_or_initialize a SaleListing by scrape_item_id and update it,
      # or create a new one if necessary. Its UUID might change if a new record is made.
      sale_listing = create_sale_listing(scrape_item, portal)
      puts "[PortalDossierCreator#_sync_listing_data] SaleListing created/updated. ID: #{begin
        sale_listing.id
      rescue StandardError
        'N/A'
      end}, UUID: #{begin
        sale_listing.uuid
      rescue StandardError
        'N/A'
      end}"

      _upsert_primary_dossier_asset(dossier, sale_listing)

      # Optional: Post-process photos associated with the dossier.
      # puts "[PortalDossierCreator#_sync_listing_data] Triggering post_parse_photos if uncommented."
      # dossier.post_parse_photos(portal: portal)

      dossier
    end

    # Validates the result of the scraping process.
    # @param result [String, nil] The scraped content.
    # @raise [RuntimeError] if the scrape_result is nil or too short.
    def validate_scrape_result(result)
      puts "[PortalDossierCreator] Validating scrape result. Length: #{result&.length || 0}"
      return if result && result.length > 1000 # Basic sanity check

      puts "[PortalDossierCreator] ERROR: Scrape result is unavailable or suspiciously short. Length: #{result&.length || 0}"
      Rails.logger.debug { "[PortalDossierCreator] Invalid scrape_result: #{result.inspect}" }
      raise 'scrape_result unavailable or suspiciously short'
    end

    # Creates or updates a SaleListing from a ScrapeItem.
    # Assumes this method handles idempotency for SaleListing based on ScrapeItem.
    # @param scrape_item [ScrapeItem] The scrape item containing the raw data.
    # @param portal [String] The name of the portal.
    # @return [SaleListing] The created or updated SaleListing instance.
    def create_sale_listing(scrape_item, portal)
      puts "[PortalDossierCreator] Determining SaleListing creation/update strategy for portal: #{portal}"
      if portal == 'zoopla'
        puts '[PortalDossierCreator] Using RealtyParsers::ParseListingViaLlm for Zoopla.'
        parser = RealtyParsers::ParseListingViaLlm.new
        puts "[PortalDossierCreator] Calling inferred_listing_from_scrape_item for ScrapeItem ID: #{begin
          scrape_item.id
        rescue StandardError
          'N/A'
        end}"
        # This method should ideally be idempotent or update existing based on scrape_item
        sale_listing = parser.inferred_listing_from_scrape_item(scrape_item)
      else
        puts "[PortalDossierCreator] Using scrape_item.sale_listing_from_scrape_item for portal: #{portal}."
        # This method should ideally be idempotent or update existing based on self (scrape_item)
        sale_listing = scrape_item.sale_listing_from_scrape_item
      end
      puts "[PortalDossierCreator] SaleListing obtained/processed. ID: #{begin
        sale_listing.id
      rescue StandardError
        'N/A'
      end}, UUID: #{begin
        sale_listing.uuid
      rescue StandardError
        'N/A'
      end}"
      sale_listing
    end

    # Creates or updates the primary DossierAsset for a dossier, ensuring only one primary asset exists.
    # @param dossier [RealtyDossier] The dossier to link.
    # @param new_sale_listing [SaleListing] The sale listing to be marked as primary.
    # @return [DossierAsset] The created or updated primary DossierAsset.
    def _upsert_primary_dossier_asset(dossier, new_sale_listing)
      puts "[PortalDossierCreator#_upsert_primary_dossier_asset] Upserting primary asset for Dossier UUID: #{dossier.uuid}, SaleListing UUID: #{begin
        new_sale_listing.uuid
      rescue StandardError
        'N/A'
      end}"

      # Define the attributes for the primary asset
      target_asset_attributes = {
        realty_asset_uuid: new_sale_listing.realty_asset.uuid, # Assumes sale_listing has realty_asset
        agency_tenant_uuid: new_sale_listing.agency_tenant_uuid,
        property_is_for_sale: true,
        is_most_comparable_to_primary: false,
        is_good_comparable_to_primary: false
        # is_primary_dossier_asset will be handled explicitly
      }

      # Find any current primary asset for this dossier
      # Assumes RealtyDossier has `has_many :dossier_assets`
      current_primary_asset = dossier.dossier_assets.find_by(is_primary_dossier_asset: true)

      if current_primary_asset
        # If the current primary asset links to the *same* sale listing, update it.
        if current_primary_asset.origin_sale_listing_uuid == new_sale_listing.uuid
          puts "[PortalDossierCreator#_upsert_primary_dossier_asset] Current primary asset (ID: #{current_primary_asset.id}) links to the same SaleListing. Updating attributes."
          current_primary_asset.update!(target_asset_attributes.merge(is_primary_dossier_asset: true)) # Ensure it remains primary
          puts "[PortalDossierCreator#_upsert_primary_dossier_asset] Primary DossierAsset ID: #{current_primary_asset.id} updated."
          return current_primary_asset
        else
          # If it links to a *different* sale listing, mark the old one as non-primary.
          puts "[PortalDossierCreator#_upsert_primary_dossier_asset] Current primary asset (ID: #{current_primary_asset.id}) links to a different SaleListing. Marking it non-primary."
          current_primary_asset.update!(is_primary_dossier_asset: false)
        end
      end

      # At this point, either there was no primary asset, or the old one was de-primarized.
      # Try to find if an asset already exists for this *new* sale_listing and make it primary.
      # Or, create a new one.
      asset_for_new_listing = dossier.dossier_assets.find_by(origin_sale_listing_uuid: new_sale_listing.uuid)

      if asset_for_new_listing
        puts "[PortalDossierCreator#_upsert_primary_dossier_asset] Found existing asset (ID: #{asset_for_new_listing.id}) for new SaleListing. Marking as primary and updating."
        asset_for_new_listing.update!(target_asset_attributes.merge(is_primary_dossier_asset: true))
        puts "[PortalDossierCreator#_upsert_primary_dossier_asset] DossierAsset ID: #{asset_for_new_listing.id} marked primary and updated."
        asset_for_new_listing
      else
        puts '[PortalDossierCreator#_upsert_primary_dossier_asset] Creating new DossierAsset and marking as primary.'
        new_asset = dossier.dossier_assets.create!(
          target_asset_attributes.merge(
            origin_sale_listing_uuid: new_sale_listing.uuid,
            is_primary_dossier_asset: true
          )
        )
        puts "[PortalDossierCreator#_upsert_primary_dossier_asset] New primary DossierAsset created. ID: #{new_asset.id}."
        new_asset
      end
    end
  end
end
