module Creators
  class FakeListingCreatorWithLangchain
    include Dry::Monads[:result, :do]

    PROMPT_TEMPLATE = <<~PROMPT.squish
      Generate a JSON object for a luxury property listing with these fields:
      title, description, price, bedrooms, bathrooms, plot_area, constructed_area,
      location (city, state, country), features (array), property_type
    PROMPT

    IMAGE_PROMPT_TEMPLATE = <<~PROMPT
      Generate a professional real estate photo prompt for a listing with the following details:
      Property type: %<property_type>s
      City: %<city>s
    PROMPT

    def initialize
      # @llm = Langchain::LLM::OpenAI.new(
      #   api_key: ENV['OPENAI_API_KEY'],
      #   model_name: 'gpt-3.5-turbo',
      #   temperature: 0.7
      # )
      @llm = Langchain::LLM::OpenAI.new(
        api_key: ENV['OPENAI_API_KEY'], default_options: {
          chat_completion_model_name: 'gpt-3.5-turbo'
          # chat_completion_model_name: ConfigH2c.general::SETTINGS_FOR_AI[:DefaultGptModelToUse]
        }
      )
    end

    def create
      property_data = yield generate_property_data
      debugger
      image_urls = yield generate_images(property_data)
      create_listing_with_images(property_data, image_urls)
    end

    private

    def generate_property_data
      response = @llm.chat(
        prompt: PROMPT_TEMPLATE,
        output_format: :json # Automatically parse JSON
      )

      return Failure(:no_content) if response.blank?

      Success(response)
    rescue Langchain::Errors::LLMError => e
      Rails.logger.error "Error generating property data: #{e.message}"
      Failure(:generation_error)
    end

    def generate_images(property_data)
      image_prompts = build_image_prompts(property_data)

      image_urls = image_prompts.map do |prompt|
        @llm.image(prompt:, size: '1024x1024', n: 1).dig(:data, 0, :url)
      end.compact

      image_urls.any? ? Success(image_urls) : Failure(:no_images_generated)
    rescue Langchain::Errors::LLMError => e
      Rails.logger.error "Error generating images: #{e.message}"
      Failure(:image_generation_error)
    end

    def create_listing_with_images(property_data, image_urls)
      property_data['listing_image_urls'] = image_urls
      property_data['reference'] = property_data['title'].parameterize
      property_data['import_url'] = "http://propertywebbuilder.com/fantasy-listings/#{SecureRandom.uuid}"

      request_host = 'fake_listing_creator_host'
      creator = Creators::Full::FullListingAndAssetCreator.new(save_images_locally: true)

      sale_listing = creator.create_from_resales_hash(property_data, request_host)

      export_listing(sale_listing)
      sale_listing
    end

    def build_image_prompts(property_data)
      property_type = property_data['property_type'].capitalize
      city = property_data.dig('location', 'city')

      [
        format(IMAGE_PROMPT_TEMPLATE, property_type:, city:),
        "Interior photo of a luxury #{property_type}, modern design, bright and spacious"
      ]
    end

    def export_listing(sale_listing)
      exporter = ExportersAndLoaders::SaleListingsExporter.new(
        destination_dir: Rails.root.join('db', 'exports', 'ai_faked_listings')
      )
      exporter.export([sale_listing])
    rescue StandardError => e
      Rails.logger.error "Error exporting listing: #{e.message}"
    end
  end
end
