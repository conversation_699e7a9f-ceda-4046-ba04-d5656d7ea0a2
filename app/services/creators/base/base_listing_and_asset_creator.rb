module Creators
  module Base
    class BaseListingAndAssetCreator
      # attr_accessor :target_page

      def initialize(target_page = nil)
        # if target_page.present?
        #   self.target_page = target_page
        #   # else
        #   #   raise ArgumentError, 'Please provide target_page' if self.target_page.blank?
        # end
      end

      def find_or_create_by_unique_url(import_url)
        canonical_unique_url = RealtyScrapers::SharedScraperHelpers.get_unique_h2c_url_from_url(import_url)
        target_listing = SaleListing.find_by({
                                               unique_url: canonical_unique_url
                                             })

        if target_listing.blank?
          target_listing = SaleListing.create!({
                                                 unique_url: canonical_unique_url
                                               })
          target_listing.reload
          # target_listing.host_on_create = request_host # request.host
        end

        if target_listing.realty_asset.blank?
          new_realty_asset = RealtyAsset.create!
          new_realty_asset.reload
          target_listing.realty_asset_uuid = new_realty_asset.uuid
        end
        target_listing.save!
        target_listing
      end

      # March 2025 - will eventually remove below when it is entirely
      # replaced by above

      def find_or_create_by_url(import_url, request_host)
        canonical_import_url = RealtyScrapers::SharedScraperHelpers.get_canonical_url_from_url(import_url)

        target_listing = SaleListing.find_by({
                                               import_url: canonical_import_url
                                             })

        if target_listing.blank?
          #   return target_listing
          # else
          target_listing = SaleListing.create!({
                                                 import_url: canonical_import_url
                                               })
          target_listing.reload
          # listing_uuid = SecureRandom.uuid
          # target_listing.import_url = import_url # params[:import_url_top]
          target_listing.host_on_create = request_host # request.host
          # Feb 2023 - got an error below with slug already taken when I tried
          # setting slug as listing_uuid
          # Will have to revisit the whole idea of a slug for a listing.
          # Especially as it is a mobility field which is really not great for use as a slug...
          # target_listing.slug = SecureRandom.uuid # listing_uuid
        end

        if target_listing.realty_asset.blank?
          new_realty_asset = RealtyAsset.create!
          new_realty_asset.reload
          target_listing.realty_asset_uuid = new_realty_asset.uuid
          # target_listing.save!
        end
        target_listing.save!
        target_listing
      end
    end
  end
end
