require 'fastimage'

module Creators
  # app/services/photo_creation_service.rb
  class ListingsPhotoCreator
    attr_reader :photo_urls, :photo_urls_with_description, :realty_asset, :sale_listing,
                :max_photos_to_process, :folder_name, :save_images_locally

    def initialize(
      realty_asset:, sale_listing:, photo_urls: nil,
      photo_urls_with_description: nil,
      max_photos_to_process: 25, folder_name: 'h2c-photos',
      save_images_locally: false
    )
      @photo_urls = photo_urls
      @photo_urls_with_description = photo_urls_with_description
      @realty_asset = realty_asset
      @sale_listing = sale_listing
      @max_photos_to_process = max_photos_to_process
      @folder_name = folder_name
      @save_images_locally = save_images_locally
    end

    def create_and_update_photos
      photos = create_from_hash_with_urls
      associate_photos_with_asset(photos)
      photos
    end

    private

    def create_from_hash_with_urls
      photos = []

      urls_to_process.each do |photo_data|
        break if photos.length >= max_photos_to_process

        photo_url = photo_data[:url]

        next if sale_listing&.listing_photos&.find_by_remote_photo_url(photo_url)

        # This part can take a minute or more....
        photo = if save_images_locally
                  create_uploaded_photo(photo_data)
                else
                  create_external_photo(photo_data)
                end

        photos << photo if photo
      end
      photos

      # result = urls_to_process.each_with_object([]) do |photo_data, photos|
      #   break photos if photos.length >= max_photos_to_process

      #   photo_url = photo_data[:url]
      #   next if sale_listing&.listing_photos&.find_by_remote_photo_url(photo_url)

      #   # This part can take a minute or more....
      #   photo = if save_images_locally
      #             create_uploaded_photo(photo_data)
      #           else
      #             create_external_photo(photo_data)
      #           end
      #   photos << photo if photo
      #   return photos
      # end
      # result
    end

    def create_uploaded_photo(photo_data)
      photo_data = photo_data.symbolize_keys
      photo_url = photo_data[:url]
      photo_title = photo_data[:title]
      description = photo_data[:description]
      photo_gen_prompt = photo_data[:image_prompt] || ''
      fastimage = FastImage.new(photo_url)
      content_length = fastimage.content_length || 0
      width, height = fastimage.size || [0, 0]
      photo_slug = photo_data[:full_photo_slug]
      photo_filename = photo_slug || File.basename(photo_url)
      keyword_tags = photo_data[:keyword_tags] || []
      room_features = photo_data[:room_features] || []
      realty_asset_photo_tags = keyword_tags + room_features
      # Skip small images
      unless content_length >= 10_000
        puts "Skipping small image: #{photo_url}"
        return
      end

      # Fetch the image from the remote URL
      file = URI.open(photo_url)

      new_r_photo = RealtyAssetPhoto.create(
        details: photo_data,
        # TODO: - rename above to extra_photo_details
        realty_asset_photo_tags:,
        file_size: content_length,
        width:,
        height:,
        content_type: fastimage.type,
        photo_gen_prompt:,
        photo_title:,
        photo_slug:,
        photo_description: description # Optionally store the description
      ).tap do |photo|
        # Attach the image file to the Active Storage association
        photo.realty_image.attach(io: file, filename: photo_filename, content_type: fastimage.type)
      end
      if photo_data[:llm_interaction_uuid].present?
        new_r_photo.update!(
          is_ai_generated_photo: true,
          llm_interaction_uuid: photo_data[:llm_interaction_uuid]
        )
      end
      # new_r_photo.update!(llm_interaction: photo_data[:llm_interaction]) if photo_data[:llm_interaction].present?
      new_r_photo
    rescue OpenURI::HTTPError, StandardError => e
      puts "Error creating photo from URL #{photo_url}: #{e.message}"
      Rails.logger.error "Error creating photo from URL #{photo_url}: #{e.message}"
      nil
    end

    def create_external_photo(photo_data)
      photo_url = photo_data[:url]
      description = photo_data[:description]

      RealtyAssetPhoto.create.tap do |photo|
        photo.remote_photo_url = photo_url
        photo.external_img_details[:remote_image] = remote_image_details(photo_url)
        photo.photo_slug = photo_url
        photo.photo_description = description # Optionally store the description
        photo.save!
      rescue StandardError => e
        puts "Error creating photo: #{e}"
        photo&.destroy
        nil
      end
    end

    def remote_image_details(url)
      {
        'url' => url,
        'small_fill' => { 'url' => url },
        'small_fit' => { 'url' => url }
      }
    end

    def associate_photos_with_asset(photos)
      photos.each do |photo|
        photo.assign_attributes(
          realty_asset_uuid: realty_asset.uuid,
          folder: folder_name,
          sale_listing_uuid: sale_listing&.uuid,
          visible_for_sale_listing: sale_listing.present?
        )
        photo.save!
      end
    end

    def urls_to_process
      utp = if photo_urls_with_description.present?
              photo_urls_with_description
            elsif photo_urls.present?
              photo_urls.map { |url| { url:, description: nil } }
            else
              []
            end

      utp.each do |photo_data|
        photo_data[:url] = photo_data[:url].strip
        photo_data[:photo_title] = photo_data[:title] || photo_data[:photo_title] || photo_data[:description] || photo_data[:url]
        # photo_data[:photo_slug] = photo_data[:photo_title].parameterize[0..24]
      end
      utp
    end
  end
end
