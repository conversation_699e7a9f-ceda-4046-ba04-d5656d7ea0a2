require 'fastimage'

module Creators
  class PhotoCreator
    def initialize(**keyword_args)
      # https://www.justinweiss.com/articles/fun-with-keyword-arguments/
    end

    def create_photo_from_url(listing_for_photo, photo_class, photo_url)
      # eg of photo_class: RealtyAssetPhoto
      # result_object = {
      #   success: false,
      #   error_message: "",
      #   photo: nil,
      # }
      result_object = Resonad.Failure('Error creating photo from url')
      # result.success? #=> true
      # result.failure? #=> false
      # result.value #=> 5
      # result.error #=> raises an exception

      photo_info = { photo_url: }
      # When creating photos from urls, can either upload them
      # photo = create_uploaded_photo(photo_info, photo_class)
      # Or can create remote photo:
      existing_photo = listing_for_photo.listing_photos.find_by_remote_photo_url(photo_url)
      # This check is to prevent duplication of images
      # if a scrape is re-run
      if existing_photo.present?
        result_object = Resonad.Failure('Photo already added')
      else
        begin
          result_object = create_remote_photo(photo_info, photo_class)
          return result_object unless result_object.success?

          photo = result_object.value
          # if photo.present?
          #   photos.push photo
          # else
          #   puts "Unable to create photo from #{photo_info}"
          # end

          photo.realty_asset_uuid = listing_for_photo.realty_asset.uuid
          # photo.folder = folder_name
          if listing_for_photo.listings_model_name == 'sale_listings'
            # if sale_listing.uuid
            photo.sale_listing_uuid = listing_for_photo.uuid
            photo.visible_for_sale_listing = true
          end
          # if rental_listing_uuid
          #   photo.rental_listing_uuid = rental_listing_uuid
          #   photo.visible_for_rental_listing = true
          # end
          # photo.sale_listing_uuid = realty_asset.sale_listings.first.uuid
          # photo.rental_listing_uuid = realty_asset.rental_listings.first.uuid
          # photo.visible_for_rental_listing = true
          photo.save!
        rescue Exception => e
          # raise e
          result_object = Resonad.Failure(e.message)
          # return result_object
        end
      end
      result_object
    end

    # def call_update_asset_photos(listing_hash, realty_asset, sale_listing)
    #   stringified_listing_hash = listing_hash.stringify_keys
    #   update_asset_photos_local stringified_listing_hash, realty_asset, sale_listing
    # end

    private

    def create_remote_photo(photo_info, photo_class)
      # Will set the remote_photo_url on the photo_class without
      # actualy downloading the image.
      # Might extract as a helper class
      photo = nil
      if photo_info[:photo_url]
        # June 2022 - This was stupid - prevented photo with
        # given url existing for any other sale listing
        # photo = photo_class.send("find_by_remote_photo_url", photo_info[:photo_url])
        # if photo.present?
        #   return photo
        # end

        fastimage = FastImage.new(photo_info[:photo_url])
        fastimage_content_length = fastimage.content_length || 0
        fastimage_width = fastimage.size ? fastimage.size[0] : 0
        fastimage_height = fastimage.size ? fastimage.size[1] : 0
        if fastimage_content_length < 10_000
          result_object = if fastimage_content_length == 0
                            Resonad.Failure('Photo url is not valid')
                          else
                            # https://www.zillowstatic.com/s3/hdp/home-details/images/video-tile-placeholder.jpg
                            # above is 57737 so perhaps should increase acceptable content length
                            Resonad.Failure("#{photo_info[:photo_url]} is too small at  #{fastimage_content_length}")
                          end
          return result_object
        else
          puts "#{photo_info[:photo_url]} is okay at  #{fastimage_content_length}"
        end

        begin
          photo = photo_class.send('create')
          # if photo_info[:photo_url]
          # photo.remote_image_url = photo_info[:photo_url]
          # Jan 2022 - remote_photo_url is a field I added to model
          # - not to be confused with remote_image_url which will
          # help save a remote image locally
          photo.remote_photo_url = photo_info[:photo_url]
          photo.details[:remote_image] = {
            "url": photo_info[:photo_url],
            "small_fill": {
              "url": photo_info[:photo_url]
            },
            "small_fit": {
              "url": photo_info[:photo_url]
            }
          }
          photo.photo_slug = photo_info[:photo_url]
          # below useful as a clue to how decent an image I might have
          photo.file_size = fastimage_content_length
          photo.width = fastimage_width
          photo.height = fastimage_height
          photo.content_type = fastimage.type

          # I also have a description and translations field I should use in future
          photo.save!
        rescue Exception => e
          result_object = Resonad.Failure("Error creating photo in PhotoCreator: #{e}")
          photo.destroy! if photo
          raise e
        end
      end
      Resonad.Success(photo)
      # result_object[:photo] = photo
    end
  end
end
