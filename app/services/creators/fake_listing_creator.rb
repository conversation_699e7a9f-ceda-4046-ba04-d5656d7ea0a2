module Creators
  class FakeListingCreator
    include Dry::Monads[:result, :do]

    def initialize(
      client: nil,
      listing_prompt_generator: PromptGenerators::FakeListingRegular.new,
      photo_prompt_generator: PromptGenerators::FakeListingPhotoRegular.new
    )
      #  OpenAI::Client.new(access_token: Rails.application.credentials.config[:openai_api_key]
      # client ||= Ai::Clients::OpenAiClient.new(
      #   api_key: Rails.application.credentials.config[:openai_api_key]
      # )

      client ||= Ai::Clients::ReplicateClient.new(
        api_key: Rails.application.credentials.config[:openai_api_key]
      )

      @llm_service = Ai::LlmInteractionService.new(
        client:, model_name: 'gpt-4o-mini', temperature: 0.7
        # using below was more expensive and resulted in unparsable json
        # client:, model_name: 'gpt-4', temperature: 0.9
      )
      @listing_prompt_generator = listing_prompt_generator
      @photo_prompt_generator = photo_prompt_generator

      #       client = Ai::Clients::OpenAiClient.new(api_key: Rails.application.credentials.config[:openai_api_key])
    end

    def create_listing_data_only(data_for_prompt: nil)
      # property_data = yield @llm_service.generate_property_data(@listing_prompt_generator.property_data_prompt)
      result = @llm_service.generate_property_data(@listing_prompt_generator.property_data_prompt)
      case result
      when Dry::Monads::Success
        property_data, llm_interaction = result.value!
        # Handle successful response
      when Dry::Monads::Failure

        error_symbol, llm_interaction = result.failure
        puts "error_symbol: #{error_symbol}"
        # Handle failure and log interaction details
      end
      puts "llm_interaction: #{llm_interaction}"
      skinny_sale_listing = create_listing_only(property_data)
      update_sale_listing_with_prompt(skinny_sale_listing, property_data)
      skinny_sale_listing.update!(
        llm_interaction:,
        listing_slug: data_for_prompt['sale_listing_slug'],
        currency: data_for_prompt['currency'],
        is_ai_generated_listing: true
      )
      skinny_sale_listing
    end

    def create_images_only(sale_listing)
      # prompts only:
      prompts = @photo_prompt_generator.image_prompts_from_sale_listing(sale_listing)
      # Where LLM is called and LLM urls returned
      photo_urls_with_description = yield generate_images_for_sale_listing(
        prompts, sale_listing, max_photos: 2
      )
      # Where the images are actually created and saved
      update_sale_listing_with_images(sale_listing, photo_urls_with_description)
      sale_listing
    end

    private

    def generate_images_for_sale_listing(prompts, sale_listing, max_photos: nil)
      llm_interaction = nil
      # Limit the number of prompts if max_photos is specified
      limited_prompts = max_photos ? prompts.first(max_photos) : prompts

      results = limited_prompts.map do |prompt_hash|
        prompt_hash = prompt_hash.symbolize_keys

        existing_photo = sale_listing.listing_photos.find_by(
          photo_slug: prompt_hash[:full_photo_slug]
        )
        if existing_photo.present?
          puts "existing_photo: #{existing_photo.photo_slug} - skipping"
          next nil
        end
        prompt = prompt_hash[:image_prompt]

        llm_service_result = @llm_service.fetch_image_url(prompt)

        case llm_service_result
        when Dry::Monads::Success
          image_url, llm_interaction = llm_service_result.value!
          # Handle successful response
        when Dry::Monads::Failure
          error_symbol, llm_interaction = llm_service_result.failure
          puts "error_symbol: #{error_symbol}"
          # Handle failure and log interaction details
        end

        # description = prompt_hash[:image_description] || 'Img desc'
        next nil if image_url.blank?

        prompt_hash[:url] = image_url
        prompt_hash[:llm_interaction_uuid] = llm_interaction.uuid
        prompt_hash

        # { url: image_url, photo_gen_prompt: prompt,
        #   llm_interaction_uuid: llm_interaction.uuid,
        #   description: }
      end.compact

      puts llm_interaction
      results.any? ? Success(results) : Failure(:no_images_generated)
    end

    # Other private methods remain unchanged

    def update_sale_listing_with_images(sale_listing, photo_urls_with_description)
      puts "photos to create count: #{photo_urls_with_description.count}"
      image_creator = Creators::ListingsPhotoCreator.new(
        photo_urls_with_description:,
        realty_asset: sale_listing.realty_asset,
        sale_listing:,
        save_images_locally: true
        # max_photos_to_process:
      )
      asset_photos_returned = image_creator.create_and_update_photos
      # puts "number of photos created by FLAAC: #{asset_photos_returned.count}"
      asset_photos_returned.each do |new_realty_asset_photo|
        new_realty_asset_photo.resize_image
      end

      puts "number of photos created by fake listing creator: #{asset_photos_returned.count}"

      sale_listings = [sale_listing]
      exporter = ExportersAndLoaders::SaleListingsExporter.new(
        destination_dir: Rails.root.join('db', 'exports', 'ai_faked_listings')
      )
      exporter.export(sale_listings)
    end

    def update_sale_listing_with_prompt(sale_listing, property_data)
      section_descriptions = property_data.slice(
        'main_bedroom_description',
        'main_bathroom_description',
        'main_study_area_description',
        'main_utility_room_description',
        'main_kitchen_description',
        'main_living_room_description',
        'main_dining_room_description',
        'main_exterior_description',
        'main_garden_description',
        'main_pool_description',
        'main_patio_description',
        'main_terrace_description',
        'main_garage_description',
        'main_view_description'
      )
      details_of_rooms = property_data['rooms'] || {}
      listing_tags = property_data['property_tags'] || []
      design_style = property_data['property_style'] || ''
      sale_listing_gen_prompt = {
        property_data_prompt: @listing_prompt_generator.property_data_prompt
      }
      sale_listing.update!(
        section_descriptions:,
        sale_listing_gen_prompt:,
        details_of_rooms:,
        listing_tags:,
        design_style:
      )
    end

    def create_listing_only(property_data)
      property_data['reference'] = property_data['title'].parameterize
      property_data['import_url'] = "http://propertywebbuilder.com/fantasy-listings/#{SecureRandom.uuid}"

      request_host = 'fake_listing_creator_host'
      creator = Creators::Full::FullListingAndAssetCreator.new(
        save_images_locally: true
      )
      sale_listing = creator.create_and_update_data_only(property_data, request_host)
      puts "sale_listing: #{sale_listing.id}"
      sale_listing
    end
  end
end
