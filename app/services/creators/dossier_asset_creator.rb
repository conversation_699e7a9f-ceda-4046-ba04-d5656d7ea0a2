module Creators
  class DossierAssetCreator
    PORTAL_CONFIG = {
      'purplebricks' => {
        scrape_class: 'ScrapeItemFromPurplebricks',
        connector: 'ScraperConnectors::Purplebricks',
        method: :find_or_create_for_h2c_purplebricks,
        include_trailing_slash: false
      },
      'onthemarket' => {
        scrape_class: 'ScrapeItemFromOtm',
        connector: 'ScraperConnectors::Regular',
        method: :find_or_create_for_h2c_onthemarket,
        include_trailing_slash: true
      },
      'zoopla' => {
        scrape_class: 'ScrapeItem',
        connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c,
        include_trailing_slash: false
      },
      'rightmove' => {
        scrape_class: 'ScrapeItemFromRightmove',
        connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_rightmove,
        include_trailing_slash: false
      },
      'buenavista' => {
        scrape_class: 'ScrapeItemFromBuenavista', connector: 'ScraperConnectors::Json',
        method: :find_or_create_for_h2c_buenavista,
        include_trailing_slash: false
      }
    }.freeze

    MINIMUM_SCRAPE_LENGTH = 1000
    class UnknownPortalError < StandardError; end
    class DossierNotFoundError < StandardError; end

    # Creates a DossierAsset from a URL and associates it with an existing RealtyDossier.
    # @param dossier_uuid [String] The UUID of the existing RealtyDossier
    # @param url [String] The source URL to scrape for the asset
    # @param portal [String] The portal identifier (e.g., 'zoopla', 'rightmove')
    # @param is_primary [Boolean] Whether this asset should be marked as primary (default: false)
    # @return [DossierAsset] The created or existing dossier asset
    # @raise [UnknownPortalError] If the portal is not recognized
    # @raise [DossierNotFoundError] If the dossier UUID doesn't correspond to an existing dossier
    def create_asset_for_dossier(dossier_uuid, url, portal, is_primary: false)
      validate_inputs(dossier_uuid, url, portal)
      dossier = fetch_dossier(dossier_uuid)
      scrape_item = scrape_content(url, portal)
      validate_scrape_result(scrape_item.full_content_before_js)

      # CouldDo: check for and re-use existing listings
      # not a priority for now
      sale_listing = create_sale_listing(scrape_item, portal)
      create_and_associate_asset(dossier, sale_listing, is_primary)
      # below is currently for bvh - might apply to other portals later
      sale_listing.realty_asset.set_bvh_geojson if sale_listing.realty_asset_uuid
    rescue StandardError => e
      Rails.logger.error("Failed to create dossier asset from #{portal} URL for dossier #{dossier_uuid}: #{e.message}")
      raise
    end

    private

    def validate_inputs(dossier_uuid, url, portal)
      raise ArgumentError, 'Invalid UUID' unless dossier_uuid =~ /\A[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\z/
      raise ArgumentError, 'Invalid URL' unless url =~ URI::DEFAULT_PARSER.make_regexp
      raise UnknownPortalError, "Unknown portal: #{portal}" unless PORTAL_CONFIG.key?(portal)
    end

    def fetch_dossier(dossier_uuid)
      dossier = RealtyDossier.find_by(uuid: dossier_uuid)
      raise DossierNotFoundError, "No dossier found with UUID: #{dossier_uuid}" unless dossier

      dossier
    end

    def scrape_content(url, portal)
      p_config = PORTAL_CONFIG[portal]
      scrape_class = p_config[:scrape_class].constantize
      scrape_item = scrape_class.send(p_config[:method], url)
      scrape_item.retrieve_and_set_content_object(
        p_config[:connector],
        include_trailing_slash: p_config[:include_trailing_slash],
        force_retrieval: false
      )
      scrape_item
    end

    def validate_scrape_result(content)
      return if content && content.length > MINIMUM_SCRAPE_LENGTH

      Rails.logger.debug { "scrape_result: #{content}" }
      raise 'scrape_result unavailable or suspiciously short'
    end

    # CouldDo: check for and re-use existing listings
    # not a priority for now
    def create_sale_listing(scrape_item, portal)
      if portal == 'zoopla'
        RealtyParsers::ParseListingViaLlm.new.inferred_listing_from_scrape_item(scrape_item)
      else
        scrape_item.sale_listing_from_scrape_item
      end
    end

    def create_and_associate_asset(dossier, sale_listing, is_primary)
      # primary_dossier_asset is created in portal_dossier_creator
      DossierAsset.find_or_create_by(
        realty_dossier_uuid: dossier.uuid,
        origin_sale_listing_uuid: sale_listing.uuid,
        # 13 may 2025 - realty_asset_uuid below could be redundant but keeping it
        # as that is how I started - with sale_listings hanging off
        # realty_assets
        realty_asset_uuid: sale_listing.realty_asset.uuid,
        is_primary_dossier_asset: is_primary
      ) do |asset|
        asset.is_most_comparable_to_primary = false
        asset.is_good_comparable_to_primary = false
        asset.property_is_for_sale = true
        asset.agency_tenant_uuid = sale_listing.agency_tenant_uuid
      end.tap(&:save!)
    end
  end
end
