module Charts
  # app/services/charts_data_service.rb
  class ChartsDataService
    CACHE_DURATION = 1.hour

    def initialize(sold_transaction_epcs:, sold_transactions: nil)
      @sold_transaction_epcs = sold_transaction_epcs
      # if sold_transaction_epcs.present?
      #   @sold_transaction_epcs = sold_transaction_epcs
      # elsif sold_transactions.present?
      #   sold_transaction_epcs
      # end
    end

    def get_chart_data(data_source_name)
      case data_source_name
      when 'sold_data_scatter_charts'
        get_sorted_floor_area_chart_data
      when 'sold_data_by_habitable_rooms'
        get_sorted_habitable_rooms_chart_data
      else
        []
      end
    end

    def get_sale_v_predicted_price_data(limit = 10)
      raw_series_data = sale_v_predicted_price_data(@sold_transaction_epcs.limit(limit))
      build_time_series_data(
        series_data(raw_series_data, name: 'Actual Vs. Predicted Prices'),
        chart_options(
          chart_type: 'scatter',
          x_label: 'Predicted Price',
          y_label: 'Actual Price',
          x_min: 50_000,
          y_min: 50_000,
          title: 'Actual House Prices Vs. Predicted Prices'
        )
      )
    end

    def get_price_by_floor_area_data(limit = 10)
      raw_series_data = price_by_floor_area_data(@sold_transaction_epcs.limit(limit))
      build_time_series_data(
        series_data(raw_series_data, name: 'Price By Floor Area'),
        chart_options(
          chart_type: 'scatter',
          x_label: 'Floor Area',
          y_label: 'Sold Price',
          # x_min: 50_000,
          # y_min: 50_000,
          title: 'House Prices Vs. Floor Area'
        )
      )
    end

    private

    def get_sorted_floor_area_chart_data
      Rails.cache.fetch('sorted_floor_area_chart_data', expires_in: CACHE_DURATION) do
        @sold_transaction_epcs.map do |transaction|
          [
            transaction.total_floor_area,
            transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f,
            'label'
          ]
        end.sort_by { |data| data[0].to_f }
      end
    end

    def get_sorted_habitable_rooms_chart_data
      Rails.cache.fetch('sold_data_by_habitable_rooms', expires_in: 1.minute) do
        @sold_transaction_epcs.map do |transaction|
          [
            transaction.number_habitable_rooms,
            transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f
          ]
        end.sort_by { |data| data[0].to_f }
      end
    end

    def price_by_floor_area_data(sold_transaction_epcs)
      sold_transaction_epcs.map do |transaction|
        sold_price = transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_i
        floor_area = transaction.total_floor_area.to_i
        # fill_color = 'orange'
        # fill_color = 'blue' if predicted_price > sold_price
        # predicted_price_difference = predicted_price - sold_price
        {
          x: floor_area,
          y: sold_price,
          dataItemLabel: transaction.sold_transaction.st_long_address,
          fillColor: 'green',
          totalFloorArea: transaction.total_floor_area,
          # predictedPriceDifference: predicted_price_difference,
          # predictedPriceA: transaction.st_predicted_price_a,
          soldDate: transaction.sold_transaction.sold_date&.strftime('%B %Y'),
          postalCode: transaction.sold_transaction.st_postal_code,
          soldPriceShort: transaction.sold_transaction.short_formatted_sold_price,
          soldPrice: transaction.sold_transaction.formatted_sold_price,
          transactionLongitude: transaction.sold_transaction.longitude,
          transactionLatitude: transaction.sold_transaction.latitude,
          soldTransactionEpcId: transaction.id,
          SoldTransactionId: transaction.sold_transaction.id
        }
      end
    end

    def sale_v_predicted_price_data(sold_transaction_epcs)
      sold_transaction_epcs.map do |transaction|
        next unless transaction.st_predicted_price_a.present?

        sold_price = transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_i
        predicted_price = transaction.st_predicted_price_a.gsub(/[^\d.]/, '').to_i
        fill_color = 'orange'
        fill_color = 'blue' if predicted_price > sold_price
        predicted_price_difference = predicted_price - sold_price
        datapoint_size = 22
        {
          size: datapoint_size,
          marker: {
            size: 6,
            fillColor: '#FFA500',
            strokeColor: '#000000',
            strokeWidth: 2
          },
          dataItemLabel: transaction.sold_transaction.st_long_address,
          label: {
            borderColor: '#FFA500',
            borderWidth: 1,
            text: 'High Predicted Price',
            style: {
              fontSize: '12px',
              fontWeight: 'bold',
              background: '#FFFACD',
              color: '#000'
            }
          },
          x: predicted_price,
          y: sold_price,
          fillColor: fill_color,
          totalFloorArea: transaction.total_floor_area,
          soldDate: transaction.sold_transaction.sold_date&.strftime('%B %Y'),
          postalCode: transaction.sold_transaction.st_postal_code,
          predictedPriceDifference: predicted_price_difference,
          predictedPriceA: transaction.st_predicted_price_a,
          soldPriceShort: transaction.sold_transaction.short_formatted_sold_price,
          soldPrice: transaction.sold_transaction.formatted_sold_price,
          transactionLongitude: transaction.sold_transaction.longitude,
          transactionLatitude: transaction.sold_transaction.latitude,
          soldTransactionEpcId: transaction.id,
          SoldTransactionId: transaction.sold_transaction.id
        }
      end
    end

    def build_time_series_data(series, options)
      { series:, options: }
    end

    def series_data(data_points, name: 'Price')
      [{ name:, data: data_points }]
    end

    def marker_options
      {
        size: 10,
        strokeColors: '#fff',
        strokeWidth: 2,
        strokeOpacity: 0.9,
        strokeDashArray: 0,
        fillOpacity: 1,
        discrete: [],
        shape: 'circle',
        offsetX: 0,
        offsetY: 0,
        showNullDataPoints: true,
        hover: { sizeOffset: 3 }
      }
    end

    def chart_options(
      chart_type: 'line', title: 'Average House Prices Over Years',
      y_label: 'Price (USD)', x_label: 'Year', y_min: 'undefined', x_min: 'undefined'
    )
      xaxis = { title: { text: x_label }, tickAmount: nil }
      yaxis = { title: { text: y_label } }

      xaxis[:min] = x_min if x_min != 'undefined'
      yaxis[:min] = y_min if y_min != 'undefined'

      {
        markers: marker_options,
        chart: { type: chart_type },
        xaxis:,
        yaxis:,
        title: { text: title },
        plotOptions: {
          scatter: {
            marker: {
              radius: 3
            }
          }
        }
      }
    end
  end
end
