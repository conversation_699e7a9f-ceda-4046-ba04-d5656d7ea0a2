module Ai
  # app/services/llm_api_service.rb
  # require 'openai'
  # require 'json'

  class LlmApiService
    # Feb 2025 - will prefer this over Ai::LlmInteractionService
    # going foward as it tries to do too much...
    def initialize(client_to_use: nil)
      @llm_parameters = {
        model: 'gpt-4o-mini'
      }
      if client_to_use && client_to_use == 'gemini'
        # This attempt to use the Google Gemini API was unsuccessful
        # In future will use LangchainService for that
        # api_key = Rails.application.credentials.config[:gemini_api_key]
        # @openai_client = OpenAI::Client.new(
        #   access_token: api_key,
        #   uri_base: 'https://generativelanguage.googleapis.com/v1beta/openai/'
        # )
        # @llm_parameters[:model] = 'gemini-2.0-flash'
      else
        api_key = Rails.application.credentials.config[:openai_api_key]
        @openai_client = OpenAI::Client.new(access_token: api_key)
        @llm_parameters[:response_format] = { type: 'json_object' }
      end
    end

    def call_llm_api(
      prompt, use_vcr: false, cassette_name: nil, cassette_library_dir: nil
    )
      cassette_name ||= 'unknown_llm_interaction'
      cassette_name = "#{cassette_name}_#{@llm_parameters[:model]}"
      llm_interaction_slug = "#{cassette_name}_#{@llm_parameters[:temperature]}"

      llm_interaction = LlmInteraction.initialize_llm_interaction(
        prompt, llm_interaction_slug, @llm_parameters
      )
      puts "llm_interaction: #{llm_interaction}"
      cassette_library_dir ||= 'spec/cassettes'

      @llm_parameters[:messages] = [
        # { role: 'system', content: 'You are a real estate price evaluation expert.' },
        { role: 'user', content: prompt }
      ]

      if use_vcr && cassette_name
        VCR.configure do |config|
          config.cassette_library_dir = cassette_library_dir
          config.hook_into :webmock
          config.ignore_localhost = true
        end

        VCR.use_cassette(cassette_name) do
          make_api_json_call(@llm_parameters, llm_interaction)
        end
      else
        make_api_json_call(@llm_parameters, llm_interaction)
      end
      llm_interaction
    end

    private

    def make_api_json_call(llm_parameters, llm_interaction)
      # Rails.logger.info("Calling LLM with prompt:\n#{prompt}")

      response = @openai_client.chat(
        parameters: llm_parameters
      )

      llm_response = response.dig('choices', 0, 'message', 'content')

      # Clean up the response by removing extraneous characters (e.g., ```json and ```)
      cleaned_response = llm_response.gsub(/```json\n|```|\n```/, '').strip

      Rails.logger.info("LLM response: #{cleaned_response}")
      llm_interaction.update_llm_interaction_success(response, cleaned_response)
      # Parse the JSON response from the LLM
      JSON.parse(cleaned_response)
    rescue JSON::ParserError => e
      Rails.logger.error("Failed to parse LLM response as JSON: #{e.message}")
      llm_interaction.update_llm_interaction_with_error(response, e.message)
      []
    rescue StandardError => e
      Rails.logger.error("LLM API call failed: #{e.message}")
      llm_interaction.update_llm_interaction_with_error(response, e.message)

      []
    end
  end
end
