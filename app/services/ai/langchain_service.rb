require 'vcr'

module Ai
  class LangchainService
    # Feb 2025 - reason for this service is that the
    # regular LlmApiService is tricky to get working with gemini
    def initialize(client_to_use: nil)
      @llm_parameters = {
        model: 'gpt-4o-mini'
      }

      if client_to_use == 'gemini'
        api_key = Rails.application.credentials.config[:gemini_api_key]
        @llm_client = Langchain::LLM::GoogleGemini.new(api_key: api_key)
        # @llm_parameters[:model] = 'gemini-1.5-flash'
        @llm_parameters[:model] = 'gemini-2.0-flash-001'
        # Need to look up exact model name here:
        # https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models#gemini-models
        #  'gemini-2-flash'
      elsif client_to_use == 'gemini-vision'
        api_key = Rails.application.credentials.config[:gemini_api_key]
        @llm_client = Langchain::LLM::GoogleGemini.new(api_key: api_key)
        # @llm_parameters[:model] = 'gemini-1.0-pro-vision'
        # above has been replaced by below
        @llm_parameters[:model] = 'gemini-1.5-flash-latest'
        # @llm_client = Langchain::LLM::GoogleVertexAI.new(api_key: api_key)
        # @llm_parameters[:model] = 'vertexai-1.0'
      else
        api_key = Rails.application.credentials.config[:openai_api_key]
        @llm_client = Langchain::LLM::OpenAI.new(api_key: api_key)
        @llm_parameters[:response_format] = 'json'
      end
    end

    def call_llm_api(prompt, use_vcr: false, cassette_name: nil, cassette_library_dir: nil)
      cassette_name ||= 'unknown_llm_interaction'
      cassette_name = "#{cassette_name}_#{@llm_parameters[:model]}"
      llm_interaction_slug = "#{cassette_name}_#{@llm_parameters[:temperature]}"

      llm_interaction = LlmInteraction.initialize_llm_interaction(prompt, llm_interaction_slug, @llm_parameters)
      puts "llm_interaction: #{llm_interaction}"
      cassette_library_dir ||= 'spec/cassettes'

      if use_vcr && cassette_name
        VCR.configure do |config|
          config.cassette_library_dir = cassette_library_dir
          config.hook_into :webmock
          config.ignore_localhost = true
        end

        VCR.use_cassette(cassette_name) do
          make_api_call(prompt, llm_interaction)
        end
      else
        make_api_call(prompt, llm_interaction)
      end

      llm_interaction
    end

    private

    def make_api_call(prompt, llm_interaction)
      # messages_for_llm = [
      #   { role: 'system', content: 'You are a helpful assistant.' },
      #   { role: 'user', content: "What's the weather like today?" }
      #   # Google Gemini and Google VertexAI expect messages in a different format:
      #   # { role: "user", parts: [{ text: "why is the sky blue?" }]}
      # ]

      messages_for_llm = if @llm_client.is_a?(Langchain::LLM::GoogleGemini)
                           [{ role: 'user', parts: [{ text: prompt }] }]
                         else
                           [{ role: 'user', content: prompt }]
                         end
      # response = @llm_client.chat(messages: [{ role: 'user', content: prompt }])

      @llm_parameters[:messages] = messages_for_llm
      langchanrb_response = @llm_client.chat(@llm_parameters)
      # llm_response = langchanrb_response.chat_completion

      #       Each LLM method returns a response object that provides a consistent interface for accessing the results:

      # embedding: Returns the embedding vector
      # completion: Returns the generated text completion
      # chat_completion: Returns the generated chat completion
      # tool_calls: Returns tool calls made by the LLM
      # prompt_tokens: Returns the number of tokens in the prompt
      # completion_tokens: Returns the number of tokens in the completion
      # total_tokens: Returns the total number of tokens used

      # Clean up the response by removing extraneous characters (e.g., ```json and ```)
      cleaned_response = langchanrb_response.chat_completion.gsub(/```json\n|```|\n```/, '').strip
      # Rails.logger.info("LLM response: #{cleaned_response}")
      llm_interaction.update_langchanrb_llm_interaction_success(langchanrb_response, cleaned_response)
      # Parse the JSON response from the LLM
      JSON.parse(cleaned_response)
    rescue JSON::ParserError => e
      Rails.logger.error("Failed to parse LLM response as JSON: #{e.message}")
      llm_interaction.update_llm_interaction_with_error(langchanrb_response, e.message)
      []
    rescue StandardError => e
      Rails.logger.error("LLM API call failed: #{e.message}")
      llm_interaction.update_llm_interaction_with_error(langchanrb_response, e.message)
      []
    end
  end
end
