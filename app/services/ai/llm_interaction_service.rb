module Ai
  # Feb 2025 - will lean towards using Ai::LlmApiService
  # going foward as this tries to do too much...
  class LlmInteractionService
    DEFAULT_MODEL_NAME = 'gpt-3.5-turbo'.freeze
    DEFAULT_TEMPERATURE = 0.7

    def initialize(client:, model_name: DEFAULT_MODEL_NAME, temperature: DEFAULT_TEMPERATURE)
      @client = client
      @model_name = model_name
      @temperature = temperature
      @response = nil
    end

    def generate_property_data(prompt)
      llm_interaction = initialize_llm_interaction(prompt, 'chat')

      begin
        response = if replicate_client?
                     @client.generate_text(prompt, model: @model_name, temperature: @temperature)
                   else
                     @client.chat(parameters: chat_parameters(prompt))
                   end

        @response = response
        content = extract_content(response)

        if content.present?
          parsed_content = parse_llm_response(content)
          # parsed_content = JSON.parse(content)
          update_llm_interaction(llm_interaction, response, parsed_content)
          Dry::Monads::Success([parsed_content, llm_interaction])
        else
          update_llm_interaction_with_error(llm_interaction, response, 'No content returned in the response')
          Dry::Monads::Failure([:no_content, llm_interaction])
        end
      rescue Faraday::UnauthorizedError
        handle_error(llm_interaction, 'Unauthorized request to the LLM API. Check your API key.')
        Dry::Monads::Failure([:unauthorized, llm_interaction])
      rescue JSON::ParserError => e
        handle_error(llm_interaction, "Error parsing property data JSON: #{e.message}")
        Dry::Monads::Failure([:json_parsing_error, llm_interaction])
      end
    end

    def parse_llm_response(response)
      # Remove Markdown code block markers
      cleaned_response = response.gsub(/```json|```/, '').strip

      # Parse the cleaned JSON
      begin
        json_data = JSON.parse(cleaned_response)
        puts 'Parsed JSON successfully!'
        json_data
      rescue JSON::ParserError => e
        puts "Error parsing JSON: #{e.message}"
        nil
      end
    end

    def fetch_image_url(prompt)
      llm_interaction = initialize_llm_interaction(prompt, 'images.generate')

      begin
        response = if replicate_client?
                     @client.generate_image(prompt)
                   else
                     @client.images.generate(parameters: image_parameters(prompt))
                   end
        image_url = extract_image_url(response)

        if image_url
          update_llm_interaction(llm_interaction, response, { url: image_url })
          Dry::Monads::Success([image_url, llm_interaction])
        else
          update_llm_interaction_with_error(llm_interaction, response, 'No image URL returned in the response')
          Dry::Monads::Failure([:no_image_url, llm_interaction])
        end
      rescue Faraday::UnauthorizedError
        handle_error(llm_interaction, 'Unauthorized request to the LLM API during image generation. Check your API key.')
        Dry::Monads::Failure([:unauthorized, llm_interaction])
      rescue StandardError => e
        handle_error(llm_interaction, "Error generating image for prompt '#{prompt}': #{e.message}")
        Dry::Monads::Failure([:generation_error, llm_interaction])
      end
    end

    private

    def initialize_llm_interaction(prompt, endpoint)
      # TODO: - add a @client.llm_client_slug that I can use below
      llm_interaction_slug = "#{@model_name}-#{endpoint}-#{SecureRandom.uuid[0..4]}"
      LlmInteraction.create(
        ai_model: @model_name,
        endpoint:,
        llm_interaction_slug:,
        full_prompt: prompt,
        temperature: @temperature,
        has_errored: true
      )
    end

    def update_llm_interaction(llm_interaction, response, chosen_response)
      client_slug = @client.get_client_slug
      response_model = response['model']
      response_id = response['id'] || '??..??'
      llm_interaction_slug = "#{client_slug}-#{llm_interaction.endpoint}-#{response_model}-success-#{response_id[0..3]}"
      llm_interaction.update(
        llm_interaction_slug:,
        full_response: response,
        chosen_response:,
        response_object: response['object'],
        response_id:,
        response_model: response.dig('model'),
        response_prompt_tokens: response.dig('usage', 'prompt_tokens'),
        response_completion_tokens: response.dig('usage', 'completion_tokens'),
        response_total_tokens: response.dig('usage', 'total_tokens'),
        has_errored: false
      )
    end

    def update_llm_interaction_with_error(llm_interaction, response, error_message)
      client_slug = @client.get_client_slug
      response_model = response['model']
      response_id = response['id'] || '??..??'
      llm_interaction_slug = "#{client_slug}-#{llm_interaction.endpoint}-#{response_model}-error-#{response_id[0..3]}"
      llm_interaction.update(
        llm_interaction_slug:,
        full_response: response,
        response_object: response['object'],
        response_id: response['id'],
        response_model:,
        response_prompt_tokens: response.dig('usage', 'prompt_tokens'),
        response_completion_tokens: response.dig('usage', 'completion_tokens'),
        response_total_tokens: response.dig('usage', 'total_tokens'),
        has_errored: true,
        llm_error_message: error_message
      )
    end

    def handle_error(llm_interaction, error_message)
      Rails.logger.error error_message
      response = @response || {}
      # llm_interaction.update(llm_error_message: error_message)
      update_llm_interaction_with_error(llm_interaction, response, error_message)
    end

    def chat_parameters(prompt)
      {
        model: @model_name,
        messages: [{ role: 'user', content: prompt }],
        temperature: @temperature
      }
    end

    def image_parameters(prompt)
      {
        prompt:,
        size: '1024x1024',
        quality: 'standard',
        n: 1
      }
    end

    def replicate_client?
      @client.is_a?(Ai::Clients::ReplicateClient)
    end

    def extract_content(response)
      if replicate_client?
        response[:text]
      else
        response.dig('choices', 0, 'message', 'content')
      end
    end

    def extract_image_url(response)
      if replicate_client?
        if %w[processing succeeded].include? response['status']
          #  response['status'] == 'succeeded'
          # { image_url: response.dig('output', 0) }
          response.dig('output', 0)
        elsif response['status'] == 'failed'
          raise StandardError, "Replicate API error: #{response['error'] || 'Unknown error'}"
        else
          raise StandardError, 'Unexpected API response status'
        end
      else
        response.dig('data', 0, 'url')
      end
    end
  end
end
