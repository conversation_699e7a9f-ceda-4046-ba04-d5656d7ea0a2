module Ai::PropertyInsights
  class SideBySideSectionsAnalyser
    # Default LLM client to use if none is specified during initialization.
    DEFAULT_CLIENT = 'side_by_side_sections_analysis'.freeze # Assuming a text-based LLM client; adjust as needed
    # Directory for storing VCR cassettes for LLM API interactions.
    CASSETTE_DIR = 'spec/cassettes/side_by_side_sections_analysis'.freeze

    # Initializes the analyser with an LLM API service.
    # @param client_to_use [String, nil] The specific LLM client to use (e.g., 'gemini', 'openai').
    #                                     Defaults to DEFAULT_CLIENT if nil.
    def initialize(client_to_use: nil)
      puts "[SideBySideSectionsAnalyser] Initializing with client: #{client_to_use || DEFAULT_CLIENT}"
      # Instantiate the LLM service, which will handle communication with the AI model.
      @llm_api_service = Ai::RubyLlmService.new(client_to_use: client_to_use || DEFAULT_CLIENT)
      puts '[SideBySideSectionsAnalyser] Initialization complete.'
    end

    # Main method to orchestrate the comparison process between a realty dossier and a secondary asset.
    # This involves fetching data, transforming it via LLM, and storing the result.
    # @param realty_dossier [RealtyDossier] The primary property dossier.
    # @param secondary_asset [DossierAsset] The secondary asset to compare against.
    # @return [DossierAssetsComparison, Hash, nil] The created or updated comparison object,
    #                                              an error hash, or nil on failure.
    def fully_process_comparison_for_dossier(realty_dossier, secondary_asset)
      puts "[SideBySideSectionsAnalyser] Starting fully_process_comparison_for_dossier for RealtyDossier ID: #{realty_dossier&.id}, SecondaryAsset ID: #{secondary_asset&.id}"

      # Validate input parameters.
      raise 'RealtyDossier not found' unless realty_dossier
      raise 'SecondaryAsset not found' unless secondary_asset

      puts '[SideBySideSectionsAnalyser] Input parameters validated.'

      # Step 1: Fetch and build the initial comparison JSON data structure.
      puts '[SideBySideSectionsAnalyser] Fetching comparison JSON...'
      comparison_json = fetch_comparison_json(realty_dossier, secondary_asset)
      puts "[SideBySideSectionsAnalyser] Comparison JSON fetched: #{comparison_json.is_a?(Hash) && comparison_json.key?(:error) ? "Error: #{comparison_json[:error]}" : 'Success'}"

      # Handle errors during JSON fetching.
      if comparison_json.is_a?(Hash) && comparison_json[:error]
        Rails.logger.error "Error fetching comparison JSON: #{comparison_json[:error]}"
        puts "[SideBySideSectionsAnalyser] Error fetching comparison JSON: #{comparison_json[:error]}"
        return comparison_json # Or handle the error appropriately
      end

      # Step 2: Send the comparison JSON to the LLM for analysis and transformation.
      puts '[SideBySideSectionsAnalyser] Processing JSON transformation with LLM...'
      llm_json_reply = process_json_transformation(
        realty_dossier, secondary_asset, comparison_json
      )

      # Handle cases where LLM processing fails or returns no data.
      if llm_json_reply.nil?
        Rails.logger.error 'Failed to transform the comparison JSON.'
        puts '[SideBySideSectionsAnalyser] Failed to transform the comparison JSON. LLM reply was nil.'
        return nil # Or handle the error appropriately
      end
      puts '[SideBySideSectionsAnalyser] LLM JSON reply received.' # Add a puts here to show we got something

      # (Commented out) Alternative: Load LLM reply from a local file for debugging/testing.
      # data_file_path = File.join(
      #   Rails.root, 'db', 'ai_prompts/from_code/side_by_side_analyser_response_grok_ui.txt'
      # )
      # llm_json_reply = JSON.parse(File.read(data_file_path))
      # puts "[SideBySideSectionsAnalyser] (Debug) Loaded LLM JSON reply from file."

      # Construct a unique slug for the comparison.
      comparison_slug = "dossier_#{realty_dossier.id}_v_sec_ast_#{secondary_asset.id}"
      puts "[SideBySideSectionsAnalyser] Generated comparison_slug: #{comparison_slug}"

      # Prepare options for creating/updating the DossierAssetsComparison record.
      options = {
        comparison_slug: comparison_slug,
        realty_dossier_uuid: realty_dossier.uuid,
        second_dossier_asset_uuid: secondary_asset.uuid
      }
      puts "[SideBySideSectionsAnalyser] Options for DossierAssetsComparison: #{options.inspect}"

      # Step 3: Create or update the DossierAssetsComparison record with the LLM's response.
      puts '[SideBySideSectionsAnalyser] Creating or updating DossierAssetsComparison...'
      comparison = DossierAssetsComparison.create_or_update_from_llm_response(
        llm_json_reply, options
      )
      puts "[SideBySideSectionsAnalyser] Created/Updated DossierAssetsComparison with ID: #{comparison&.id}"

      # Return the created or updated comparison object.
      puts '[SideBySideSectionsAnalyser] fully_process_comparison_for_dossier complete. Returning comparison.'
      comparison
    end

    private

    # Fetches and structures comparison JSON data for two properties.
    # This method acts as a wrapper for build_comparison_json and includes error handling.
    # @param realty_dossier [RealtyDossier] The primary property dossier.
    # @param dossier_asset_to_compare [DossierAsset] The secondary asset.
    # @return [Hash] The structured comparison JSON or an error hash.
    def fetch_comparison_json(realty_dossier, dossier_asset_to_compare)
      puts "[SideBySideSectionsAnalyser#fetch_comparison_json] Called with RealtyDossier ID: #{realty_dossier&.id}, DossierAssetToCompare ID: #{dossier_asset_to_compare&.id}"
      # (Commented out) Previous logic for finding entities by UUID, now passed directly.
      # realty_dossier = RealtyDossier.find_by_uuid(realty_dossier_uuid)
      # return { error: 'Realty dossier not found' } unless realty_dossier
      # dossier_asset_to_compare = DossierAsset.find_by_uuid(secondary_asset_uuid)
      # return { error: 'Secondary asset not found' } unless dossier_asset_to_compare
      # close_uprn_details_json = {}
      # close_uprn_details_json = sale_listing.realty_asset.close_uprn_details_json if sale_listing&.realty_asset&.close_uprn_details_json

      # Build the comparison JSON structure using dedicated helper methods.
      puts '[SideBySideSectionsAnalyser#fetch_comparison_json] Calling build_comparison_json...'
      comparison_data = build_comparison_json(realty_dossier, dossier_asset_to_compare)
      puts '[SideBySideSectionsAnalyser#fetch_comparison_json] Returning from build_comparison_json.'
      comparison_data
    rescue StandardError => e
      # Log any errors encountered during the fetching process.
      log_error('Failed to fetch comparison JSON', e)
      puts "[SideBySideSectionsAnalyser#fetch_comparison_json] Error: #{e.message}"
      { error: "Failed to fetch comparison data: #{e.message}" }
    end

    # Builds the main structured comparison JSON object.
    # @param realty_dossier [RealtyDossier] The primary property dossier.
    # @param secondary_asset [DossierAsset] The secondary asset.
    # @return [Hash] The structured comparison JSON.
    def build_comparison_json(realty_dossier, secondary_asset)
      puts "[SideBySideSectionsAnalyser#build_comparison_json] Called with RealtyDossier ID: #{realty_dossier&.id}, SecondaryAsset ID: #{secondary_asset&.id}"
      # Create the root structure for the comparison.
      comparison = {
        dossier_properties_comparison: {
          # Populate data for the first property.
          first_property: first_property_json(realty_dossier),
          # Populate data for the second property.
          second_property: second_property_json(secondary_asset)
        }
      }
      puts '[SideBySideSectionsAnalyser#build_comparison_json] Main comparison structure built.'

      # (Commented out) Optionally add UPRN details if available.
      # comparison[:close_uprn_details] = close_uprn_details_json unless close_uprn_details_json.empty?

      puts '[SideBySideSectionsAnalyser#build_comparison_json] Returning comparison hash.'
      comparison
      # (Commented out) Rescue block moved to the caller (fetch_comparison_json) for broader coverage.
      # rescue StandardError => e
      #   log_error('Failed to build comparison JSON', e)
      #   { error: "Failed to build comparison structure: #{e.message}" }
    end

    # Builds the JSON structure for the first (primary) property.
    # @param realty_dossier [RealtyDossier] The primary property dossier.
    # @return [Hash] JSON structure for the first property.
    def first_property_json(realty_dossier)
      puts "[SideBySideSectionsAnalyser#first_property_json] Called for RealtyDossier ID: #{realty_dossier&.id}"
      # Ensure the primary asset has a minimum number of parts.
      asset_parts_count = realty_dossier.primary_dossier_asset&.dossier_asset_parts&.kept&.count
      puts "[SideBySideSectionsAnalyser#first_property_json] Primary asset parts count: #{asset_parts_count}"
      raise 'RealtyDossier primary_asset has less than 2 asset_parts' unless asset_parts_count && asset_parts_count > 2 # Ensure asset_parts_count is not nil before comparison

      # (Commented out) Redundant check if realty_dossier itself is nil, already handled by caller or initial checks.
      # return {} unless realty_dossier&.primary_dossier_asset

      primary_asset = realty_dossier.primary_dossier_asset
      puts "[SideBySideSectionsAnalyser#first_property_json] Building JSON for primary_asset ID: #{primary_asset&.id}"
      result = {
        asset_title_calc: primary_asset.asset_title_calc,
        # Build JSON for the parts of the primary asset.
        asset_parts: asset_parts_json(primary_asset&.dossier_asset_parts&.kept)
      }
      puts '[SideBySideSectionsAnalyser#first_property_json] JSON for first property built.'
      result
      # (Commented out) Rescue block could be here, but often better handled by a calling method that aggregates errors.
      # rescue StandardError => e
      #   log_error('Failed to build first property JSON structure', e)
      #   {}
    end

    # Builds the JSON structure for the second (comparative) property.
    # @param secondary_asset [DossierAsset] The secondary asset.
    # @return [Hash] JSON structure for the second property.
    def second_property_json(secondary_asset)
      puts "[SideBySideSectionsAnalyser#second_property_json] Called for SecondaryAsset ID: #{secondary_asset&.id}"
      # Ensure the secondary asset has a minimum number of parts.
      asset_parts_count = secondary_asset&.dossier_asset_parts&.kept&.count
      puts "[SideBySideSectionsAnalyser#second_property_json] Secondary asset parts count: #{asset_parts_count}"
      raise 'target secondary_asset has less than 2 asset_parts' unless asset_parts_count && asset_parts_count > 2 # Ensure asset_parts_count is not nil

      # (Commented out) Redundant check if secondary_asset is nil.
      # return {} unless secondary_asset

      puts "[SideBySideSectionsAnalyser#second_property_json] Building JSON for secondary_asset ID: #{secondary_asset&.id}"
      result = {
        asset_title_calc: secondary_asset.asset_title_calc,
        # Build JSON for the parts of the secondary asset.
        asset_parts: asset_parts_json(secondary_asset&.dossier_asset_parts&.kept)
      }
      puts '[SideBySideSectionsAnalyser#second_property_json] JSON for second property built.'
      result
      # (Commented out) Rescue block.
      # rescue StandardError => e
      #   log_error('Failed to build second property JSON structure', e)
      #   {}
    end

    # Builds the JSON array for asset parts (rooms, sections, etc.).
    # @param asset_parts [ActiveRecord::Relation, Array<DossierAssetPart>] A collection of asset parts.
    # @return [Array<Hash>] An array of JSON objects, each representing an asset part.
    def asset_parts_json(asset_parts)
      puts "[SideBySideSectionsAnalyser#asset_parts_json] Called for #{asset_parts&.count} asset parts."
      return [] if asset_parts.blank? # Use blank? to handle nil or empty collections

      puts '[SideBySideSectionsAnalyser#asset_parts_json] Mapping asset parts...'
      # Map each asset part to its JSON representation.
      mapped_parts = asset_parts.map do |part|
        # puts "[SideBySideSectionsAnalyser#asset_parts_json] Mapping part ID: #{part.id}" # Can be verbose
        {
          id: part.id,
          asset_part_slug: part.asset_part_slug,
          asset_part_title: part.asset_part_title,
          asset_part_description: part.asset_part_description,
          # Use a summary of photos suitable for LLM processing.
          asset_part_photos_json_summary: part.asset_part_photos_json_summary_for_llm,
          asset_part_type: part.asset_part_type,
          asset_part_main_color: part.asset_part_main_color,
          asset_part_secondary_color: part.asset_part_secondary_color,
          asset_part_condition: part.asset_part_condition,
          asset_part_style: part.asset_part_style,
          asset_part_significant_items: part.asset_part_significant_items,
          asset_part_unique_features: part.asset_part_unique_features
          # (Commented out) asset_part_details might contain too much data for the LLM.
          # asset_part_details: part.asset_part_details
        }
      end
      puts "[SideBySideSectionsAnalyser#asset_parts_json] Asset parts mapping complete. Count: #{mapped_parts.size}"
      mapped_parts
    rescue StandardError => e
      # Log errors during asset part processing.
      log_error('Failed to build asset parts JSON', e)
      puts "[SideBySideSectionsAnalyser#asset_parts_json] Error: #{e.message}"
      [] # Return an empty array on error to prevent further issues.
    end

    # Processes the input JSON by sending it to the LLM API for transformation.
    # @param realty_dsr_item [RealtyDossier] The primary realty dossier (used for cassette naming).
    # @param secondary_asset [DossierAsset] The secondary asset (used for cassette naming).
    # @param input_json [Hash] The JSON data to be transformed.
    # @return [Hash, nil] The transformed JSON from the LLM, or nil on error.
    def process_json_transformation(realty_dsr_item, secondary_asset, input_json)
      puts "[SideBySideSectionsAnalyser#process_json_transformation] Called for RealtyDossier ID: #{realty_dsr_item&.id}, SecondaryAsset ID: #{secondary_asset&.id}"

      # Step 1: Build the prompt for the LLM.
      puts '[SideBySideSectionsAnalyser#process_json_transformation] Building prompt...'
      prompt_content = build_prompt(input_json)
      # puts "[SideBySideSectionsAnalyser#process_json_transformation] Prompt built:\n#{prompt_content.lines.first(5).join.strip}..." # Log first few lines of prompt

      # Generate a unique VCR cassette name for this specific comparison.
      cassette_name = "dossier_#{realty_dsr_item.id}_v_sec_asset_#{secondary_asset.id}_side_by_side_comp"
      puts "[SideBySideSectionsAnalyser#process_json_transformation] VCR Cassette name: #{cassette_name}"

      # (Commented out) Code for writing the prompt to a local file for debugging.
      # data_file_path = File.join(
      #   Rails.root, 'db', 'ai_prompts/from_code/side_by_side_analyser.txt'
      # )
      # FileUtils.mkdir_p(File.dirname(data_file_path))
      # File.write(data_file_path, prompt_content)
      # puts "[SideBySideSectionsAnalyser#process_json_transformation] (Debug) Prompt written to #{data_file_path}"

      # Step 2: Call the LLM API.
      puts '[SideBySideSectionsAnalyser#process_json_transformation] Calling LLM API service...'
      llm_interaction = @llm_api_service.call_llm_api_text(
        prompt_content,
        use_vcr: true, # VCR is used to record and replay API interactions for testing.
        cassette_name: cassette_name,
        cassette_library_dir: CASSETTE_DIR
      )
      puts '[SideBySideSectionsAnalyser#process_json_transformation] LLM API call complete.'

      # Step 3: Process the LLM's response.
      if llm_interaction.has_errored?
        log_error('LLM API error', llm_interaction.llm_error_message)
        puts "[SideBySideSectionsAnalyser#process_json_transformation] LLM API Error: #{llm_interaction.llm_error_message}"
        nil # Return nil if the LLM API call resulted in an error.
      else
        puts '[SideBySideSectionsAnalyser#process_json_transformation] LLM response received. Extracting JSON...'
        # Extract the JSON content from the LLM's (potentially text-based) response.
        extracted_json = extract_json_from_llm_response(llm_interaction.chosen_response)
        if extracted_json
          puts '[SideBySideSectionsAnalyser#process_json_transformation] JSON extracted successfully.'
        else
          puts '[SideBySideSectionsAnalyser#process_json_transformation] Failed to extract JSON from LLM response.'
        end
        extracted_json
      end
    rescue StandardError => e
      # Log any other errors during the transformation process.
      log_error('JSON transformation processing failed', e)
      puts "[SideBySideSectionsAnalyser#process_json_transformation] Error: #{e.message}"
      nil
    end

    # Builds the detailed prompt string to be sent to the LLM.
    # This prompt instructs the LLM on how to analyze and transform the input JSON.
    # @param input_json [Hash] The input JSON data containing property details.
    # @return [String] The formatted prompt string.
    def build_prompt(input_json)
      puts '[SideBySideSectionsAnalyser#build_prompt] Building prompt with input JSON.'
      # Convert the input hash to a pretty-printed JSON string for inclusion in the prompt.
      input_json_string = JSON.pretty_generate(input_json)
      # puts "[SideBySideSectionsAnalyser#build_prompt] Input JSON string for prompt: #{input_json_string.truncate(200)}" # Log truncated JSON

      # The heredoc defines the multi-line prompt.
      prompt = <<~PROMPT
        You are an expert in real estate data analysis and JSON transformation. I will provide you with a JSON object containing detailed data for two properties ("first_property" and "second_property") under a "dossier_comparison" key. While we will use first_property in keys etc, please refer to the first_property in texts as the "main property" and the second_property as the "second property".

        Please produce a comprehensive JSON response with these sections:

        1. **overall_property_comparison**: A detailed comparison of the properties:
           - "comparison_title": A short, catchy title for the comparison (5-8 words).
           - "comparison_description": A detailed overall comparison (150-200 words).
           - "relative_strengths": Array of 5-7 features where the second property is superior to the first.
           - "relative_weaknesses": Array of 5-7 features where the second property is inferior to the first.
           - "price_difference_percentage": Float of price difference percentage (positive if second property is more expensive).
           - "similarity_score": Float from 0-10 indicating overall similarity between properties.
           - "key_differentiators": Array of 3-5 most important differences between the properties.
           - "value_insights": String describing what this comparison suggests about each property's value.
           - "target_audience": Object with "first_property" and "second_property" keys, each containing detailed buyer profiles.
           - "investment_potential": Object with "first_property" and "second_property" keys, each with investment analysis.
           - "questions_for_seller": Object with "first_property" and "second_property" keys, each containing 5-7 important questions buyers should ask.
           - "recommended_tasks": Object with "first_property" and "second_property" keys, each listing 3-5 tasks buyers should undertake (surveys, inspections, etc.).

        2. **rooms_or_sections_comparisons**: For each asset part from both properties, map by type and compare:
           - "first_property_part_id": The ID of the first property's asset part.
           - "first_property_part_slug": The asset_part_slug of the first property's asset part.
           - "second_property_part_id": The ID of the second property's asset part (null if no match).
           - "second_property_part_slug": The asset_part_slug of the second property's asset part (null if no match).
           - "title": A concise, catchy title for this section comparison.
           - "comparison": A detailed narrative (100-150 words) of similarities and differences.
           - "improvement_suggestions": Object with "first_property" and "second_property" keys, each containing 2-3 improvement ideas for that section.
           - "maintenance_concerns": Object with "first_property" and "second_property" keys, each containing potential maintenance issues.

        3. **property_details**: Detailed information about each property:
           - "first_property": Object containing:
              - "id": ID from input
              - "strengths": Array of 5-7 specific strengths
              - "weaknesses": Array of 5-7 specific weaknesses
              - "unique_selling_points": Array of 3-5 standout features
              - "potential_issues": Array of 3-5 concerns or red flags
              - "estimated_renovation_costs": String with cost estimate range if renovations needed
              - "lifestyle_fit": Description of lifestyle this property suits
              - "neighbourhood_overview": Description of the neighbourhood
           - "second_property": Same structure as first_property

        4. **market_analysis**:
           - "price_competitiveness": Object with "first_property" and "second_property" keys, each analyzing price vs. market value
           - "future_value_projection": Object with "first_property" and "second_property" keys, each with 5-year outlook
           - "neighborhood_trends": Object with "first_property" and "second_property" keys, each with area development insights

        5. **recommendation**:
           - "preferred_property": Which property you recommend ("first", "second", or "depends_on_buyer")
           - "reasoning": Detailed explanation of your recommendation (100-150 words)
           - "buyer_considerations": Key factors that might influence the decision

        Use the input JSON data without altering raw data. Ensure all IDs and slugs are preserved exactly as in the input. Return valid JSON only.

        Now, please process the following input JSON and return the transformed JSON object:

        ```json
        #{input_json_string}
            ```
            '
      PROMPT
      puts '[SideBySideSectionsAnalyser#build_prompt] Prompt constructed.'
      prompt
    end

    # Extracts and parses JSON from the LLM's response body.
    # The LLM might wrap the JSON in markdown code blocks (```json ... ```).
    # @param response_body [String] The raw response string from the LLM.
    # @return [Hash, nil] The parsed JSON object, or nil if parsing fails.
    def extract_json_from_llm_response(response_body)
      puts '[SideBySideSectionsAnalyser#extract_json_from_llm_response] Attempting to extract JSON from LLM response.'
      # puts "[SideBySideSectionsAnalyser#extract_json_from_llm_response] Raw response body (first 200 chars): #{response_body.to_s.truncate(200)}"

      # Attempt to extract JSON if it's wrapped in markdown code blocks.
      json_match = response_body.to_s.match(/```(?:json)?\s*(.*?)```/m)
      json_string = json_match ? json_match[1] : response_body.to_s
      # puts "[SideBySideSectionsAnalyser#extract_json_from_llm_response] String after regex: #{json_string.truncate(200)}"

      # Strip leading/trailing whitespace.
      json_string = json_string.strip
      # puts "[SideBySideSectionsAnalyser#extract_json_from_llm_response] String after strip: #{json_string.truncate(200)}"

      # (Commented out) Previous gsub was causing syntax errors with empty char-class.
      # The primary goal is to handle markdown blocks, which the regex above does.
      # Further cleaning might be needed if LLMs add extraneous text outside JSON blocks
      # without markdown, but `JSON.parse` itself is somewhat tolerant.
      # json_string = json_string.gsub(/^[^{[]+/, '').gsub(/[^}\]]+$/, '')

      puts '[SideBySideSectionsAnalyser#extract_json_from_llm_response] Attempting to parse JSON string...'
      parsed_json = JSON.parse(json_string)
      puts '[SideBySideSectionsAnalyser#extract_json_from_llm_response] JSON parsed successfully.'
      parsed_json
    rescue JSON::ParserError => e
      # Log parsing errors and the problematic response body for debugging.
      log_error('Failed to parse JSON from LLM response', e)
      log_debug("Response body was: #{response_body}") # Log full body on error
      puts "[SideBySideSectionsAnalyser#extract_json_from_llm_response] JSON::ParserError: #{e.message}. Response body was: #{response_body.truncate(500)}"
      nil
    end

    # Logs an error message along with exception details to Rails logger.
    # @param message [String] The custom error message.
    # @param error [Exception] The exception object.
    def log_error(message, error)
      # This method primarily uses Rails.logger, so puts statements here might be redundant
      # unless specific console output during development is desired.
      # puts "[ERROR] #{self.class.name}: #{message} - #{error.message}"
      Rails.logger.error("#{self.class.name}: #{message} - #{error.message}")
      Rails.logger.error(error.backtrace.join("\n")) if error.respond_to?(:backtrace) && error.backtrace
    end

    # Logs debug information to Rails logger.
    # @param message [String] The debug message.
    def log_debug(message)
      # Similar to log_error, this uses Rails.logger.
      # puts "[DEBUG] #{self.class.name}: #{message}"
      Rails.logger.debug("#{self.class.name}: #{message}")
    end

    # Validates the transformed JSON against an expected schema (basic validation here).
    # @param json [Hash] The JSON object to validate.
    # @return [Boolean] True if the JSON is valid according to the defined checks, false otherwise.
    def validate_transformed_json(json)
      puts "[SideBySideSectionsAnalyser#validate_transformed_json] Validating JSON: #{json.class}"
      # Check if it's a hash.
      unless json.is_a?(Hash)
        puts '[SideBySideSectionsAnalyser#validate_transformed_json] Validation failed: Not a Hash.'
        return false
      end

      # Basic structure validation: check for presence of top-level keys.
      # This list of keys seems outdated or mismatched with the prompt's expected output structure.
      # The prompt expects keys like 'overall_property_comparison', 'rooms_or_sections_comparisons', etc.
      # 'first_property' and 'second_property' are sub-keys within other sections.
      # 'comparison' is also not a top-level key as per the prompt.
      # Adjusting this to reflect the prompt's structure is advisable for meaningful validation.
      # For now, I will keep the original keys for this exercise, but note this discrepancy.
      required_keys = %w[first_property second_property comparison] # Mismatch with prompt
      # Example of more accurate required keys based on prompt:
      # required_keys = %w[overall_property_comparison rooms_or_sections_comparisons property_details market_analysis recommendation]

      all_keys_present = required_keys.all? do |key|
        is_present = json.key?(key)
        puts "[SideBySideSectionsAnalyser#validate_transformed_json] Checking for key '#{key}': #{is_present ? 'Found' : 'Missing'}"
        is_present
      end

      unless all_keys_present
        puts '[SideBySideSectionsAnalyser#validate_transformed_json] Validation failed: One or more required keys are missing.'
        return false
      end

      # Further more detailed validation (e.g., types of values, nested structures) could be added here.
      # For example, checking if 'overall_property_comparison' is a Hash,
      # or if 'rooms_or_sections_comparisons' is an Array.

      puts '[SideBySideSectionsAnalyser#validate_transformed_json] JSON passed basic validation.'
      true
    rescue StandardError => e
      # Log errors encountered during validation.
      log_error('JSON validation failed', e)
      puts "[SideBySideSectionsAnalyser#validate_transformed_json] Error during validation: #{e.message}"
      false
    end
  end
end
