module Ai::PropertyInsights
  class LlmDossierComparableListingsEvaluator
    def initialize(client_to_use: nil)
      @llm_api_service = Ai::LangchainService.new(client_to_use: client_to_use)
    end

    #   # this gets called from RealtyDossier.last.evaluate_against_comparable_listings
    def evaluate(dossier_item)
      dossier_sale_listing = dossier_item.primary_sale_listing
      # batch_key = dossier_sale_listing.postal_code.parameterize
      # comparable_listing_examples_as_features = comparable_listing_examples.map { |v| features(v) }
      prompt_content = build_prompt(dossier_item, dossier_sale_listing)

      sec_asset_ids = dossier_item.secondary_dossier_assets.pluck(:id).to_s.parameterize
      # Time.now.strftime('%Y%m') is only month so should be valid for the entire month...
      cassette_name = "dossier_#{dossier_item.id}_v_#{sec_asset_ids}_#{Time.now.strftime('%Y%m')}"
      # "llm_comparable_listings_evaluation_2025_#{batch_key}"

      # spec/cassettes/listing_evaluations/llm_listing_evaluation_jan_2025_e_gemini-2_0-flash-001.yml
      llm_interaction = @llm_api_service.call_llm_api(
        prompt_content, use_vcr: true, cassette_name: cassette_name,
                        cassette_library_dir: 'spec/cassettes/dossier_v_multiple_comparables'
        # 1 apr - renamed cassette dir
        # cassette_library_dir: 'spec/cassettes/listing_evaluations'
      )

      interaction_assoc = LiaToEvalComparableListings.find_or_create_from_llm_interaction(
        llm_interaction, dossier_sale_listing, dossier_item
      )
      interaction_assoc
    end

    private

    def build_prompt(dossier_item, listing_to_evaluate)
      # wonder if I should put info about dossier_search_to_use in the prompt..
      property_details = extract_property_details(listing_to_evaluate)
      <<~PROMPT
        You are a real estate expert with extensive knowledge of property valuation and market trends. Your task is to evaluate a for-sale property listing based on recent comparable listings in the same area and provide a structured JSON response.

        #{get_main_listing_details(property_details)}

        ### Similar property listings in the Area:
        #{dossier_item.secondary_dossier_assets.map.with_index { |example, index| format_comparable_listings_examples(example, index + 1) }.join("\n")}

        ### Evaluation Instructions:
        Please evaluate the main for-sale listing by comparing it to the provided comparable listings. Consider the following factors:
        - Similarity in location (city, neighborhood, or proximity based on latitude/longitude).
        - Similarity in property characteristics (bedrooms, bathrooms, property type, plot area, year built).
        - Price differences and market trends.
        - Unique features or conditions mentioned in the description.


        Provide your evaluation in a JSON object with the following structure:
        {
          "catchy_title": string, // A catchy title for the property for sale
          "description_short": string, // A short description of the property for sale
          "description_long": string, // A detailed description of the property for sale
          "description_bullet_points": array of strings, // Bullet points summarizing the property for sale
          "id_of_most_similar_comparable_listing": integer, // The ID of the most similar comparable listing
          "uuid_of_most_similar_comparable_listing": string, // The UUID of the most similar comparable listing
          "estimated_fair_value_price": float, // Your estimated fair value price in GBP
          "is_asking_price_competitive_or_overpriced": string, // "competitive", "overpriced", or "underpriced"
          "reasoning_content": {
            "price_comparison": string, // Explain how the asking price compares to others
            "strong_points_about_the_property": array of strings, // Mention any unique features or selling points including about the neighborhood
            "weak_points_about_the_property": array of strings, // Mention any drawbacks or areas of improvement
            "missing_data_assumptions": string // Explain any assumptions made due to missing data
            "questions_to_ask_the_seller": array of strings, // Suggest questions to ask the seller for more information
            "additional_insights": string // Share any additional insights or market trends
            "negotiation_tips": string // Provide tips for negotiating the price
            "ideal_buyer_profile": string // Describe the ideal buyer for this property
          },
          "comparable_listings_analysis": [
            {
              "id": "integer", // ID of this comparable listing
              "uuid": "string", // UUID of this comparable listing
              "address": "string", // Address of this comparable listing for reference
              "relative_strengths": ["string"], // Features where this comparable is superior to the main listing
              "relative_weaknesses": ["string"], // Features where this comparable is inferior to the main listing
              "price_difference_percentage": "float", // Price difference percentage compared to main listing (positive if more expensive)
              "similarity_score": "float", // A score from 0-10 indicating overall similarity to the main listing
              "key_differentiators": ["string"], // Most important differences between this comparable and the main listing
              "value_insights": "string" // What this comparable suggests about the main listings value
           }
          ]
        }

        ### Additional Notes:
        - If no similar comparable listings are found, set the ID and UUID fields to null and provide a general estimate based on the listing details.
        - If any data is missing (e.g., price or bedrooms), use reasonable assumptions and note them in the reasoning.
        - Ensure all monetary values are in GBP (£).
        - Use language that communicates the this response is not a formal appraisal and may be prone to mistakes. Words like 'seem' should be preffered over words like 'is'.
        - In the reasoning_content section, do not refer to specific examples or comparable listings by ID or UUID. Use the address or a description instead.

        Please respond with the JSON object only, without any additional text or explanations outside the JSON.

      PROMPT
    end

    def get_main_listing_details(property_details)
      <<~MAINLISTING
        ### Main For-Sale Listing Details:
        - **Title**: #{property_details[:title]}
        - **City**: #{property_details[:city]}
        - **Address**: #{property_details[:street_address]}
        - **Bedrooms**: #{property_details[:bedrooms]}
        - **Bathrooms**: #{property_details[:bathrooms]}
        - **Asking Price**: #{property_details[:price]}
        - **Property Type**: #{property_details[:property_type]}
        - **Plot Area**: #{property_details[:plot_area] || 'N/A'} square meters
        - **Year Built**: #{property_details[:year_construction] || 'N/A'}
        - **Latitude**: #{property_details[:latitude] || 'N/A'}
        - **Longitude**: #{property_details[:longitude] || 'N/A'}
        - **Description**: #{property_details[:description]}
      MAINLISTING
    end

    # Extract relevant details from the realty asset
    def extract_property_details(listing_to_evaluate)
      {
        title: listing_to_evaluate.realty_asset.title || 'N/A',
        city: listing_to_evaluate.city || 'N/A',
        street_address: listing_to_evaluate.street_address || 'N/A',
        bedrooms: listing_to_evaluate.count_bedrooms || 0,
        bathrooms: listing_to_evaluate.count_bathrooms || 0,
        price: listing_to_evaluate.formatted_display_price,
        # property_type: listing_to_evaluate.prop_type_key || 'N/A',
        description: listing_to_evaluate.realty_asset.description || 'N/A'
        # description: listing_to_evaluate.description || 'No description available'
      }
    end

    # def format_comparable_listings_examples(dossier_listing, index)
    def format_comparable_listings_examples(dossier_asset, index)
      listing = dossier_asset.default_sale_listing
      <<~EXAMPLE
        Example #{index}:
          Id: #{listing.id}
          Uuid: #{listing.uuid}
          Reference: #{listing.reference}
          Postal Code: #{listing.postal_code}
          Address: #{listing.street_address}
          Age of Property: #{listing.year_construction > 0 ? (Time.now.year - listing.year_construction) : 'Unknown'}
          Total Floor Area: #{listing.constructed_area} #{listing.area_unit}
          Plot Area: #{listing.plot_area} #{listing.area_unit}
          Number of Bedrooms: #{listing.count_bedrooms}
          Number of Bathrooms: #{listing.count_bathrooms}
          Number of Toilets: #{listing.count_toilets}
          Number of Garages: #{listing.count_garages}
          Construction Year: #{listing.year_construction > 0 ? listing.year_construction : 'Unknown'}
          City: #{listing.city}
          Region: #{listing.region}
          Country: #{listing.country}
          Title: #{listing.title}
          Description: #{listing.description}
          Price: #{listing.formatted_display_price || (listing.price_sale_current_cents / 100.0).round(2)}
      EXAMPLE
    end
  end
end
