module Ai::PropertyInsights
  class LlmPricePredictionModel
    def initialize
      @llm_api_service = Ai::LlmApiService.new
    end

    def predict(training_examples, properties_to_predict, batch_key)
      batch_key ||= 'a'
      training_examples_as_features = training_examples.map { |v| features(v) }
      prompt_template = create_prompt_template(training_examples_as_features, properties_to_predict)

      cassette_name = "llm_price_prediction_jan_2025_#{batch_key}"
      json_response = @llm_api_service.call_llm_api(
        prompt_template, use_vcr: true, cassette_name: cassette_name,
                         cassette_library_dir: 'spec/cassettes/price_predictions'
      )

      puts json_response[0]
      json_response
    end

    private

    def create_prompt_template(training_examples, properties_to_predict)
      # Generate prompt for all properties
      property_prompts = properties_to_predict.map.with_index do |property, index|
        features_hash = features(property)
        <<~PROPERTY
          --- Property #{index + 1} Details ---
          Id: #{features_hash[:id]}
          Uuid: #{features_hash[:uuid]}
          Tenure: #{features_hash[:st_tenure]}
          Property Type: #{features_hash[:st_property_type]}
          Postal Code: #{features_hash[:st_postal_code]}
          Age of Property: #{features_hash[:st_age_of_property]}
          Total Floor Area: #{features_hash[:total_floor_area]}
          Number of Heated Rooms: #{features_hash[:number_heated_rooms]}
          Number of Habitable Rooms: #{features_hash[:number_habitable_rooms]}
          Construction Age Band: #{features_hash[:construction_age_band]}
          Current Energy Rating: #{features_hash[:current_energy_rating]}
          Sold Year: #{features_hash[:sold_year]}
          Sold Month and Year: #{features_hash[:sold_month_and_year]}
          Sold Date: #{features_hash[:sold_date]}
        PROPERTY
      end.join("\n")

      <<~TEMPLATE
        You are an expert real estate evaluator. I will provide you with examples of property sales and their prices, followed by details of multiple properties. Please predict the sold price in sterling for each new property and provide a brief explanation of how you arrived at each price.
        Do not explicitly reference other properties in the explanation but do provide the id and uuid of the closest property in the examples set.

        --- Examples of Property Sales ---
        #{training_examples.map.with_index { |example, index| format_example(example, index + 1) }.join("\n")}

        #{property_prompts}

        Please respond with an array of JSON objects, each with the following format for each property:
        {
          "property_id": ,
          "property_uuid": ,
          "predicted_price": ,
          "id_of_most_similar_property": ,
          "uuid_of_most_similar_property": ,
          "explanation": ""
        }
      TEMPLATE
    end

    def format_example(example, index)
      <<~EXAMPLE
        Example #{index}:
          Tenure: #{example[:st_tenure]}
          Id: #{example[:id]}
          Uuid: #{example[:uuid]}
          Property Type: #{example[:st_property_type]}
          Postal Code: #{example[:st_postal_code]}
          Age of Property: #{example[:st_age_of_property]}
          Total Floor Area: #{example[:total_floor_area]}
          Number of Heated Rooms: #{example[:number_heated_rooms]}
          Number of Habitable Rooms: #{example[:number_habitable_rooms]}
          Construction Age Band: #{example[:construction_age_band]}
          Current Energy Rating: #{example[:current_energy_rating]}
          Sold Year: #{example[:sold_year]}
          Sold Month and Year: #{example[:sold_month_and_year]}
          Sold Date: #{example[:sold_date]}
          Sold Price: #{example[:sold_price_sterling].round(2)}
      EXAMPLE
    end

    def features(s_transaction_with_epc)
      sold_date = s_transaction_with_epc.sold_date
      {
        id: s_transaction_with_epc.id,
        uuid: s_transaction_with_epc.uuid,
        st_tenure: s_transaction_with_epc.st_tenure || 'Missing',
        st_property_type: s_transaction_with_epc.st_property_type || 'Missing',
        st_postal_code: s_transaction_with_epc.st_postal_code || 'Missing',
        st_age_of_property: s_transaction_with_epc.st_age_of_property || 'Missing',
        total_floor_area: s_transaction_with_epc.total_floor_area || 'Missing',
        number_heated_rooms: s_transaction_with_epc.number_heated_rooms || 'Missing',
        number_habitable_rooms: s_transaction_with_epc.number_habitable_rooms,
        construction_age_band: s_transaction_with_epc.construction_age_band || 'Missing',
        current_energy_rating: s_transaction_with_epc.current_energy_rating || 'Missing',
        sold_year: sold_date&.strftime('%Y') || 'Missing',
        sold_month_and_year: sold_date&.strftime('%m/%Y') || 'Missing',
        sold_date: sold_date&.strftime || 'MissingDate',
        sold_price_sterling: s_transaction_with_epc.sold_price_cents.to_f / 100.0
      }
    end
  end
end
