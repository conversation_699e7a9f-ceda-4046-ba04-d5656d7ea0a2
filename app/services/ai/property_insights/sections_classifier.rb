module Ai::PropertyInsights
  class SectionsClassifier
    DEFAULT_CLIENT = 'gemini-text'.freeze # Text-based LLM
    CASSETTE_DIR = 'spec/cassettes/sections_classifications'.freeze

    def initialize(client_to_use: nil)
      @llm_api_service = Ai::RubyLlmService.new(client_to_use: client_to_use || DEFAULT_CLIENT)
    end

    # Classifies rooms based on photo descriptions
    # @param dossier_item [DossierItem] The dossier item being processed.
    # @param target_dossier_asset [DossierAsset] The asset whose photos are being classified.
    # @param discard_existing_parts [Boolean] If true, deletes existing DossierAssetParts before classification.
    def classify_rooms(dossier_item, target_dossier_asset, discard_existing_parts: false)
      # Discard existing parts if requested
      if discard_existing_parts
        Rails.logger.info "Discarding existing DossierAssetParts for DossierAsset #{target_dossier_asset.id}"
        target_dossier_asset.dossier_asset_parts.each(&:discard!)
      end

      image_descriptions_data = target_dossier_asset.default_sale_listing.image_descriptions || {}
      classification = process_descriptions(image_descriptions_data, dossier_item, target_dossier_asset)
      classification
    rescue StandardError => e
      log_error("Failed to classify rooms for dossier #{dossier_item.id}", e)
      nil
    end

    private

    # Processes descriptions to classify rooms
    def process_descriptions(image_descriptions_data, dossier_item, target_dossier_asset)
      return nil unless image_descriptions_data&.length&.positive?

      session_id = SecureRandom.uuid
      prompt_content = build_prompt(image_descriptions_data, dossier_item.id, target_dossier_asset, session_id)
      cassette_name = "dossier_#{dossier_item.id}_asset_#{target_dossier_asset.id}_classification"

      llm_interaction = @llm_api_service.call_llm_api_text(
        prompt_content,
        use_vcr: true,
        cassette_name: cassette_name,
        cassette_library_dir: CASSETTE_DIR
      )

      if llm_interaction.has_errored?
        log_error("LLM error: #{llm_interaction.llm_error_message}", StandardError.new('LLM failed'))
        nil
      else
        validate_and_create_classification(llm_interaction, dossier_item, target_dossier_asset)
      end
    rescue StandardError => e
      log_error('Room classification failed', e)
      nil
    end

    # Builds the prompt for LLM to classify rooms from descriptions
    def build_prompt(image_descriptions_data, dossier_item_id, target_dossier_asset, session_id)
      asset_part_types = DossierAssetPart.asset_part_types.keys.map { |k| "- #{k}" }.join("\n           ")

      descriptions = image_descriptions_data.map do |desc|
        <<~DESC
          - **Image ID**: #{desc['image_id']}
            - **Catchy Title**: #{desc['catchy_title']}
            - **General Description**: #{desc['general_description']}
            - **Style**: #{desc['style']}
            - **Dominant Color**: #{desc['dominant_color']}
            - **Significant Items**: #{desc['significant_items']&.join(', ') || ''}
            - **Condition**: #{desc['condition']}
            - **Unique Features**: #{desc['unique_features']&.join(', ') || ''}
            - **Estimated Area**: #{desc['estimated_area_sq']}
            - **Is Outside**: #{desc['is_outside']}
        DESC
      end.join("\n")

      <<~PROMPT
        You are an expert in real estate analysis. This is a standalone request with session ID #{session_id}. Your task is to analyze textual descriptions of property photos and identify unique rooms or sections, assigning consistent identifiers and classifying them by type. Additionally, you will provide a suggested ordering for the photos, prioritizing images that show the whole property. Finally, you will generate a title and description for the entire property for sale.

        The dossier_item_id is #{dossier_item_id} and the target_dossier_asset_id is #{target_dossier_asset.id}. Return these in the response.
        The only valid image_ids are #{target_dossier_asset.asset_photo_short_ids}. Ignore any other image_ids.

        ### Property For Sale Description:
        #{target_dossier_asset.initial_listing_summary_for_ai}

        ### Photo Descriptions:
        #{descriptions}

        ### Analysis Instructions:
        1. **Generate Property Title and Description**:
           - **ai_gen_title**: Create a concise, catchy title for the entire property (max 60 characters) that highlights its key appeal (e.g., "Modern Coastal Retreat").
           - **ai_gen_description**: Write a compelling description of the entire property (100-150 words) summarizing its key features, style, and unique selling points based on the provided descriptions and initial listing summary. Highlight aspects like architecture, condition, standout rooms, or outdoor features.

        2. **Identify Unique Rooms or Sections**:
           - Based on the descriptions, identify distinct rooms or sections using features like furniture, decor, or architectural elements.
           - Assign a consistent 'section_or_room_identifier' (e.g., 'kitchen', 'bedroom 1') based on the 'section_or_room_type'. Use numeric suffixes for multiple rooms of the same type (e.g., 'bedroom 1', 'bedroom 2') if descriptions indicate differences (e.g., size, decor).
           - For each unique room, list the 'image_ids' where it appears based on matching features.
           - To determine if descriptions refer to the same room, look for:
             - Similar furniture or appliances (e.g., "blue island" in multiple descriptions).
             - Matching architectural features (e.g., "large bay window").
             - Consistent colors or styles.
           - For each unique room, provide:
             - **Section or Room Type**: One of:
               #{asset_part_types}
             - **Section or Room Identifier**: As described.
             - **Image IDs**: Array of image IDs where the room appears.
             - **Summary Description**: A brief summary based on the descriptions.

        3. **Analyze Each Description**:
           - For each description, list the 'section_or_room_identifier's of visible rooms. Indicate which is the main focus with 'is_main_section_or_room' (only one per description can be true).

        4. **Suggest Photo Ordering**:
           - Provide a suggested order for all valid image_ids, prioritizing images that show the whole property (e.g., exterior shots, aerial views, or images marked as 'is_outside': true).
           - Criteria for prioritization (in descending order):
             - Images with 'is_outside': true, especially those described as showing the full property or facade.
             - Images with larger 'estimated_area_sq', indicating broader coverage.
             - Images with unique features or high-quality descriptions (e.g., good condition, distinctive style).
           - Ensure all valid image_ids are included exactly once in the ordering.

        ### Response Format:
        {
          "dossier_item_id": "string",
          "target_dossier_asset_id": "string",
          "ai_gen_title": "string",
          "ai_gen_description": "string",
          "unique_rooms_or_sections": [
            {
              "section_or_room_type": "string",
              "section_or_room_identifier": "string",
              "image_ids": ["string"],
              "summary_description": "string"
            }
          ],
          "sub_images": [
            {
              "image_id": "string",
              "sections_or_rooms_in_photo": [
                {
                  "section_or_room_identifier": "string",
                  "is_main_section_or_room": boolean
                }
              ]
            }
          ],
          "suggested_photo_order": ["string"]
        }

        ### Guidelines:
        - Use only the provided descriptions, not external data.
        - Ensure consistent identifiers across descriptions for the same room.
        - Only one 'is_main_section_or_room' should be true per description.
        - Use "other" for unidentifiable sections with an explanation in 'summary_description'.
        - Ensure all valid image_ids appear exactly once in 'suggested_photo_order'.
        - Return only valid JSON without additional text.
      PROMPT
    end

    # Validates response and creates classification
    def validate_and_create_classification(llm_interaction, dossier_item, target_dossier_asset)
      response_data = JSON.parse(llm_interaction.chosen_response)
      valid_image_ids = target_dossier_asset.asset_photo_short_ids
      valid_room_types = DossierAssetPart.asset_part_types.keys

      unique_rooms = response_data['unique_rooms_or_sections'] || []
      invalid_image_ids = unique_rooms.flat_map { |room| room['image_ids'] }.uniq.map(&:to_i) - valid_image_ids
      invalid_room_types = unique_rooms.map { |room| room['section_or_room_type'] } - valid_room_types

      if invalid_image_ids.any?
        log_error("Invalid image IDs: #{invalid_image_ids.join(', ')}", StandardError.new('Invalid image IDs'))
        return nil
      elsif invalid_room_types.any?
        puts "Invalid room types: #{invalid_room_types.join(', ')}"
        # sometimes LLM will return a room type that is not in the list of valid room types
        # 13 may 2025 - decided not to treat this as an error
      end

      DossierInfoSourceFromCompositePhoto.find_or_create_from_ai_interaction(
        llm_interaction, dossier_item, target_dossier_asset
      )
    rescue JSON::ParserError => e
      log_error('Invalid JSON response', e)
      nil
    end

    # Logs error with a formatted message
    def log_error(message, exception)
      puts "#{message}: #{exception.message}"
      puts "Backtrace: #{exception.backtrace.join("\n")}"
      Rails.logger.error("#{message}: #{exception.message}")
    end
  end
end
