# require 'openai'
# require 'json'

module Ai::PropertyInsights
  class LlmSalesOutlierFinder
    def initialize
      @llm_api_service = Ai::LlmApiService.new
    end

    def find_outliers(training_examples, batch_key)
      batch_key ||= 'a'
      training_examples_as_features = training_examples.map { |v| features(v) }
      prompt_template = create_prompt_template(training_examples_as_features)

      cassette_name = "llm_sales_outliers_jan_2025_#{batch_key}"
      json_response = @llm_api_service.call_llm_api(
        prompt_template, use_vcr: true, cassette_name: cassette_name,
                         cassette_library_dir: 'spec/cassettes/price_predictions'
      )

      puts json_response[0]
      # Return the json_response as JSON string
      json_response
    end

    private

    def create_prompt_template(training_examples)
      <<~TEMPLATE
        You are an expert real estate evaluator. I will provide you with examples of property sales and their prices. Please find outliers among the examples and provide a brief explanation of what makes each one an outlier.

        --- Property Sales Data ---
        #{training_examples.map.with_index { |example, index| format_example(example, index + 1) }.join("\n")}


        Please respond with an array of JSON objects, each with the following format for each property:
        {
          "id_of_outlier": ,
          "uuid_of_outlier": ,
          "explanation": ""
        }
      TEMPLATE
    end

    def format_example(example, index)
      <<~EXAMPLE
        Example #{index}:
          Tenure: #{example[:st_tenure]}
          Id: #{example[:id]}
          Uuid: #{example[:uuid]}
          Property Type: #{example[:st_property_type]}
          Postal Code: #{example[:st_postal_code]}
          Age of Property: #{example[:st_age_of_property]}
          Total Floor Area: #{example[:total_floor_area]}
          Number of Heated Rooms: #{example[:number_heated_rooms]}
          Number of Habitable Rooms: #{example[:number_habitable_rooms]}
          Construction Age Band: #{example[:construction_age_band]}
          Current Energy Rating: #{example[:current_energy_rating]}
          Sold Year: #{example[:sold_year]}
          Sold Month and Year: #{example[:sold_month_and_year]}
          Sold Date: #{example[:sold_date]}
          Sold Price: #{example[:sold_price_sterling].round(2)}
      EXAMPLE
    end

    def features(s_transaction_with_epc)
      sold_date = s_transaction_with_epc.sold_date
      {
        id: s_transaction_with_epc.id,
        uuid: s_transaction_with_epc.uuid,
        st_tenure: s_transaction_with_epc.st_tenure || 'Missing',
        st_property_type: s_transaction_with_epc.st_property_type || 'Missing',
        st_postal_code: s_transaction_with_epc.st_postal_code || 'Missing',
        st_age_of_property: s_transaction_with_epc.st_age_of_property || 'Missing',
        total_floor_area: s_transaction_with_epc.total_floor_area || 'Missing',
        number_heated_rooms: s_transaction_with_epc.number_heated_rooms || 'Missing',
        number_habitable_rooms: s_transaction_with_epc.number_habitable_rooms,
        construction_age_band: s_transaction_with_epc.construction_age_band || 'Missing',
        current_energy_rating: s_transaction_with_epc.current_energy_rating || 'Missing',
        sold_year: sold_date&.strftime('%Y') || 'Missing',
        sold_month_and_year: sold_date&.strftime('%m/%Y') || 'Missing',
        sold_date: sold_date&.strftime || 'MissingDate',
        sold_price_sterling: s_transaction_with_epc.sold_price_cents.to_f / 100.0
      }
    end
  end
end
