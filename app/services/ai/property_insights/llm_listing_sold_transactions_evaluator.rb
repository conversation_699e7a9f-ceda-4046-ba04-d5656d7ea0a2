module Ai::PropertyInsights
  class LlmListingSoldTransactionsEvaluator
    # might rename this to LlmDossierSoldTransactionsEvaluator llm_dossier_sold_transactions_evaluator
    #
    def initialize(client_to_use: nil)
      # @llm_api_service = Ai::LlmApiService.new(client_to_use: client_to_use)
      # @llm_api_service = Ai::LlmApiServiceForGemini.new(client_to_use: client_to_use)
      @llm_api_service = Ai::LangchainService.new(client_to_use: client_to_use)
    end

    def evaluate(sold_transaction_examples, dossier_item)
      dossier_sale_listing = dossier_item.primary_sale_listing
      batch_key = 'e'
      sold_transaction_examples_as_features = sold_transaction_examples.map { |v| features(v) }
      prompt_content = build_prompt(sold_transaction_examples_as_features, dossier_sale_listing)

      cassette_name = "llm_listing_evaluation_jan_2025_#{batch_key}"
      # spec/cassettes/listing_evaluations/llm_listing_evaluation_jan_2025_e_gemini-2_0-flash-001.yml
      llm_interaction = @llm_api_service.call_llm_api(
        prompt_content, use_vcr: true, cassette_name: cassette_name,
                        cassette_library_dir: 'spec/cassettes/listing_evaluations'
      )

      # llm_interaction.update(
      #   # llm_interaction_code: 'primary_listing_evaluation',
      #   is_sale_listing_interaction: true,
      #   sale_listing_uuid: dossier_sale_listing.uuid
      # )
      # # Above creates an instance of LlmInteractionWithSaleListing
      # interaction_with_sl = LlmInteractionWithSaleListing.find_by(
      #   uuid: llm_interaction.uuid
      # )
      # dossier_sale_listing.update_new_llm_interaction(interaction_with_sl)
      # interaction_with_sl.store_sale_listing_data
      # March 2025 - above being replaced by the below
      interaction_assoc = LiaToEvalRecentSales.find_or_create_from_llm_interaction(
        llm_interaction, dossier_sale_listing, dossier_item
      )
      interaction_assoc
    end

    private

    def build_prompt(sold_transaction_examples, listing_to_evaluate)
      property_details = extract_property_details(listing_to_evaluate)

      <<~PROMPT
        You are a real estate expert with extensive knowledge of property valuation and market trends. Your task is to evaluate a for-sale property listing based on recent sold transactions in the same area and provide a structured JSON response.

        ### For-Sale Listing Details:
        - **Title**: #{property_details[:title]}
        - **City**: #{property_details[:city]}
        - **Address**: #{property_details[:street_address]}
        - **Bedrooms**: #{property_details[:bedrooms]}
        - **Bathrooms**: #{property_details[:bathrooms]}
        - **Asking Price**: #{property_details[:price]}
        - **Property Type**: #{property_details[:property_type]}
        - **Plot Area**: #{property_details[:plot_area] || 'N/A'} square meters
        - **Year Built**: #{property_details[:year_construction] || 'N/A'}
        - **Latitude**: #{property_details[:latitude] || 'N/A'}
        - **Longitude**: #{property_details[:longitude] || 'N/A'}
        - **Description**: #{property_details[:description]}

        ### Recent Sold Transactions:
        #{sold_transaction_examples.map.with_index { |example, index| format_recent_sale_examples(example, index + 1) }.join("\n")}

        ### Evaluation Instructions:
        Please evaluate the for-sale listing by comparing it to the provided sold transactions. Consider the following factors:
        - Similarity in location (city, neighborhood, or proximity based on latitude/longitude).
        - Similarity in property characteristics (bedrooms, bathrooms, property type, plot area, year built).
        - Price differences and market trends.
        - Unique features or conditions mentioned in the description.


        Provide your evaluation in a JSON object with the following structure:
        {
          "catchy_title": string, // A catchy title for the property for sale
          "description_short": string, // A short description of the property for sale
          "description_medium": string, // A medium-length description of the property for sale
          "description_long": string, // A detailed description of the property for sale
          "description_bullet_points": array of strings, // Bullet points summarizing the property for sale
          "id_of_most_similar_sold_transaction": integer, // The ID of the most similar sold transaction
          "uuid_of_most_similar_sold_transaction": string, // The UUID of the most similar sold transaction
          "ids_of_other_similar_sold_transactions": array of integers, // IDs of other similar transactions
          "uuids_of_other_similar_sold_transactions": array of strings, // UUIDs of other similar transactions
          "estimated_fair_value_price": float, // Your estimated fair value price in GBP
          "is_asking_price_competitive_or_overpriced": string, // "competitive", "overpriced", or "underpriced"
          "reasoning_content": {
            "price_comparison": string, // Explain how the asking price compares to recent sales
            "strong_points_about_the_property": array of strings, // Mention any unique features or selling points including about the neighborhood
            "weak_points_about_the_property": array of strings, // Mention any drawbacks or areas of improvement
            "missing_data_assumptions": string // Explain any assumptions made due to missing data
            "questions_to_ask_the_seller": array of strings, // Suggest questions to ask the seller for more information
            "additional_insights": string // Share any additional insights or market trends
            "negotiation_tips": string // Provide tips for negotiating the price
            "ideal_buyer_profile": string // Describe the ideal buyer for this property
          }
        }

        ### Additional Notes:
        - If no similar transactions are found, set the ID and UUID fields to null and provide a general estimate based on the listing details.
        - If any data is missing (e.g., price or bedrooms), use reasonable assumptions and note them in the reasoning.
        - Ensure all monetary values are in GBP (£).
        - Use language that communicates the this response is not a formal appraisal and may be prone to mistakes. Words like 'seem' should be preffered over words like 'is'.
        - In the reasoning_content section, do not refer to specific examples or transactions by ID or UUID. Use the address or a description instead.

        Please respond with the JSON object only, without any additional text or explanations outside the JSON.

      PROMPT
    end

    # Extract relevant details from the realty asset
    def extract_property_details(listing_to_evaluate)
      {
        title: listing_to_evaluate.realty_asset.title || 'N/A',
        city: listing_to_evaluate.city || 'N/A',
        street_address: listing_to_evaluate.street_address || 'N/A',
        bedrooms: listing_to_evaluate.count_bedrooms || 0,
        bathrooms: listing_to_evaluate.count_bathrooms || 0,
        price: listing_to_evaluate.formatted_display_price,
        description: listing_to_evaluate.realty_asset.description || 'N/A'
        # description: listing_to_evaluate.description || 'No description available'
      }
    end

    def format_recent_sale_examples(example, index)
      <<~EXAMPLE
        Example #{index}:
          Tenure: #{example[:st_tenure]}
          Id: #{example[:id]}
          Uuid: #{example[:uuid]}
          Property Type: #{example[:st_property_type]}
          Postal Code: #{example[:st_postal_code]}
          Address: #{example[:st_address]}
          Age of Property: #{example[:st_age_of_property]}
          Total Floor Area: #{example[:total_floor_area]}
          Number of Heated Rooms: #{example[:number_heated_rooms]}
          Number of Habitable Rooms: #{example[:number_habitable_rooms]}
          Construction Age Band: #{example[:construction_age_band]}
          Current Energy Rating: #{example[:current_energy_rating]}
          Sold Year: #{example[:sold_year]}
          Sold Month and Year: #{example[:sold_month_and_year]}
          Sold Date: #{example[:sold_date]}
          Sold Price: #{example[:sold_price_sterling].round(2)}
      EXAMPLE
    end

    def features(s_transaction_with_epc)
      sold_date = s_transaction_with_epc.sold_date
      {
        id: s_transaction_with_epc.id,
        uuid: s_transaction_with_epc.uuid,
        st_tenure: s_transaction_with_epc.st_tenure || 'Missing',
        st_property_type: s_transaction_with_epc.st_property_type || 'Missing',
        st_postal_code: s_transaction_with_epc.st_postal_code || 'Missing',
        st_address: s_transaction_with_epc.st_address || 'Missing',
        st_age_of_property: s_transaction_with_epc.st_age_of_property || 'Missing',
        total_floor_area: s_transaction_with_epc.total_floor_area || 'Missing',
        number_heated_rooms: s_transaction_with_epc.number_heated_rooms || 'Missing',
        number_habitable_rooms: s_transaction_with_epc.number_habitable_rooms,
        construction_age_band: s_transaction_with_epc.construction_age_band || 'Missing',
        current_energy_rating: s_transaction_with_epc.current_energy_rating || 'Missing',
        sold_year: sold_date&.strftime('%Y') || 'Missing',
        sold_month_and_year: sold_date&.strftime('%m/%Y') || 'Missing',
        sold_date: sold_date&.strftime || 'MissingDate',
        sold_price_sterling: s_transaction_with_epc.sold_price_cents.to_f / 100.0
      }
    end
  end
end
