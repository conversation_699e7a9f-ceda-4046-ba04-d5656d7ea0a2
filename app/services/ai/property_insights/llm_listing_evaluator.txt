module Ai::PropertyInsights
  class LlmListingEvaluator
    # Default directory for VCR cassettes
    DEFAULT_CASSETTE_DIR = 'spec/cassettes/listing_expansion'.freeze

    # Prompt instructions for LLM
    # Prompt instructions for LLM
    PROMPT_INSTRUCTIONS = <<~INSTRUCTIONS.strip
      You are a real estate expert specializing in property valuation and market analysis.
      Your task is to analyze a for-sale property listing and provide a detailed, structured JSON response based on available data.

      ### Assessment Instructions:
      Analyze the listing details and property photos to identify and describe **every single room and section** of the property.
      For **each and every room or section** (e.g., bedrooms, bathrooms, kitchen, living room, dining room, garage, garden, etc.), include its type, estimated size, unique features, and corresponding photo UUIDs.
      If there is more than 1 room or section of the same type, provide a separate entry for each with a suffix indicating the count such as bedroom_1, bedroom_2, etc..
      Do not omit any room or section that can be inferred from the listing details or photo analysis.
      If a photo cannot be clearly associated with a specific room or section, assign it to an "unknown" section with an explanation in the reasoning_content.
      Additionally, provide an inventory of items in the property. For each item, include the item name, condition, location (the specific room or section it is in), and photo UUIDs if available.
      Set the `item_likely_to_be_worth_over_100` field to `true` if the item seems likely to be worth over £100, and `false` otherwise.

      ### Guidelines:
      - Ensure the `sections_or_rooms_details` object includes **all identifiable rooms and sections** from the listing details and photos, even if details are sparse.
      - Use reasonable assumptions for missing data (e.g., size or type), and note these assumptions in `reasoning_content`.
      - All monetary values must be in GBP (£).
      - Use tentative language (e.g., "seems" rather than "is") as this is not a formal appraisal.
      - Focus on actionable insights for potential buyers.
    INSTRUCTIONS

    # Initialize with an optional LLM client
    # @param [Object, nil] client_to_use - Custom client for LLM service (defaults to nil)
    def initialize(client_to_use: nil)
      @llm_api_service = Ai::LangchainService.new(client_to_use: client_to_use)
    end

    # Evaluates a dossier item and returns its LLM interaction association
    # @param [Object] dossier_item - Dossier item with a primary sale listing
    # @return [Object] LLM interaction association object
    # @raise [ArgumentError] If dossier_item or primary_sale_listing is missing
    # @raise [StandardError] If LLM API call fails
    def evaluate(dossier_item)
      validate_dossier_item(dossier_item)
      dossier_sale_listing = dossier_item.primary_sale_listing
      prompt_content = build_prompt(dossier_item, dossier_sale_listing)
      cassette_name = "listing_from_dossier_#{dossier_item.id}"

      llm_interaction = call_llm_api(prompt_content, cassette_name)
      create_llm_association(llm_interaction, dossier_sale_listing, dossier_item)
    rescue ArgumentError => e
      Rails.logger.error("Validation error: #{e.message}")
      raise
    rescue StandardError => e
      Rails.logger.error("Failed to evaluate listing: #{e.message}")
      raise
    end

    private

    # Validates the dossier item
    # @raise [ArgumentError] If validation fails
    def validate_dossier_item(dossier_item)
      raise ArgumentError, 'Dossier item is required' unless dossier_item
      raise ArgumentError, 'Primary sale listing is missing' unless dossier_item.primary_sale_listing
    end

    # Builds the LLM prompt
    # @param [Object] dossier_item - Dossier item being evaluated
    # @param [Object] listing - Property listing to assess
    # @return [String] Formatted prompt
    def build_prompt(dossier_item, listing)
      property_details = extract_property_details(listing)
      # 23 apr 2025 - now that I use composit_photos, analysed_photos may well get deprecated
      photo_summary = dossier_item.analysed_photos_summary.presence || 'No photo analysis available'

      [
        PROMPT_INSTRUCTIONS,
        "\n### For-Sale Listing Details:\n#{format_property_details(property_details)}",
        "\n### Property Listing Photos Analysis:\n#{photo_summary}",
        "\n### Response Format:\n#{response_format_instructions}"
      ].join("\n")
    end

    # Calls the LLM API with VCR support
    # @param [String] prompt_content - The prompt to send
    # @param [String] cassette_name - Name for VCR cassette
    # @return [Object] LLM interaction result
    def call_llm_api(prompt_content, cassette_name)
      @llm_api_service.call_llm_api(
        prompt_content,
        use_vcr: true,
        cassette_name: cassette_name,
        cassette_library_dir: DEFAULT_CASSETTE_DIR
      )
    end

    # Creates or finds LLM association
    # @param [Object] llm_interaction - LLM API response
    # @param [Object] sale_listing - Sale listing object
    # @param [Object] dossier_item - Dossier item object
    # @return [Object] LLM interaction association
    def create_llm_association(llm_interaction, sale_listing, dossier_item)
      LiaToExpandSingleListing.find_or_create_from_llm_interaction(
        llm_interaction, sale_listing, dossier_item
      )
    end

    # Formats property details
    # @param [Hash] details - Property details hash
    # @return [String] Formatted details
    def format_property_details(details)
      [
        "- **Title**: #{details[:title]}",
        "- **City**: #{details[:city]}",
        "- **Address**: #{details[:street_address]}",
        "- **Bedrooms**: #{details[:bedrooms]}",
        "- **Bathrooms**: #{details[:bathrooms]}",
        "- **Asking Price**: #{details[:price]}",
        "- **Property Type**: #{details[:property_type] || 'N/A'}",
        "- **Plot Area**: #{details[:plot_area] || 'N/A'} square meters",
        "- **Year Built**: #{details[:year_construction] || 'N/A'}",
        "- **Latitude**: #{details[:latitude] || 'N/A'}",
        "- **Longitude**: #{details[:longitude] || 'N/A'}",
        "- **Description**: #{details[:description]}"
      ].join("\n")
    end

    # Defines expected JSON response structure
    # @return [String] JSON format instructions
    def response_format_instructions
      <<~JSON.strip
        {
          "catchy_title": "string",
          "description_short": "string",
          "description_medium": "string",
          "description_long": "string",
          "walk_through_description": "string",
          "description_bullet_points": ["string"],
          "estimated_fair_value_price": float,
          "is_asking_price_competitive_or_overpriced": "competitive|overpriced|underpriced",
          "property_inventory": {
            "item_name": {
              "item_title": "string",
              "item_likely_to_be_sold_with_property": boolean,
              "item_likely_to_be_worth_over_100": boolean,
              "condition": "string",
              "section_or_room_location": "string",
              "photo_uuids": ["string"]
            }
          },
          "sections_or_rooms_details": {
            "section_or_room_name": {
              "type": "string",
              "size": "string",
              "unique_features": ["string"],
              "realty_asset_photo_uuids": ["string"]
            }
          },
          "reasoning_content": {
            "strong_points_about_the_property": ["string"],
            "weak_points_about_the_property": ["string"],
            "missing_data_assumptions": "string",
            "questions_to_ask_the_seller": ["string"],
            "additional_insights": "string",
            "negotiation_tips": "string",
            "ideal_buyer_profile": "string"
          }
        }
      JSON
    end

    # Extracts property details with defaults
    # @param [Object] listing - Property listing object
    # @return [Hash] Property details
    def extract_property_details(listing)
      asset = listing.realty_asset
      {
        title: asset&.title || 'N/A',
        city: listing.city || 'N/A',
        street_address: listing.street_address || 'N/A',
        bedrooms: listing.count_bedrooms || 0,
        bathrooms: listing.count_bathrooms || 0,
        price: listing.formatted_display_price || 'N/A',
        plot_area: asset&.plot_area,
        year_construction: asset&.year_construction,
        latitude: asset&.latitude,
        longitude: asset&.longitude,
        description: asset&.description || 'No description available'
      }
    end
  end
end
