module Ai::PropertyInsights
  # 12 apr 2025: this will replace CompositePhotoAnalyser
  #  which was generating too many errors
  class CompositePhotoDescriptionAnalyser
    DEFAULT_CLIENT = 'gemini-vision'.freeze
    CASSETTE_DIR = 'spec/cassettes/composite_photo_descriptions'.freeze
    PROMPT_TEMPLATE_VERSION = 'v1'.freeze
    # require 'digest'

    def initialize(client_to_use: nil)
      puts "Initializing CompositePhotoDescriptionAnalyser with client: #{client_to_use || DEFAULT_CLIENT}"
      @client_to_use = client_to_use || DEFAULT_CLIENT
      @llm_api_service = Ai::RubyLlmService.new(client_to_use: @client_to_use)
    end

    # Analyzes a composite image to generate descriptions for each sub-image
    def analyse_composite_url(composite_params)
      puts "Starting analysis for composite image, dossier_item_id: #{composite_params[:dossier_item].id}"
      start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
      llm_interaction = process_composite_image(composite_params)
      duration_ms = ((Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time) * 1000).to_i
      puts "Analysis completed in #{duration_ms}ms"

      file_path = composite_params[:composite_photo_file_path].to_s
      checksum = begin
        Digest::MD5.file(file_path).hexdigest
      rescue StandardError
        nil
      end
      file_size = begin
        File.size(file_path)
      rescue StandardError
        nil
      end
      puts "Processed file: #{file_path}, checksum: #{checksum}, size: #{file_size} bytes"

      # Parse chosen_response for sub_images count
      parsed = if llm_interaction&.chosen_response.is_a?(String)
                 begin
                   JSON.parse(llm_interaction.chosen_response)
                 rescue StandardError
                   {}
                 end
               else
                 llm_interaction&.chosen_response || {}
               end
      sub_count = parsed['sub_image_descriptions']&.size
      puts "Found #{sub_count || 0} sub-image descriptions"

      # DisFromCompositePhotoA.find_or_create_from_ai_interaction(
      #   llm_interaction, composite_params[:dossier_item], composite_params[:target_dossier_asset]
      # )

      AiInsights::CompositeImageSource.composite_image_insight_from_ai_interaction(
        llm_interaction,
        composite_params[:dossier_item],
        composite_params[:target_dossier_asset],
        composite_photo_file_path: file_path,
        batch_offset: composite_params[:batch_offset],
        batch_limit: composite_params[:batch_limit] || composite_params[:max_photos_to_analyse],
        llm_model: @client_to_use,
        prompt_template_version: PROMPT_TEMPLATE_VERSION,
        prompt_payload: composite_params[:prompt_payload],
        response_status: llm_interaction&.has_errored? ? 'error' : 'success',
        error_message: llm_interaction&.llm_error_message,
        duration_ms: duration_ms,
        screenshot_dimensions: composite_params[:screenshot_dimensions],
        file_size_bytes: file_size,
        sub_images_count: sub_count,
        composite_checksum: checksum,
        screenshot_url: composite_params[:screenshot_url]
      )
      puts "Stored composite image insight, status: #{llm_interaction&.has_errored? ? 'error' : 'success'}"
      llm_interaction
    rescue StandardError => e
      log_error("Failed to process composite image for dossier #{composite_params[:dossier_item].id}", e)
      nil
    end

    private

    # Processes a composite image to extract descriptions
    def process_composite_image(composite_params)
      puts "Processing composite image for dossier_item_id: #{composite_params[:dossier_item].id}, asset_id: #{composite_params[:target_dossier_asset].id}"
      session_id = SecureRandom.uuid
      prompt_content = build_prompt(
        composite_params[:composite_photo_file_path], composite_params[:max_photos_to_analyse],
        composite_params[:dossier_item].id, composite_params[:target_dossier_asset], session_id,
        composite_params[:acceptable_image_ids_for_llm_call]
      )
      # Store prompt payload and metadata
      composite_params[:prompt_payload] = prompt_content
      composite_params[:prompt_template_version] = PROMPT_TEMPLATE_VERSION
      composite_params[:llm_model] = @client_to_use
      cassette_name = "dossier_#{composite_params[:dossier_item].id}_asset_#{composite_params[:target_dossier_asset].id}_listing_#{composite_params[:target_dossier_asset].default_sale_listing.id}_descriptions"

      cassette_name += '_plan_b' if composite_params[:is_plan_b]
      # Include batch offset/limit in cassette name to isolate VCR cassettes per batch
      if composite_params[:batch_offset]
        cassette_name += "_offset#{composite_params[:batch_offset]}"
        cassette_name += "_limit#{composite_params[:batch_limit]}" if composite_params[:batch_limit]
      end
      # puts "Using VCR cassette: #{CASSETTE_DIR}/#{cassette_name}"

      llm_interaction = @llm_api_service.call_llm_api_vision(
        prompt_content,
        use_vcr: true,
        cassette_name: cassette_name,
        cassette_library_dir: CASSETTE_DIR,
        images: composite_params[:composite_photo_file_path].to_s
      )

      if llm_interaction.has_errored?
        log_error("LLM error: #{llm_interaction.llm_error_message}", StandardError.new('LLM failed'))
        nil
      else
        validate_and_create_descriptions(llm_interaction, composite_params[:dossier_item], composite_params[:target_dossier_asset])
      end
      puts "Finished processing composite image, result: #{llm_interaction ? 'success' : 'failed'}"
      llm_interaction
    rescue StandardError => e
      log_error("Composite image description failed for #{composite_params[:composite_photo_file_path]}", e)
      nil
    end

    # Builds the prompt for LLM to generate sub-image descriptions
    def build_prompt(composite_photo_file_path, max_photos_to_analyse, dossier_item_id, target_dossier_asset, session_id, acceptable_image_ids_for_llm_call = nil)
      acceptable_image_ids_for_llm_call ||= target_dossier_asset.asset_photo_short_ids
      puts "Acceptable images for LLM call: #{acceptable_image_ids_for_llm_call}"
      puts "Building prompt for composite image: #{composite_photo_file_path}, max_photos: #{max_photos_to_analyse}"
      prompt = <<~PROMPT
        You are an expert in real estate photo analysis. This is a standalone request with session ID #{session_id}. Ignore any previous requests or data. Your task is to examine a composite image containing multiple property photos, each labeled with an "IMAGE ID" (e.g., "IMAGE ID: 755"), and provide a detailed description for each sub-image.
        The dossier_item_id is #{dossier_item_id} and the target_dossier_asset_id is #{target_dossier_asset.id}. Return these in the response.
        The only valid image_ids are #{acceptable_image_ids_for_llm_call}. Ignore any other image_ids.

        ### Composite Image Details:
        - **Composite Image URL**: "#{composite_photo_file_path}"

        ### Analysis Instructions:
        - For each sub-image (up to #{max_photos_to_analyse}), provide:
          - **Image ID**: The labeled "IMAGE ID" (e.g., "755").
          - **Catchy Title**: A concise, engaging title (e.g., "Bright Modern Kitchen").
          - **General Description**: A detailed description of the scene, including visible elements (e.g., furniture, decor, architectural features).
          - **Style**: The aesthetic style (e.g., "modern", "traditional").
          - **Dominant Color**: The primary color tone (e.g., "white", "dark wood").
          - **Significant Items**: Notable items or features (e.g., "stainless steel appliances", "large bay window").
          - **Condition**: Observed condition (e.g., "well-maintained", "needs renovation").
          - **Unique Features**: Standout elements (e.g., "vaulted ceiling").
          - **Estimated Area (Square Feet)**: Estimate based on visual cues (e.g., "200") or "N/A".
          - **Is Outside**: True if outdoors, false if indoors.

        ### Response Format:
        {
          "dossier_item_id": "string",
          "target_dossier_asset_id": "string",
          "composite_photo_file_path": "string",
          "sub_image_descriptions": [
            {
              "image_id": "string",
              "catchy_title": "string",
              "general_description": "string",
              "style": "string",
              "dominant_color": "string",
              "significant_items": ["string"],
              "condition": "string",
              "unique_features": ["string"],
              "estimated_area_sq_feet": "string",
              "is_outside": boolean
            }
          ]
        }

        ### Guidelines:
        - Analyze only the provided composite image.
        - Ensure descriptions are factual and based on visual evidence.
        - Return only valid JSON without additional text or comments.
      PROMPT
      puts "Prompt built, length: #{prompt.length} characters"
      prompt
    end

    # Validates response and creates description data
    def validate_and_create_descriptions(llm_interaction, _dossier_item, target_dossier_asset)
      puts "Validating LLM response for asset_id: #{target_dossier_asset.id}"
      response_data = JSON.parse(llm_interaction.chosen_response)
      valid_image_ids = target_dossier_asset.asset_photo_short_ids

      sub_image_descriptions = response_data['sub_image_descriptions'] || []
      invalid_ids = sub_image_descriptions.map { |desc| desc['image_id'].to_i } - valid_image_ids

      if invalid_ids.any?
        log_error("Invalid image IDs: #{invalid_ids.join(', ')}", StandardError.new('Invalid image IDs'))
        return nil
      end

      result = {
        dossier_item_id: response_data['dossier_item_id'],
        target_dossier_asset_id: response_data['target_dossier_asset_id'],
        composite_photo_file_path: response_data['composite_photo_file_path'],
        sub_image_descriptions: sub_image_descriptions
      }
      puts "Validation successful, returning #{sub_image_descriptions.size} descriptions"
      result
    rescue JSON::ParserError => e
      log_error('Invalid JSON response', e)
      nil
    end

    # Logs error with a formatted message
    def log_error(message, exception)
      puts "#{message}: #{exception.message}"
      Rails.logger.error("#{message}: #{exception.message}")
    end
  end
end
