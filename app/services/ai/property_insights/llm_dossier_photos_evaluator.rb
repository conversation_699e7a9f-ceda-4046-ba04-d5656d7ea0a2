module Ai::PropertyInsights
  class LlmDossierPhotosEvaluator
    DEFAULT_CLIENT = 'gemini-vision'.freeze
    CASSETTE_DIR = 'spec/cassettes/photo_analyses'.freeze

    def initialize(client_to_use: nil)
      @llm_api_service = Ai::RubyLlmService.new(client_to_use: client_to_use || DEFAULT_CLIENT)
    end

    # Processes a single photo and stores the analysis
    def process_single_photo(dossier_item, photo, existing_rooms: [])
      prompt_content = build_prompt(photo, existing_rooms)
      cassette_name = cassette_name_for(dossier_item, photo)

      llm_interaction = @llm_api_service.call_llm_api_vision(
        prompt_content,
        use_vcr: true,
        cassette_name: cassette_name,
        cassette_library_dir: CASSETTE_DIR,
        images: photo.remote_photo_url
      )

      # analysis = LiaToAnalysePhotos.kept.find_or_create_from_llm_interaction(llm_interaction, photo, dossier_item)
      analysis = DossierInfoSourceFromPhoto.find_or_create_from_ai_interaction(
        llm_interaction, photo, dossier_item
      )
      # update_existing_rooms(analysis)
      analysis
    rescue StandardError => e
      log_error("Photo processing failed for photo #{photo.id}", e)
    end

    private

    # Builds the prompt for LLM analysis with existing rooms context
    def build_prompt(photo, existing_rooms)
      asset_part_types = DossierAssetPart.asset_part_types.keys.map { |k| "- #{k}" }.join("\n       ")
      photo_url = photo.remote_photo_url || 'Not provided'
      existing_rooms_str = existing_rooms.empty? ? 'None identified yet.' : existing_rooms.join(', ')

      <<~PROMPT
        You are an expert in real estate photo analysis with a sharp eye for detail. Your task is to examine the provided property photo and identify all visible sections or rooms, extracting key observational details for each. Additionally, you will determine which section of the house this photo represents, using an existing list of identified rooms/sections, and update it if necessary.

        ### Photo Details:
        - **Photo URL**: "#{photo_url}"

        ### Existing Rooms/Sections Identified:
        - #{existing_rooms_str}

        ### Analysis Instructions:
        For each distinct section or room visible in the photo:
        1. **Section or Room Type**: Identify the type using one of these valid values:
           #{asset_part_types}
           Use "other" if a section cannot be confidently identified, and explain why in the notes.
        2. **Main Section Indicator**: Indicate if this is the primary focus of the photo with a boolean (true/false).
        3. **Descriptive Title**: Provide a concise, descriptive title (e.g., "Spacious Modern Kitchen").
        4. **Dominant Color**: Specify the primary color (e.g., "white", "dark wood").
        5. **Significant Items and Features**: List notable items (e.g., "stainless steel refrigerator", "large bay window").
        6. **Condition or Style**: Summarize the observed condition or style (e.g., "modern", "well-maintained").
        7. **Unique Features**: Highlight standout elements (e.g., "vaulted ceiling").
        8. **Estimated Area (Square Feet)**: Estimate the area in square feet based on visual cues (e.g., "approximately 200 sq ft"). Use "N/A" if unable to estimate.
        9. **Estimated Area (Square Meters)**: Estimate the area in square meters (e.g., "approximately 18.6 sq m"). Use "N/A" if unable to estimate.
        10. **Is Outside**: Indicate if the section is outdoors (true) or indoors (false) based on visual evidence.
        11. **Summary Description**: Provide a brief, factual description (e.g., "A bright kitchen with ample counter space").
        12. **Matches Existing Room**: If the identified section or room matches an existing room from the provided list, specify which one (e.g., "kitchen"). If it’s new, say "new_room_section" and it will be added to the list.

        ### Response Format:
        #{response_format_template}

        ### Guidelines:
        #{guidelines_template}
      PROMPT
    end

    # # Updates the existing rooms array based on analysis
    # def update_existing_rooms(analysis)
    #   return unless analysis&.[]('sections_or_rooms_in_photo')

    #   analysis['sections_or_rooms_in_photo'].each do |section|
    #     room_type = section['section_or_room_type']
    #     matches_existing = section['matches_existing_room']

    #     @existing_rooms << room_type if matches_existing == 'new_room_section' && !@existing_rooms.include?(room_type)
    #   end
    # end

    # Generates cassette name for VCR
    def cassette_name_for(dossier_item, photo)
      "dossier_#{dossier_item.id}_photo_#{photo.id}"
    end

    # Logs error with a formatted message
    def log_error(message, exception)
      put "#{message}: #{exception.message}"
      Rails.logger.error("#{message}: #{exception.message}")
    end

    # Defines the expected JSON response structure
    def response_format_template
      <<~JSON
        {
          "general_photo_analysis": {
            "photo_url": "string",
            "photo_catchy_title": "string",
            "photo_general_description": "string",
            "photo_orientation": "string",
            "photo_quality": "string",
            "photo_style": "string",
            "photo_tags": ["string"],
            "photo_atmosphere": "string",
            "photo_emotion": "string"
          },
          "sections_or_rooms_in_photo": [
            {
              "section_or_room_type": "string",
              "is_main_section_or_room": boolean,
              "descriptive_title": "string",
              "dominant_color": "string",
              "significant_items": ["string"],
              "condition_or_style": "string",
              "unique_features": ["string"],
              "estimated_area_sq_feet": "string",
              "estimated_area_sq_meters": "string",
              "is_outside": boolean,
              "summary_description": "string",
              "matches_existing_room": "string"
            }
          ]
        }
      JSON
    end

    # Defines analysis guidelines
    def guidelines_template
      <<~GUIDELINES
        - Analyze all visible sections or rooms in the photo; include multiple entries in "sections_or_rooms_in_photo" if more than one is present.
        - Set "is_main_section_or_room" to true for the section or room that is the primary focus (e.g., most prominent or largest area). Only one entry should be true; set others to false.
        - Base your analysis purely on visual evidence; avoid speculation about value or intent.
        - Use "other" for unidentifiable sections, with an explanation in "significant_items" (e.g., "Unclear area, possibly storage").
        - Ensure all fields are populated; use "N/A" for "dominant_color", "estimated_area_sq_feet", or "estimated_area_sq_meters" if they cannot be determined.
        - For area estimates, provide approximate values based on typical room sizes and visual scale; note uncertainty if applicable (e.g., "approx. 150-200 sq ft").
        - For "matches_existing_room", compare the identified "section_or_room_type" with the provided list of existing rooms. If it matches, return the matching room name (e.g., "kitchen_1"). If it’s new, return "new_room_section".
        - Return only valid JSON without additional text, comments, or markdown outside the JSON structure.
      GUIDELINES
    end
  end
end
