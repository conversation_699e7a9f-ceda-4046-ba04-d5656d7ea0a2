require 'vcr'

module Ai
  class RubyLlmService
    def initialize(client_to_use: nil)
      @llm_parameters = {}

      # Configure RubyLLM with API keys from Rails credentials
      RubyLLM.configure do |config|
        config.openai_api_key = Rails.application.credentials.config[:openai_api_key]
        config.gemini_api_key = Rails.application.credentials.config[:gemini_api_key]
      end

      # https://rubyllm.com/guides/available-models
      # can find models above
      # and useful model info froms
      # model = RubyLLM.models.find('gemini-2.0-flash')
      case client_to_use
        # 19 may 2025 - changing the idea of client_to_use
        # now it will state the use case and I'll pick the model here
        # Allows this to be a source of wisdom for model choice
      when 'side_by_side_sections_analysis'
        # RubyLLM.models.refresh!
        # @chat = RubyLLM.chat(model: 'gemini-2.5-flash-preview-04-17')
        @chat = RubyLLM.chat(model: 'o4-mini')
      when 'gemini'
        # @chat = RubyLLM.chat(model: 'gemini-2.0-flash')
        # 22 apr - trying out below:
        RubyLLM.models.refresh!
        # need above to be able to run below
        @chat = RubyLLM.chat(model: 'gemini-2.5-flash-preview-04-17')
      when 'gemini-vision'
        # @chat = RubyLLM.chat(model: 'gemini-1.5-flash-latest')
        # 29 mar 2025 - updated to below
        # @chat = RubyLLM.chat(model: 'gemini-2.0-flash')
        # 12 apr - trying with the mini model
        # in terms of the composite photo gpt-4o-mini was the worst!
        # gpt-4o & gpt-4.5-preview weren't great either
        # @chat = RubyLLM.chat(model: 'gpt-4o-mini')
        # @chat = RubyLLM.chat(model: 'gpt-4o')
        # @chat = RubyLLM.chat(model: 'gpt-4.5-preview')
        # @chat = RubyLLM.chat(model: 'gemini-2.0-flash-exp')
        # 23 apr: open ai released o4-mini
        # @chat = RubyLLM.chat(model: 'o4-mini')
        # and gpt-4.1-nano which is even cheaper!!!
        # Unfortunately nano doesn't perform great with images
        # @chat = RubyLLM.chat(model: 'gpt-4.1-nano')
        RubyLLM.models.refresh!
        # in April below failed where o4-mini worked
        @chat = RubyLLM.chat(model: 'gemini-2.5-flash-preview-04-17')
      # 04 may - above worked after o4-mini failed
      # I did reduce batch size to 10 too though...
      else
        # @chat = RubyLLM.chat(model: 'gpt-4o-mini')
        # 23 apr - will default to the cheapest here:
        @chat = RubyLLM.chat(model: 'gpt-4.1-nano')
        @llm_parameters[:response_format] = 'json'
      end
    end

    def call_llm_api_text(
      prompt, use_vcr: false, cassette_name: nil, cassette_library_dir: nil
    )
      cassette_name ||= 'unknown_llm_interaction'
      cassette_name = "ruby_llm_text_#{cassette_name}_#{@chat.model.id}"
      llm_interaction_slug = "#{cassette_name}_#{@llm_parameters[:temperature]}"

      llm_interaction = LlmInteraction.initialize_llm_interaction(prompt, llm_interaction_slug, @llm_parameters)
      puts "llm_interaction: #{llm_interaction}"
      cassette_library_dir ||= 'spec/cassettes'

      if use_vcr && cassette_name
        VCR.configure do |config|
          config.cassette_library_dir = cassette_library_dir
          config.hook_into :webmock
          config.ignore_localhost = true
        end
        vcr_cassette_path = "#{cassette_library_dir}/#{cassette_name.sub('.', '_')}.yml"
        llm_interaction.update!(vcr_cassette_path: "#{vcr_cassette_path}")
        puts "call_llm_api_text using: #{vcr_cassette_path}"
        VCR.use_cassette(cassette_name) do
          make_api_text_call(prompt, llm_interaction)
        end
      else
        make_api_text_call(prompt, llm_interaction)
      end

      llm_interaction
    end

    # def call_llm_api(prompt, use_vcr: false, cassette_name: nil, cassette_library_dir: nil)
    def call_llm_api_vision(
      prompt, use_vcr: false, cassette_name: nil, cassette_library_dir: nil,
      images: nil
    )
      cassette_name ||= 'unknown_llm_interaction'
      cassette_name = "ruby_llm_vision_#{cassette_name}_#{@chat.model.id}"
      llm_interaction_slug = "#{cassette_name}_#{@llm_parameters[:temperature]}"

      llm_interaction = LlmInteraction.initialize_llm_interaction(prompt, llm_interaction_slug, @llm_parameters)
      puts "llm_interaction: #{llm_interaction}"
      cassette_library_dir ||= 'spec/cassettes'

      if use_vcr && cassette_name
        VCR.configure do |config|
          config.cassette_library_dir = cassette_library_dir
          config.hook_into :webmock
          config.ignore_localhost = true
        end
        vcr_cassette_path = "#{cassette_library_dir}/#{cassette_name.sub('.', '_')}.yml"
        llm_interaction.update!(
          images_for_llm: images,
          vcr_cassette_path: vcr_cassette_path
        )

        puts "call_llm_api_vision using: #{vcr_cassette_path}"

        VCR.use_cassette(cassette_name) do
          make_api_vision_call(prompt, llm_interaction, images)
        end
      else
        make_api_vision_call(prompt, llm_interaction, images)
      end

      llm_interaction
    end

    private

    def make_api_text_call(prompt, llm_interaction)
      # ruby_llm uses a simpler chat interface.
      response = @chat.ask(prompt)

      cleaned_response = response.content.gsub(/```json\n|```|\n```/, '').strip
      total_tokens = response.output_tokens + response.input_tokens
      success_details = {
        full_response: response,
        chosen_response: cleaned_response,
        response_model: response.model_id,
        response_prompt_tokens: response.input_tokens,
        response_completion_tokens: response.output_tokens,
        response_total_tokens: total_tokens,
        has_errored: false
      }

      # Update interaction with response details
      llm_interaction.update_generic_llm_interaction_success(
        success_details
      )

      # Parse the JSON response from the LLM
      JSON.parse(cleaned_response)
    rescue JSON::ParserError => e
      Rails.logger.error("Failed to parse LLM response as JSON: #{e.message}")
      llm_interaction.update_llm_interaction_with_error(response, e.message)
      []
    rescue StandardError => e
      Rails.logger.error("LLM API call failed: #{e.message}")
      llm_interaction.update_llm_interaction_with_error(response, e.message)
      []
    end

    # def make_api_call(prompt, llm_interaction)
    def make_api_vision_call(prompt, llm_interaction, images)
      # ruby_llm uses a simpler chat interface; no need for complex message formatting
      response = @chat.ask(prompt, with: { image: images })
      # from below I confirmed that image above can also be on the local filesystem
      # https://rubyllm.com/guides/chat
      # "Compare these two charts", with: {
      #   image: ["chart1.png", "chart2.png"]
      # }
      # ruby_llm response provides direct access to content and token counts
      cleaned_response = response.content.gsub(/```json\n|```|\n```/, '').strip
      total_tokens = response.output_tokens + response.input_tokens
      success_details = {
        full_response: response,
        chosen_response: cleaned_response,
        # response_object: response['object'],
        # response_id: response_id,
        response_model: response.model_id,
        # response_prompt_tokens: response.dig('usage', 'prompt_tokens'),
        response_prompt_tokens: response.input_tokens,
        response_completion_tokens: response.output_tokens,
        response_total_tokens: total_tokens,
        has_errored: false
      }

      # Update interaction with response details
      llm_interaction.update_generic_llm_interaction_success(
        success_details
        # response,
        # cleaned_response,
        # # prompt_tokens: response.prompt_tokens,
        # # completion_tokens: response.completion_tokens,
        # prompt_tokens: response.input_tokens,
        # completion_tokens: response.output_tokens,
        # total_tokens: total_tokens
        # # total_tokens: response.total_tokens
      )

      # Parse the JSON response from the LLM
      JSON.parse(cleaned_response)
    rescue JSON::ParserError => e
      Rails.logger.error("Failed to parse LLM response as JSON: #{e.message}")
      llm_interaction.update_llm_interaction_with_error(response, e.message)
      []
    rescue StandardError => e
      Rails.logger.error("LLM API call failed: #{e.message}")
      llm_interaction.update_llm_interaction_with_error(response, e.message)
      []
    end
  end
end
