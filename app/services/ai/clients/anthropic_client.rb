module Ai::Clients
  class AnthropicClient < LlmClient
    def initialize(api_key:)
      @client = Anthropic::Client.new(api_key:)
    end

    def get_client_slug
      'anthropic'
    end

    def chat(parameters:)
      # Convert OpenAI-style parameters to Anthropic's format
      anthropic_parameters = {
        prompt: parameters[:messages].map { |m| "#{m[:role]}: #{m[:content]}" }.join("\n"),
        temperature: parameters[:temperature]
      }
      @client.chat(anthropic_parameters)
    end

    def generate_image(parameters:)
      raise NotImplementedError, 'Anthropic does not support image generation'
    end
  end
end
