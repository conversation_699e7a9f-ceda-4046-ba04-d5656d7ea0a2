module Ai::Clients
  class ReplicateClient
    BASE_URL = 'https://api.replicate.com/v1/models/'.freeze

    def initialize(api_key:)
      @api_key = api_key
    end

    # Generate text using a specific Replicate model (if applicable)
    def generate_text(prompt, model:, temperature:)
      raise NotImplementedError, 'Replicate does not provide text generation in this example'
    end

    # Generate an image based on a given prompt
    def generate_image(prompt)
      model_path = 'black-forest-labs/flux-schnell/predictions'
      uri = URI("#{BASE_URL}#{model_path}")

      request_body = {
        input: {
          prompt:,
          go_fast: true,
          num_outputs: 1,
          aspect_ratio: '1:1',
          output_format: 'webp',
          output_quality: 80
        }
      }.to_json

      response = send_post_request(uri, request_body)
      # parse_image_response(response)
      return response
    end

    def get_client_slug
      'replicate'
    end

    private

    # Sends a POST request to the specified URI with the given body
    def send_post_request(uri, body)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true

      request = Net::HTTP::Post.new(uri)
      # request = Net::HTTP::Get.new('https://api.replicate.com/v1/account')
      request['Authorization'] = "Bearer #{@api_key}"
      request['Content-Type'] = 'application/json'
      request['Prefer'] = 'wait'
      request.body = body

      response = http.request(request)
      JSON.parse(response.body)
    rescue JSON::ParserError => e
      raise StandardError, "Failed to parse JSON response: #{e.message}"
    rescue StandardError => e
      raise StandardError, "HTTP request failed: #{e.message}"
    end

    # Parses the Replicate API response for image generation
    # def parse_image_response(response)
    # if %w[processing succeeded].include? response['status']
    #   #  response['status'] == 'succeeded'
    #   { image_url: response.dig('output', 0) }
    # elsif response['status'] == 'failed'
    #   raise StandardError, "Replicate API error: #{response['error'] || 'Unknown error'}"
    # else
    #   raise StandardError, 'Unexpected API response status'
    # end
    # end
  end
end
