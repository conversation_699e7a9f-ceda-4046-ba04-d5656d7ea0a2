module Fabricators
  class GenericPropertyPhotoFabricator
    include Dry::Monads[:result, :do]

    # def initialize(llm_service)
    #   @llm_service = llm_service
    # end
    def initialize(
      client: nil,
      photo_prompt_generator: PromptGenerators::FakeListingPhotoRegular.new
    )

      client ||= Ai::Clients::ReplicateClient.new(
        api_key: Rails.application.credentials.config[:openai_api_key]
      )

      @llm_service = Ai::LlmInteractionService.new(
        client:, model_name: 'gpt-4o-mini', temperature: 0.7
        # using below was more expensive and resulted in unparsable json
        # client:, model_name: 'gpt-4', temperature: 0.9
      )
      @photo_prompt_generator = photo_prompt_generator
    end

    def create_images_only(generic_property)
      # prompts only:
      prompts = @photo_prompt_generator.image_prompts_from_generic_property(generic_property)
      # Where LLM is called and LLM urls returned
      photo_urls_with_description = yield generate_images_for_generic_property(
        prompts, generic_property, max_photos: 2
      )
      # Where the images are actually created and saved
      update_generic_property_with_images(generic_property, photo_urls_with_description)
      generic_property
    end

    def generate_images_for_generic_property(prompts, generic_property, max_photos: nil)
      llm_interaction = nil
      limited_prompts = max_photos ? prompts.first(max_photos) : prompts

      results = limited_prompts.map do |prompt_hash|
        prompt_hash = prompt_hash.symbolize_keys

        existing_photo = generic_property.generic_property_photos.find_by(
          gp_photo_slug: prompt_hash[:full_photo_slug]
        )
        next nil if existing_photo.present?

        prompt = prompt_hash[:image_prompt]
        llm_service_result = @llm_service.fetch_image_url(prompt)

        case llm_service_result
        when Dry::Monads::Success
          image_url, llm_interaction = llm_service_result.value!
        when Dry::Monads::Failure
          error_symbol, llm_interaction = llm_service_result.failure
          puts "error_symbol: #{error_symbol}"
          next nil
        end

        next nil if image_url.blank?

        prompt_hash[:url] = image_url
        prompt_hash[:llm_interaction_uuid] = llm_interaction.uuid
        prompt_hash
      end.compact

      results.any? ? Success(results) : Failure(:no_images_generated)
    end

    def update_generic_property_with_images(generic_property, photo_urls_with_description)
      puts "photos to create count: #{photo_urls_with_description.count}"
      image_creator = Creators::GenericPropertyPhotoCreator.new(
        photo_urls_with_description:,
        generic_property:,
        save_images_locally: true
      )
      asset_photos_returned = image_creator.create_and_update_photos

      # asset_photos_returned.each(&:resize_image)

      puts "number of photos created: #{asset_photos_returned.count}"

      # exporter = ExportersAndLoaders::SaleListingsExporter.new(
      #   destination_dir: Rails.root.join('db', 'exports', 'ai_faked_listings')
      # )
      # exporter.export([generic_property])
    end
  end
end
