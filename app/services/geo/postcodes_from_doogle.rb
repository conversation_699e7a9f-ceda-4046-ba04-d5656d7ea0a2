module Geo
  # PostcodesFromDoogle
  # app/services/postcodes_from_doogle.rb
  # app/services/postcode_area_importer.rb
  class PostcodesFromDoogle
    def initialize(postcode_row = nil)
      return unless postcode_row

      @postcode_data = postcode_row.to_h
    end

    def create_and_update_postcode_area(p_code)
      pc_area = PostcodeArea.find_or_create_by_uk_postal_code(p_code)
      # pc_area.update(basic_attributes)
      update_detailed_attributes(pc_area, p_code)
      pc_area.update_geojson_from_postcode
      pc_area
    end

    def import
      p_code = @postcode_data['Postcode']
      pc_area = PostcodeArea.find_or_create_by_uk_postal_code(p_code)

      pc_area.update(basic_attributes)

      update_detailed_attributes(pc_area, p_code)
      pc_area.update_geojson_from_postcode

      puts "Processed: #{p_code}"
    rescue StandardError => e
      puts "Error processing #{p_code}: #{e.message}"
    end

    def basic_attributes
      {
        country: @postcode_data['Country'],
        center_latitude: @postcode_data['Latitude'],
        center_longitude: @postcode_data['Longitude'],
        eastings: @postcode_data['Easting'],
        northings: @postcode_data['Northing'],
        parish: @postcode_data['Parish'],
        date_of_introduction: @postcode_data['Introduced'],
        lsoa: @postcode_data['LSOA Name'],
        msoa: @postcode_data['LSOA Name 2021'],
        postcode_area_description: @postcode_data['Grid Ref'],
        postcode_area_tags: ['from_doogal'],
        relevance: 1.0,
        is_incomplete_postcode: false
      }
    end

    def update_detailed_attributes(pc_area, p_code)
      url_friendly_p_code = p_code.strip.gsub(' ', '%20')
      url = URI("https://www.doogal.co.uk/GetPostcode/#{url_friendly_p_code}?output=json")
      response = Net::HTTP.get(url)
      data = JSON.parse(response)

      doogal_details = {
        propertySales: data['propertySales'],
        busStops: data['busStops'],
        businesses: data['businesses'],
        schools: data['schools']
      }

      detailed_attributes = {
        doogal_details:,
        altitude: data['altitude'],
        uk_ward: data['ward'],
        uk_ward_code: data['wardCode'],
        rural_urban: data['ruralUrban'],
        post_town: data['postTown'],
        built_up_area: data['builtUpArea'],
        # below is travel_to_work_area which I will save in cluster
        # ttwa: data['ttwa'],
        in_use: data['inUse'] == 'Yes',
        index_of_multiple_deprivation: data['imd'],
        average_household_income: data['averageIncome'],
        predominant_property_type: data['predominantPropertyType'],
        uk_grid_reference: data['gridReference'],
        area_population: data['population'],
        area_households: data['households'],
        # averageIncome: data['averageIncome'],
        # predominantPropertyType: data['predominantPropertyType'],
        # postcode: data['postcode'],
        northings: data['northing'],
        eastings: data['easting'],
        center_latitude: data['latitude'],
        center_longitude: data['longitude'],
        country: data['country'],
        lsoa: data['lsoa'], # Changed from 'LSOA Name' to 'lsoa'
        # lsoaName: data['lsoaName'], # Added as per the keys list
        admin_county: data['county'],
        region: data['region'],
        # nationalPark: data['nationalPark'],
        what_three_words: data['what3Words'],
        # constituency2024: data['constituency2024'],
        admin_district: data['district'],
        # quality: data['quality'],
        # postcode_area_description: data['Grid Ref'],
        postcode_area_tags: ['from_doogal'],
        relevance: 1.0,
        is_incomplete_postcode: false

        # altitude: data['altitude'],
        # what_three_words: data['what3Words'],
        # country: data['Country'],
        # center_latitude: data['Latitude'],
        # center_longitude: data['Longitude'],
        # eastings: data['Easting'],
        # northings: data['Northing'],
        # parish: data['Parish'],
        # date_of_introduction: data['Introduced'],
        # lsoa: data['LSOA Name'],
        # msoa: data['LSOA Name 2021'],
      }
      pc_area.update(detailed_attributes)
    end
  end
end
