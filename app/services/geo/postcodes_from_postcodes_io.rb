module Geo
  # PostcodesFromPostcodesIo
  # app/services/postcode_area_service.rb
  class PostcodesFromPostcodesIo
    def self.create_or_update_postcode_areas(postcodes)
      postcodes.each do |postcode|
        create_or_update_postcode_area(postcode)
      end
    end

    def self.create_or_update_postcode_area(postcode)
      pc_area = PostcodeArea.find_by_uk_postal_code(postcode)
      return pc_area if pc_area.present?

      postcode_slug = postcode.downcase.gsub(/[^a-z0-9-]/, '')
      url = URI("https://api.postcodes.io/postcodes/#{postcode_slug}")

      begin
        response = Net::HTTP.get_response(url)
        if response.is_a?(Net::HTTPSuccess)
          data = JSON.parse(response.body)

          if data['status'] == 200
            postcode_data = data['result']

            # Save or update the data in the PostcodeArea model
            pc_area = PostcodeArea.find_or_create_by(postal_code: postcode)

            pc_area.update!(
              {
                postcode_area_codes: postcode_data['codes'],
                european_electoral_region: postcode_data['european_electoral_region'],
                center_latitude: postcode_data['latitude'],
                center_longitude: postcode_data['longitude'],
                eastings: postcode_data['eastings'],
                northings: postcode_data['northings'],
                outcode: postcode_data['outcode'],
                incode: postcode_data['incode'],
                date_of_introduction: postcode_data['date_of_introduction'],
                primary_care_trust: postcode_data['primary_care_trust'],
                lsoa: postcode_data['lsoa'],
                msoa: postcode_data['msoa'],
                admin_district: postcode_data['admin_district'],
                parish: postcode_data['parish'],
                region: postcode_data['region'],
                country: postcode_data['country']
              }
            )

            puts "Saved data for postcode: #{postcode}"
            return pc_area
          else
            puts "Failed to fetch data for postcode: #{postcode}. Status: #{data['status']}"
          end
        else
          puts "HTTP request failed for postcode: #{postcode}. Response: #{response.code}"
        end
      rescue StandardError => e
        puts "An error occurred while processing postcode: #{postcode}. Error: #{e.message}"
      end
      nil
    end
  end
end
