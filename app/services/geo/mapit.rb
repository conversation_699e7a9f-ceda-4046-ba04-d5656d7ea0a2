require 'net/http'
require 'uri'

# https://github.com/andrewn/uksnowmap/blob/master/rb/area_boundary.rb
# Based on above
module Geo
  # Geo::Mapit
  class Mapit
    def self.fetch_touching_areas(area_code)
      new.fetch_touching_areas(area_code)
    end

    def self.fetch_apu(postcode)
      new.fetch_apu(postcode)
    end

    def self.fetch_and_set_geojson(area_code, pc_area)
      new.fetch_and_set_geojson(area_code, pc_area)
    end

    # def fetch_single_area(area_code)
    #   uri = URI("https://postcodes.mapit.longair.net/area/#{area_code}")
    #   process_response(uri) do |data|
    #     data.each do |_, area|
    #       update_postcode_area(area['name'], mapit_id: area['id'])
    #     end
    #   end
    # end

    def fetch_and_set_geojson(area_code, pc_area)
      primary_geojson = nil
      uri = URI("https://postcodes.mapit.longair.net/area/#{area_code}.geojson")
      process_response(uri) do |area_geometry|
        primary_geojson = {
          "type": 'Feature',
          "geometry": area_geometry,
          "properties": {
            "postcodes": pc_area.postal_code
            # "mapit_code": 'CV116LJ'
          }
        }
        # dd = { primary: primary_geojson }
        pc_area.postcode_area_geojson = { primary: primary_geojson }
        pc_area.save!
      end
      primary_geojson
    end

    def fetch_touching_areas(area_code)
      # area_code is an id internal to mapit
      pc_areas = []
      # area_code_without_whitespace = area_code.gsub(/\s+/, '')
      uri = URI("https://postcodes.mapit.longair.net/area/#{area_code}/touches")
      process_response(uri) do |data|
        data.each do |_, area|
          # data here does not include long lat center etc..
          if area['type'] == 'APU'
            pc_area = update_postcode_area(area['name'], mapit_id: area['id'])
            pc_areas << pc_area
          end
        end
      end
      pc_areas
    end

    def fetch_apu(postcode)
      # pc_area = nil
      pc_area = PostcodeArea.find_or_create_by_uk_postal_code(postcode)
      return pc_area if pc_area.mapit_id.present?

      postcode_without_whitespace = postcode.gsub(/\s+/, '')

      uri = URI("https://postcodes.mapit.longair.net/postcode/#{postcode_without_whitespace}")
      process_response(uri) do |data|
        apu_data = data['areas'].find { |_, v| v['type'] == 'APU' }
        if apu_data
          # Even here, data does not include city etc...
          pc_area = update_postcode_area(apu_data[1]['name'], mapit_id: apu_data[1]['id'])
          puts "Saved APU #{apu_data[1]['id']} for postcode #{postcode_without_whitespace}"
        else
          puts "No APU found for postcode #{postcode_without_whitespace}"
        end
      end
      pc_area
    end

    private

    def process_response(uri)
      response = Net::HTTP.get_response(uri)
      if response.is_a?(Net::HTTPSuccess)
        yield JSON.parse(response.body)
      else
        puts "Failed to fetch data from #{uri}"
      end
    rescue JSON::ParserError => e
      puts "Failed to parse JSON response: #{e.message}"
    end

    def update_postcode_area(postcode, attributes)
      puts "Saving postcode from mapit: #{postcode}"
      pc_area = PostcodeArea.find_or_create_by_uk_postal_code(postcode)
      pc_area.update!(attributes)
      pc_area.update_geojson_from_postcode
      puts "Saved postcode from mapit: #{postcode}"
      pc_area
    end
  end
end
