module Geo
  # 28 dec 2024 - this was gen by AI when it appeared rgeo could
  # not do polygon merging
  # Not currently used
  class PolygonMerger
    # Helper function to calculate cross product
    def self.cross_product(o, a, b)
      return 0 if o.nil? || a.nil? || b.nil?

      (a[0].to_f - o[0].to_f) * (b[1].to_f - o[1].to_f) - (a[1].to_f - o[1].to_f) * (b[0].to_f - o[0].to_f)
    end

    # Helper function to find the convex hull of a set of points
    def self.convex_hull(points)
      # Ensure points are all arrays of 2 elements
      points = points.select { |p| p.is_a?(Array) && p.size == 2 }
      return [] if points.size < 3

      # Find the leftmost point
      start = points.min_by(&:first)
      return [] if start.nil?

      points = points.sort_by { |p| [Math.atan2(p[1] - start[1], p[0] - start[0]), p[0]] }

      hull = []
      points.each do |p|
        hull.pop while hull.size > 1 && cross_product(hull[-2], hull[-1], p) <= 0
        hull << p
      end

      # If there are points in reverse order
      lower = []
      points.reverse_each do |p|
        lower.pop while lower.size > 1 && cross_product(lower[-2], lower[-1], p) <= 0
        lower << p
      end

      hull.pop # remove the last point because it's the same as the first in upper hull
      lower.pop # same for lower hull

      hull + lower[1..-1] # combine upper and lower hull
    end

    # Convert geometry to an array of polygons
    def self.normalize_geometry(geometry)
      if geometry.is_a?(Array) && geometry.first.is_a?(Array) # MultiPolygon or already normalized Polygon
        geometry
      else # Single Polygon, wrap it in an array
        [geometry]
      end
    end

    # Flatten polygon structures into a single array of points
    def self.flatten_polygons(polygons)
      polygons.flat_map do |polygon|
        normalize_geometry(polygon).flat_map do |poly|
          poly # Here, assuming each poly is already an array of points
        end
      end.compact
    end

    # Merge multiple polygons by finding the convex hull of all their points
    def self.merge_polygons(polygons)
      # Flatten the list of polygons into one array of points, removing any nil values
      all_points = flatten_polygons(polygons)

      # Compute the convex hull of all points
      convex_hull(all_points)
    end
  end
end
