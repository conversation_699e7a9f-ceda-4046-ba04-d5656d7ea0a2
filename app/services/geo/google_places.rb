require 'net/http'
require 'uri'
require 'json'

module Geo
  class GooglePlaces
    include HTTParty
    base_uri 'https://maps.googleapis.com/maps/api/place'

    def initialize(api_key)
      @api_key = api_key
    end

    # Searches for points of interest relevant to house hunting around given coordinates
    def search_by_coordinates(latitude, longitude)
      options = {
        query: {
          key: @api_key,
          location: "#{latitude},#{longitude}",
          radius: 2000, # 2 km radius, adjust as needed
          types: %w[school hospital park transit_station grocery_or_supermarket restaurant]
        }
      }
      response = self.class.get('/nearbysearch/json', options)
      JSON.parse(response.body)
    rescue JSON::ParserError => e
      { 'error' => "Failed to parse JSON response: #{e.message}" }
    end

    # Fetches detailed information about a place using its place_id
    def get_place_details(place_id)
      options = {
        query: {
          key: @api_key,
          place_id:,
          fields: 'name,formatted_address,rating,user_ratings_total,types,opening_hours,website,photos,reviews'
        }
      }
      response = self.class.get('/details/json', options)
      JSON.parse(response.body)
    rescue JSON::ParserError => e
      { 'error' => "Failed to parse JSON response: #{e.message}" }
    end

    # Helper method to fetch photos of a place
    def get_place_photo(photo_reference)
      max_width = 400 # Adjust as needed
      options = {
        query: {
          key: @api_key,
          photoreference: photo_reference,
          maxwidth: max_width
        }
      }
      self.class.get('/photo', options)
    end

    # Method to search for multiple types of locations and gather detailed info
    def search_and_detail_by_coordinates(latitude, longitude)
      results = search_by_coordinates(latitude, longitude)
      detailed_results = []

      if results['status'] == 'OK'
        results['results'].each do |place|
          place_details = get_place_details(place['place_id'])

          next unless place_details['status'] == 'OK'

          detailed_place = place_details['result']
          # Fetching the first photo if available for a visual representation
          detailed_place['photo_url'] = get_place_photo(detailed_place['photos'][0]['photo_reference'])['request']['uri'] if detailed_place['photos'] && detailed_place['photos'].any?
          detailed_results << detailed_place
        end
      end

      detailed_results
    end
  end
end
