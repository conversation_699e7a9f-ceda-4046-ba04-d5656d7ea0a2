require 'net/http'
require 'uri'

module Geo
  # Geo::MapboxPostcodes
  class MapboxPostcodes
    MAPBOX_ACCESS_TOKEN = 'pk.eyJ1IjoiYXhpc21hcHMiLCJhIjoieUlmVFRmRSJ9.CpIxovz1TUWe_ecNLFuHNg'.freeze

    def initialize(postcode)
      @postcode = postcode
    end

    def fetch_and_save_data
      encoded_postcode = @postcode.gsub(/\s+/, '')
      uri = URI("https://api.mapbox.com/geocoding/v5/mapbox.places/#{encoded_postcode}.json?access_token=#{MAPBOX_ACCESS_TOKEN}")
      response = Net::HTTP.get_response(uri)

      if response.is_a?(Net::HTTPSuccess)
        process_response(JSON.parse(response.body))
      else
        puts "Failed to fetch data for postcode #{@postcode}"
      end
    end

    private

    def process_response(data)
      if data['features'].any?
        feature = data['features'].first
        returned_p_code = feature['text']
        pc_area = PostcodeArea.find_or_create_by_uk_postal_code(returned_p_code)

        update_postcode_area(pc_area, feature)
        pc_area.update_geojson_from_postcode
        puts "Saving mapbox postcode: #{@postcode}"
        puts "Saved or updated postcode area #{pc_area.postal_code}"
      else
        puts "No features found for postcode #{@postcode}"
      end
    end

    def update_postcode_area(pc_area, feature)
      pc_area.update!(
        primary_care_trust: feature['properties']['mapbox_id'],
        # TODO: -fix above when I add mapbox_id to PostcodeArea
        postcode_area_title: feature['place_name'],
        center_latitude: feature['center'][1],
        center_longitude: feature['center'][0],
        bbox_min_latitude: feature['bbox'][1],
        bbox_max_latitude: feature['bbox'][3],
        bbox_min_longitude: feature['bbox'][0],
        bbox_max_longitude: feature['bbox'][2],
        city: find_context_value(feature['context'], 'place.'),
        region: find_context_value(feature['context'], 'region.'),
        country: find_context_value(feature['context'], 'country.')
        # Add more attributes as needed based on your model
      )
    end

    def find_context_value(context, prefix)
      context.find { |ctx| ctx['id'].start_with?(prefix) }&.dig('text')
    end
  end
end
