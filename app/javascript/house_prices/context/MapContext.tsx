import { createContext, useContext, useState, ReactNode } from 'react';
import type { Map, Popup } from 'mapbox-gl';

interface MapContextType {
  map: Map | null;
  setMap: (map: Map | null) => void;
  popup: Popup | null;
  setPopup: (popup: Popup | null) => void;
  selectedPostcode: string;
  setSelectedPostcode: (postcode: string) => void;
  priceRange: number[];
  setPriceRange: (range: number[]) => void;
  priceText: string;
  setPriceText: (text: string) => void;
}

const MapContext = createContext<MapContextType | null>(null);

export const useMap = () => {
  const context = useContext(MapContext);
  if (!context) {
    throw new Error('useMap must be used within a MapProvider');
  }
  return context;
};

export const MapProvider = ({ children }: { children: ReactNode }) => {
  const [map, setMap] = useState<Map | null>(null);
  const [popup, setPopup] = useState<Popup | null>(null);
  const [selectedPostcode, setSelectedPostcode] = useState('');
  const [priceRange, setPriceRange] = useState([500, 9000]);
  const [priceText, setPriceText] = useState('all prices');

  return (
    <MapContext.Provider value={{
      map,
      setMap,
      popup,
      setPopup,
      selectedPostcode,
      setSelectedPostcode,
      priceRange,
      setPriceRange,
      priceText,
      setPriceText
    }}>
      {children}
    </MapContext.Provider>
  );
};
