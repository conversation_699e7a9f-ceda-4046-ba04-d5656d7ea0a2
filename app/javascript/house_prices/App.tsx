import { MapProvider } from './context/MapContext';
import Map from './components/Map';
import Legend from './components/Legend';
import PriceSlider from './components/PriceSlider';
import LocationLinks from './components/LocationLinks';

const App = () => {
  return (
    <div>
      <MapProvider>
        <LocationLinks />
        <div id="map-container">
          <Map />
          <div id="legend">
            <Legend />
            <PriceSlider />
          </div>
        </div>
        <LocationLinks showHighlights={true} />
      </MapProvider>
    </div>
  );
};

export default App;
