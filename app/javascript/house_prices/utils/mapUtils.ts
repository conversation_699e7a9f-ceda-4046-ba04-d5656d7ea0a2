import mapboxgl from 'mapbox-gl';
import { MAP_CONSTANTS, colourStops } from '../constants';
import MapboxGeocoder from '@mapbox/mapbox-gl-geocoder';
import '@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css';

export const setupMapControls = (map: mapboxgl.Map) => {
  map.addControl(
    new MapboxGeocoder({
      accessToken: mapboxgl.accessToken,
      mapboxgl: mapboxgl,
      placeholder: 'Zoom to town or postcode',
      zoom: 9,
      countries: 'gb',
      types: 'postcode,place,locality,neighborhood'
    }),
    'bottom-left'
  );

  map.addControl(new mapboxgl.NavigationControl(), 'top-left');
  map.addControl(new mapboxgl.FullscreenControl(), 'bottom-right');
  
  const geoLocate = new mapboxgl.GeolocateControl();
  map.addControl(geoLocate, 'top-left');

  map.dragRotate.disable();
  map.touchZoomRotate.disableRotation();
  map.scrollZoom.disable();
};

export const addPostcodeLayers = (map: mapboxgl.Map) => {
  map.addSource('postcodes', {
    type: 'vector',
    url: 'mapbox://annapowellsmith.2kq8mrxg'
  });

  map.addLayer({
    'id': 'postcodes',
    'source': 'postcodes',
    'source-layer': 'postcode_sectors_englandgeojson',
    'type': 'fill',
    'filter': ["has", 'price_by_postcode_district_price_per_sq_m'],
    'paint': {
      'fill-opacity': 0.6,
      'fill-outline-color': 'white',
      'fill-color': {
        'property': 'price_by_postcode_district_price_per_sq_m',
        'type': 'interval',
        'stops': colourStops
      },
    },
  });

  map.addLayer({
    'id': 'postcodes_click',
    'source': 'postcodes',
    'source-layer': 'postcode_sectors_englandgeojson',
    'type': 'line',
    "filter": ["==", "PostDist", ""],
    'paint': {
      'line-color': 'white',
      'line-width': 2
    },
  });
};

export const updateMapFilters = (map: mapboxgl.Map | null, min: number, max: number) => {
  if (!map) return;
  
  map.setFilter("postcodes", ['all',
    [">=", "price_by_postcode_district_price_per_sq_m", min],
    ["<=", "price_by_postcode_district_price_per_sq_m", max]
  ]);
};
