import { useMap } from '../context/MapContext';
import { fixedLocations } from '../constants';

interface LocationLinksProps {
  showHighlights?: boolean;
}

const LocationLinks = ({ showHighlights = false }: LocationLinksProps) => {
  const { map, setSelectedPostcode } = useMap();

  const handleLocationClick = (city: string) => {
    if (!map) return;
    const location = fixedLocations[city];
    setSelectedPostcode(city);
    map.flyTo({
      center: [location.lng, location.lat],
      zoom: location.zoom
    });
  };

  if (showHighlights) {
    return (
      <p className="text-sm">
        Out of 2,280 postcode districts analysed, the top 70 were all in central London and the first 100 were all in Greater London. 
        The first outside central London is <a href="#" onClick={(e) => {
          e.preventDefault();
          handleLocationClick('TW10');
        }}>TW10 (Richmond)</a>, which is 75th at £7,689 per m2. 
        The most expensive postcode district outside Greater London is <a href="#" onClick={(e) => {
          e.preventDefault();
          handleLocationClick('OX2');
        }}>OX2 in Oxford</a>, which is 125th in the list at £5,203 per m2.
      </p>
    );
  }

  return (
    <p className="text-sm">
      Sale prices range from more than £20,000 per m2{' '}
      in <a href="#" onClick={(e) => {
        e.preventDefault();
        handleLocationClick('SW1X');
      }}>SW1X (Belgravia)</a>{' '}
      and <a href="#" onClick={(e) => {
        e.preventDefault();
        handleLocationClick('W1K');
      }}>W1K (Mayfair)</a>{' '}
      to under £1,000 in postcodes like{' '}
      <a href="#" onClick={(e) => {
        e.preventDefault();
        handleLocationClick('DN31');
      }}>DN31 (Grimsby)</a>{' '}
      and <a href="#" onClick={(e) => {
        e.preventDefault();
        handleLocationClick('CF43');
      }}>CF43 (Rhondda)</a>.{' '}
      Click to see details for a postcode, or zoom to{' '}
      <a href="#" onClick={(e) => {
        e.preventDefault();
        handleLocationClick('London');
      }}>London</a>,{' '}
      <a href="#" onClick={(e) => {
        e.preventDefault();
        handleLocationClick('Birmingham');
      }}>Birmingham</a>,{' '}
      <a href="#" onClick={(e) => {
        e.preventDefault();
        handleLocationClick('Manchester');
      }}>Manchester</a>.
    </p>
  );
};

export default LocationLinks;
