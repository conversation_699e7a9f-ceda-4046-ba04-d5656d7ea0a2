# == Schema Information
#
# Table name: postcode_areas
#
#  id                                                                                                    :bigint           not null, primary key
#  admin_county                                                                                          :string
#  admin_district                                                                                        :string
#  agency_tenant_uuid                                                                                    :uuid
#  altitude                                                                                              :integer
#  area_households                                                                                       :integer
#  area_population                                                                                       :integer
#  average_household_income                                                                              :integer
#  bbox_max_latitude                                                                                     :float
#  bbox_max_longitude                                                                                    :float
#  bbox_min_latitude                                                                                     :float
#  bbox_min_longitude                                                                                    :float
#  boundary_coordinates                                                                                  :json
#  built_up_area                                                                                         :string
#  center_latitude                                                                                       :float
#  center_longitude                                                                                      :float
#  city                                                                                                  :string
#  country                                                                                               :string
#  county                                                                                                :string
#  date_of_introduction                                                                                  :date
#  date_of_termination                                                                                   :date
#  discarded_at                                                                                          :datetime
#  doogal_details                                                                                        :jsonb
#  eastings                                                                                              :integer
#  epc_details_count                                                                                     :integer          default(0), not null
#  european_electoral_region                                                                             :string
#  feature_type                                                                                          :string
#  geometry_type                                                                                         :string
#  in_use                                                                                                :boolean          default(TRUE)
#  includes_industry                                                                                     :boolean          default(FALSE), not null
#  includes_new_developments                                                                             :boolean          default(FALSE), not null
#  incode                                                                                                :string
#  index_of_multiple_deprivation                                                                         :integer
#  is_incomplete_postcode                                                                                :boolean          default(FALSE)
#  is_outcode_only                                                                                       :boolean          default(FALSE)
#  kml_data                                                                                              :text
#  lsoa                                                                                                  :string
#  median_generic_property_uuid                                                                          :uuid
#  median_known_floor_area_sqm                                                                           :integer          default(0), not null
#  median_price_per_sqm                                                                                  :integer          default(0), not null
#  msoa                                                                                                  :string
#  nhs_ha                                                                                                :string
#  northings                                                                                             :integer
#  number_of_sales                                                                                       :integer
#  outcode                                                                                               :string
#  parent_uuid                                                                                           :uuid
#  parish                                                                                                :string
#  parliamentary_constituency                                                                            :string
#  pcode_coords                                                                                          :geography        point, 4326
#  percent_diff_outcode_price(Percentage difference from median price of the outcode area)               :integer          default(0), not null
#  place_type                                                                                            :string           default([]), is an Array
#  post_town                                                                                             :string
#  postal_code                                                                                           :string           not null
#  postcode_area_codes                                                                                   :jsonb
#  postcode_area_description                                                                             :string
#  postcode_area_details                                                                                 :jsonb
#  postcode_area_flags                                                                                   :integer          default(0), not null
#  postcode_area_geojson                                                                                 :jsonb
#  postcode_area_geometry                                                                                :jsonb
#  postcode_area_slug                                                                                    :string
#  postcode_area_streets                                                                                 :string           default([]), is an Array
#  postcode_area_tags                                                                                    :string           default([]), is an Array
#  postcode_area_title                                                                                   :string
#  postcode_average_property_price_cents                                                                 :bigint           default(0), not null
#  postcode_average_property_price_currency                                                              :string           default("EUR"), not null
#  predominant_property_type                                                                             :string
#  primary_care_trust                                                                                    :string
#  quality                                                                                               :integer
#  realty_assets_count                                                                                   :integer          default(0), not null
#  region                                                                                                :string
#  relevance                                                                                             :decimal(3, 2)
#  rural_urban                                                                                           :string
#  sold_transactions_count                                                                               :integer          default(0), not null
#  suitable_for_ppu_analysis(Determines if this property can be included in price per unit calculations) :boolean          default(FALSE), not null
#  uk_grid_reference                                                                                     :string
#  uk_ward                                                                                               :string
#  uk_ward_code                                                                                          :string
#  uuid                                                                                                  :uuid
#  what_three_words                                                                                      :string
#  created_at                                                                                            :datetime         not null
#  updated_at                                                                                            :datetime         not null
#  mapit_id                                                                                              :string
#
# Indexes
#
#  index_postcode_areas_on_agency_tenant_uuid                    (agency_tenant_uuid)
#  index_postcode_areas_on_center_latitude_and_center_longitude  (center_latitude,center_longitude)
#  index_postcode_areas_on_discarded_at                          (discarded_at)
#  index_postcode_areas_on_mapit_id                              (mapit_id)
#  index_postcode_areas_on_median_generic_property_uuid          (median_generic_property_uuid)
#  index_postcode_areas_on_parent_uuid                           (parent_uuid)
#  index_postcode_areas_on_pcode_coords                          (pcode_coords) USING gist
#  index_postcode_areas_on_postcode_area_flags                   (postcode_area_flags)
#  index_postcode_areas_on_postcode_area_slug                    (postcode_area_slug)
#  index_postcode_areas_on_uuid                                  (uuid)
#
class PostcodeArea < ApplicationRecord
  include Json::PostcodeAreaJson
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # include Discard::Model
  # has_paper_trail

  reverse_geocoded_by :center_latitude, :center_longitude # , address: :street_address
  # adds methods like:
  # PostcodeArea.last.nearbys(0.3).length
  # PostcodeArea.last.distance_from([40.714,-100.234])
  # PostcodeArea.near([40.71, -100.23], 50)              # venues within 50 miles of a point

  store_attribute :postcode_area_details, :touching_postcodes, :string, default: ''
  store_attribute :postcode_area_details, :nearby_postcodes, :json, default: {}
  # would be nicer to be able to calculate below on the fly:
  store_attribute :postcode_area_details, :similar_postcodes, :json, default: {}

  # has_many :child_postal_codes, class_name: 'PostcodeArea', foreign_key: 'parent_id'
  # belongs_to :parent_postal_code, class_name: 'PostcodeArea', optional: true
  has_many :child_postal_codes, class_name: 'PostcodeArea', foreign_key: 'parent_uuid'
  belongs_to :parent_postal_code, class_name: 'PostcodeArea', foreign_key: 'parent_uuid', optional: true

  has_many :realty_assets, primary_key: 'uuid', foreign_key: 'postcode_area_uuid'
  has_many :generic_properties, primary_key: 'uuid', foreign_key: 'postcode_area_uuid'
  belongs_to :median_generic_property, class_name: 'GenericProperty', primary_key: 'uuid',
                                       foreign_key: 'median_generic_property_uuid', optional: true

  has_many :epc_details, foreign_key: 'postcode_area_uuid', primary_key: :uuid
  has_many :postcode_geo_clusters, foreign_key: 'postcode_area_uuid', primary_key: :uuid
  has_many :geo_clusters, through: :postcode_geo_clusters

  has_many :sold_transactions, primary_key: 'uuid', foreign_key: 'postcode_area_uuid'

  has_many :sold_transaction_epcs, through: :sold_transactions

  has_many :synthetic_sold_transactions,
           -> { where(is_synthetic_transaction: true) },
           primary_key: 'uuid',
           class_name: 'SoldTransaction',
           foreign_key: 'postcode_area_uuid'

  has_many :real_sold_transactions,
           -> { real },
           primary_key: 'uuid',
           foreign_key: 'postcode_area_uuid',
           class_name: 'SoldTransaction'
  # # Assuming each PostcodeArea can be part of one primary and one secondary cluster:
  # belongs_to :primary_cluster, class_name: 'PostcodeAreaCluster', foreign_key: :primary_cluster_uuid, primary_key: :uuid, optional: true
  # belongs_to :secondary_cluster, class_name: 'PostcodeAreaCluster', foreign_key: :secondary_cluster_uuid, primary_key: :uuid, optional: true

  has_many :geo_photos, lambda {
    order 'geo_photo_sort_order asc'
  }, class_name: 'GeoPhoto', primary_key: 'uuid',
     foreign_key: 'postcode_area_uuid',
     dependent: :destroy

  def create_postcode_photo
    photo_data = {}
    GeoPhoto.create_geo_photo(
      photo_data:,
      center_latitude:,
      center_longitude:,
      postcode_area_uuid: uuid
    )
  end

  def update_sold_transactions
    # called from geo_cluster after a land registry ppd import
    SoldTransaction.where(st_postal_code: postal_code).each do |s_trans|
      s_trans.update!(postcode_area_uuid: uuid)
    end
  end

  # If using the JSONB field for association:
  # Note: This part is more complex and less performant, used here for demonstration:
  def self.in_cluster(cluster)
    where('uuid = ANY(ARRAY[:uuids]::uuid[])', uuids: cluster.postcode_area_uuids)
  end

  validates :postal_code, presence: true, uniqueness: true

  monetize :postcode_average_property_price_cents, with_model_currency: :postcode_average_property_price_currency
  # def formatted_display_price
  #   price_sale_current.format
  # end

  def load_and_parse_geojson(postcode_file_name)
    # step 1 of setting postcode_area_geojson from local geojson file
    file_path = Rails.root.join('db', 'external_sites', "#{postcode_file_name}.geojson")

    unless File.exist?(file_path)
      Rails.logger.error "GeoJSON file not found at #{file_path}"
      return nil
    end

    geojson_data = File.read(file_path)
    factory = RGeo::Geographic.simple_mercator_factory

    begin
      RGeo::GeoJSON.decode(geojson_data, json_parser: :json, geo_factory: factory)
    rescue StandardError => e
      Rails.logger.error "Error while parsing GeoJSON: #{e.message}"
      nil
    end
  end

  def update_geojson_with_matching_feature(feature_collection, postcode_name)
    # step 2 of setting postcode_area_geojson from local geojson file
    if feature_collection
      Rails.logger.info 'Successfully parsed GeoJSON file.'

      target_property = 'mapit_code'
      target_value = postcode_name

      # feature_collection is an array of RGeo::GeoJSON::Feature objects
      # - in this case likely all of cv11 geojsons so need to find the single one that matches the postcode_name
      # for this postcode_area
      matching_features = feature_collection.select do |feature|
        feature.properties[target_property] == target_value
      end

      if matching_features.any?
        matched_feature = matching_features.first
        self_geojson = { primary: matched_feature.as_json }

        # # Create a factory for geographic coordinates
        # factory = RGeo::Geographic.spherical_factory
        # # Create a polygon from the coordinates
        # polygon = factory.polygon(factory.linear_ring(matched_feature.geometry.coordinates.map { |lng, lat| factory.point(lng, lat) }))
        # # Calculate the centroid
        # centroid = polygon.centroid

        self.postcode_area_geojson = self_geojson
        save!
        Rails.logger.info "GeoJSON field updated for PostArea with postcode_name: #{postcode_name}"
      else
        Rails.logger.warn "No features found with #{target_property} = #{target_value}"
      end
    else
      Rails.logger.error 'Failed to parse or load the GeoJSON file.'
    end
  end

  def update_geojson_from_postcode
    return if postcode_area_geojson.present?

    postcode_file_name = 'CV11'
    postcode_name = postal_code.gsub(/\s+/, '') if postal_code.present?
    # step 1 of setting postcode_area_geojson from local geojson file
    feature_collection = load_and_parse_geojson(postcode_file_name)
    # step 2 of setting postcode_area_geojson from local geojson file
    update_geojson_with_matching_feature(feature_collection, postcode_name)
  end

  def update_bounding_box_from_geojson
    return unless postcode_area_geojson && postcode_area_geojson['primary'] && postcode_area_geojson['primary']['geometry'] && postcode_area_geojson['primary']['geometry']['coordinates']

    geometry_type = postcode_area_geojson['primary']['geometry']['type']
    coordinates = postcode_area_geojson['primary']['geometry']['coordinates']

    lons = []
    lats = []

    if geometry_type == 'Polygon'
      polygon_coords = coordinates[0]
      polygon_coords.each do |coord|
        lons << coord[0]
        lats << coord[1]
      end
    elsif geometry_type == 'MultiPolygon'
      coordinates.each do |polygon|
        polygon[0].each do |coord|
          lons << coord[0]
          lats << coord[1]
        end
      end
    else
      # Optionally handle other types or raise an error
      raise "Unsupported geometry type: #{geometry_type}"
    end

    min_x, max_x = lons.minmax
    min_y, max_y = lats.minmax

    # Calculate center point
    center_x = (min_x + max_x) / 2.0
    center_y = (min_y + max_y) / 2.0

    puts "Bounding Box: [#{min_x}, #{min_y}, #{max_x}, #{max_y}]"
    # Update model attributes with bounding box information
    update(
      bbox_max_latitude: max_y,
      bbox_max_longitude: max_x,
      bbox_min_latitude: min_y,
      bbox_min_longitude: min_x,
      center_longitude: center_x,
      center_latitude: center_y
    )
  end

  def mapbox_id
    # TODO: - add above to schema
    primary_care_trust
  end

  # before_save :set_parent_postal_code

  # Regular expression for UK postal codes
  UK_POSTAL_CODE_REGEX = /\A([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9]?[A-Za-z])))) [0-9][A-Za-z]{2})\z/i

  # before_validation :normalize_and_validate_uk_postal_code

  def self.find_by_uk_postal_code(postal_code)
    normalized_postal_code = normalize_and_validate_uk_postal_code(postal_code)
    find_by(postal_code: normalized_postal_code)
  end

  def self.find_or_create_by_uk_postal_code(postal_code)
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    normalized_postal_code = normalize_and_validate_uk_postal_code(postal_code)
    puts "normalized_postal_code: #{normalized_postal_code}"
    pc_sections = normalized_postal_code.split(' ')
    pc_area = nil
    if pc_sections.length == 2 && pc_sections[1].present?
      postcode_area_slug = normalized_postal_code.gsub(/\s+/, '').strip.downcase
      pc_area = find_or_create_by(postal_code: normalized_postal_code)
      pc_area.update!(
        postcode_area_slug:,
        outcode: pc_sections[0], incode: pc_sections[1]
      )
    end
    pc_area
    # find_or_create_by(postal_code: normalized_postal_code)
  end

  def self.normalize_and_validate_uk_postal_code(postal_code)
    normalized = postal_code.to_s.upcase
    # normalized = normalize_uk_postal_code(postal_code)
    unless normalized.match?(UK_POSTAL_CODE_REGEX)
      throw :abort, {
        message: "Invalid UK postal code #{normalized}",
        original: postal_code,
        normalized:
      }
      # throw :abort # This will halt the callback chain, preventing save if validation fails
    end

    normalized
  end

  # def self.normalize_uk_postal_code(postal_code)
  #   # Convert to string and upcase for consistency
  #   normalized = postal_code.to_s.upcase

  #   if normalized.match?(UK_POSTAL_CODE_REGEX)
  #     # For valid UK postal codes, normalize spacing
  #     normalized.gsub(/\s+/, ' ').strip
  #   else
  #     # For non-UK or invalid UK postal codes, remove all spaces
  #     normalized.gsub(/\s+/, '')
  #   end
  # end

  # def set_parent_postal_code
  #   parent_code = postal_code.split(' ').first
  #   self.parent_postal_code = PostcodeArea.find_by(postal_code: parent_code) if parent_code != postal_code
  # end
end
