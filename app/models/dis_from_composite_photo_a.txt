# == Schema Information
#
# Table name: dossier_info_sources
#
#  id                                         :bigint           not null, primary key
#  agency_tenant_uuid                         :uuid
#  associated_listing_uuid                    :uuid
#  discarded_at                               :datetime
#  dossier_asset_uuid                         :uuid
#  dossier_info_source_aasm_state             :string
#  dossier_info_source_details                :jsonb
#  dossier_info_source_flags                  :integer          default(0), not null
#  dossier_info_source_rating                 :integer          default(0), not null
#  is_for_most_comparable_property_to_primary :boolean          default(FALSE)
#  is_source_for_primary_property             :boolean          default(FALSE)
#  llm_interaction_uuid                       :uuid
#  property_is_for_rent                       :boolean          default(FALSE)
#  property_is_for_sale                       :boolean          default(TRUE)
#  realty_asset_photo_uuid                    :uuid
#  realty_dossier_uuid                        :uuid
#  realty_search_query_uuid                   :uuid
#  source_is_epc                              :boolean          default(FALSE)
#  source_is_listing                          :boolean          default(TRUE)
#  source_is_photo                            :boolean          default(FALSE)
#  source_is_sold_transaction                 :boolean          default(FALSE)
#  uuid                                       :uuid
#  created_at                                 :datetime         not null
#  updated_at                                 :datetime         not null
#
# Indexes
#
#  index_dossier_info_sources_on_agency_tenant_uuid          (agency_tenant_uuid)
#  index_dossier_info_sources_on_associated_listing_uuid     (associated_listing_uuid)
#  index_dossier_info_sources_on_discarded_at                (discarded_at)
#  index_dossier_info_sources_on_dossier_asset_uuid          (dossier_asset_uuid)
#  index_dossier_info_sources_on_dossier_info_source_flags   (dossier_info_source_flags)
#  index_dossier_info_sources_on_dossier_info_source_rating  (dossier_info_source_rating)
#  index_dossier_info_sources_on_realty_asset_photo_uuid     (realty_asset_photo_uuid)
#  index_dossier_info_sources_on_realty_dossier_uuid         (realty_dossier_uuid)
#  index_dossier_info_sources_on_uuid                        (uuid)
#
class DisFromCompositePhotoA < DossierInfoSource
  default_scope { info_source_is_composite_image_a.where(source_is_photo: true) }

  # Define stored attributes in dossier_info_source_details
  store_attribute :dossier_info_source_details, :description_medium, :string, default: ''
  store_attribute :dossier_info_source_details, :main_section_or_room_details, :json, default: []
  store_attribute :dossier_info_source_details, :other_section_or_room_details, :json, default: []
  store_attribute :dossier_info_source_details, :sub_images, :json, default: []
  store_attribute :dossier_info_source_details, :unique_rooms_or_sections, :json, default: []

  # below is already defined in base class
  # belongs_to :realty_dossier, foreign_key: :realty_dossier_uuid, primary_key: :uuid, optional: true

  # Main method to create or update dossier info source from AI interaction
  # Called from CompositePhotoAnalyser
  def self.find_or_create_from_ai_interaction(
    llm_interaction, dossier_item, target_dossier_asset
  )
    return unless valid_llm_interaction?(llm_interaction)

    chosen_response_json = JSON.parse(llm_interaction.chosen_response)
    # sanity check here in case the llm respond does not correspond to the target_dossier_asset
    raise 'response might be for wrong dossier' if dossier_item.id.to_s != chosen_response_json['dossier_item_id']
    raise 'response might be for wrong asset' if target_dossier_asset.id.to_s != chosen_response_json['target_dossier_asset_id']

    sub_images_ids = chosen_response_json['sub_image_descriptions'].pluck 'image_id'
    only_valid_images_included = sub_images_ids.all? { |element_a| target_dossier_asset.asset_photo_short_ids.include?(element_a.to_i) }

    raise 'response has invalid images' unless only_valid_images_included

    photo_info_source = create_or_update_info_source(llm_interaction, dossier_item, target_dossier_asset)

    process_composite_photo_data(photo_info_source, chosen_response_json, target_dossier_asset)
    photo_info_source
  end

  # Validates if LLM interaction has a chosen response
  def self.valid_llm_interaction?(llm_interaction)
    llm_interaction.chosen_response.present?
  end

  # # Parses JSON string into a Ruby hash
  # def self.parse_chosen_response(chosen_response)
  #   JSON.parse(chosen_response)
  # end

  # Creates or finds the dossier info source record
  def self.create_or_update_info_source(llm_interaction, dossier_item, target_dossier_asset)
    info_source = find_or_create_by(
      llm_interaction_uuid: llm_interaction.uuid,
      dossier_asset_uuid: target_dossier_asset.uuid,
      realty_dossier_uuid: dossier_item.uuid,
      agency_tenant_uuid: llm_interaction.agency_tenant_uuid,
      source_is_photo: true
    )
    # Manual update required as of 3 Apr 2025
    info_source.update!(info_source_is_composite_image_a: true)
    info_source
  end

  # Processes all photo-related data from the composite analysis
  def self.process_composite_photo_data(photo_info_source, response_json, _target_dossier_asset)
    sub_images = response_json['sub_image_descriptions'] || []
    # unique_rooms = response_json['unique_rooms_or_sections'] || []

    section_details = process_sub_images(sub_images)
    # process_unique_rooms(unique_rooms, target_dossier_asset)
    update_photo_info_source(photo_info_source, sub_images, section_details)
  end

  # Processes sub-images and returns section details
  def self.process_sub_images(sub_images)
    main_details = []
    other_details = []

    sub_images.each do |sub_image|
      update_realty_asset_photo(sub_image)
      # collect_section_details(sub_image, main_details, other_details)
    end

    { main: main_details, other: other_details }
  end

  # Updates RealtyAssetPhoto record if needed
  def self.update_realty_asset_photo(sub_image)
    photo = RealtyAssetPhoto.find_by(id: sub_image['image_id'])
    # puts "trying to update  photo: #{photo.id}"

    # return unless photo && photo.photo_title.blank?

    update_vals = {
      flag_is_parsed_by_llm: true,
      photo_title: sub_image['catchy_title'],
      photo_description: sub_image['general_description'],
      photo_ai_desc: sub_image['general_description'],
      raw_ai_analysis: sub_image
    }
    # keys in sub_image = %w[image_id
    #         catchy_title
    #         general_description
    #         style
    #         dominant_color
    #         significant_items
    #         condition
    #         unique_features
    #         estimated_area_sq_feet
    #         is_outside]

    photo.update!(
      update_vals
    )
  end

  # # Processes unique rooms/sections and updates DossierAssetParts
  # def self.process_unique_rooms(unique_rooms, target_dossier_asset)
  #   unique_rooms.each do |room|
  #     asset_part = update_dossier_asset_part(room, target_dossier_asset)
  #     update_asset_part_photos(asset_part, room, target_dossier_asset)
  #   end
  # end

  # # Creates or updates a DossierAssetPart record
  # def self.update_dossier_asset_part(room, target_dossier_asset)
  #   slug = room['section_or_room_identifier'].parameterize
  #   asset_part = target_dossier_asset.dossier_asset_parts.find_or_create_by(asset_part_slug: slug)
  #   asset_part.undiscard! if asset_part.discarded?
  #   asset_part
  # end

  # Updates the photo info source with final analysis data
  def self.update_photo_info_source(photo_info_source, sub_images, section_details)
    photo_info_source.update!(
      sub_images: sub_images,
      # unique_rooms_or_sections: unique_rooms,
      main_section_or_room_details: section_details[:main],
      other_section_or_room_details: section_details[:other]
      # description_medium: "Composite image analysis containing #{sub_images.length} sub-images."
    )
  end
end
