# == Schema Information
#
# Table name: realty_asset_photos
#
#  id                            :bigint           not null, primary key
#  agency_tenant_uuid            :uuid
#  agency_uuid                   :uuid
#  cloned_from_uuid              :string
#  content_type                  :string
#  details                       :jsonb
#  discarded_at                  :datetime
#  external_img_details          :jsonb
#  file_size                     :integer
#  folder                        :string           default("hhh-photos")
#  height                        :string
#  image                         :string
#  is_ai_generated_photo         :boolean          default(FALSE)
#  is_external_photo             :boolean          default(FALSE)
#  llm_interaction_uuid          :uuid
#  new_build_listing_uuid        :uuid
#  photo_description             :string
#  photo_flags                   :integer          default(0), not null
#  photo_gen_prompt              :text
#  photo_slug                    :string
#  photo_title                   :string
#  process_options               :json
#  realty_asset_photo_tags       :string           default([]), is an Array
#  realty_asset_uuid             :uuid
#  remote_photo_url              :string
#  rental_listing_uuid           :uuid
#  sale_listing_uuid             :uuid
#  site_visitor_token            :string
#  sold_transaction_uuid         :uuid
#  sort_order                    :integer
#  sort_order_new_build          :integer
#  sort_order_rental             :integer
#  sort_order_sale               :integer
#  translations                  :jsonb
#  user_uuid                     :uuid
#  uuid                          :uuid
#  visible_for_new_build_listing :boolean          default(FALSE)
#  visible_for_rental_listing    :boolean          default(FALSE)
#  visible_for_sale_listing      :boolean          default(FALSE)
#  width                         :string
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  psq_visit_id                  :bigint
#
# Indexes
#
#  index_realty_asset_photos_on_agency_tenant_uuid      (agency_tenant_uuid)
#  index_realty_asset_photos_on_discarded_at            (discarded_at)
#  index_realty_asset_photos_on_is_ai_generated_photo   (is_ai_generated_photo)
#  index_realty_asset_photos_on_is_external_photo       (is_external_photo)
#  index_realty_asset_photos_on_new_build_listing_uuid  (new_build_listing_uuid)
#  index_realty_asset_photos_on_realty_asset_uuid       (realty_asset_uuid)
#  index_realty_asset_photos_on_rental_listing_uuid     (rental_listing_uuid)
#  index_realty_asset_photos_on_sale_listing_uuid       (sale_listing_uuid)
#  index_realty_asset_photos_on_sold_transaction_uuid   (sold_transaction_uuid)
#  index_realty_asset_photos_on_uuid                    (uuid)
#
class RealtyAssetPhoto < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # has_one_attached :realty_image, variants: {
  #   thumb: { resize: '100x100' },
  #   medium: { resize: '300x300' }
  # }
  # above seemed to be the old way to do it..

  # https://edgeguides.rubyonrails.org/active_storage_overview.html

  has_one :zac_active_storage_attachment, foreign_key: 'record_id', primary_key: 'id'
  has_one :zac_active_storage_blob, through: :zac_active_storage_attachment,
                                    source: :blob, class_name: 'ZacActiveStorageBlob'
  #  primary_key: 'id', foreign_key: 'blob_id', optional: true

  has_one_attached :realty_image do |attachable|
    # attachable.variant :thumb, resize_to_limit: [100, 100] #, preprocessed: true
    attachable.variant :small, resize_to_limit: [300, 300] # , preprocessed: true
    attachable.variant :medium, resize_to_limit: [500, 500], preprocessed: true
    attachable.variant :large, resize_to_limit: [800, 800] # , preprocessed: true
    # attachable.variant :large, resize: "600x600" #, monochrome: true
  end

  # May 2025 - even though below is correct, I want to avoid accessing the
  # dossier_jots directly from here as a job might be created from a
  # different dossier from which the photo is being viewed
  # has_many :dossier_jots, class_name: 'DossierJot',
  #                         primary_key: 'uuid', foreign_key: 'primary_photo_uuid'

  store_attribute :details, :raw_ai_analysis, :json, default: {}

  # def realty_image_medium_url
  #   realty_image ? realty_image.medium_url : nil
  #   # return realty_image_url_variant(:medium)
  # end

  # mount_uploader :image, RealtyAssetPhotoUploader
  # acts_as_tenant gem...  - usually counter_cache would just be set to true above
  # counter_culture :agency_tenant, column_name: 'realty_asset_photos_count'
  # RealtyAssetPhoto.counter_culture_fix_counts

  # visitable :psq_visit

  # has_many :photo_jots, foreign_key: 'image_uuid', primary_key: 'uuid', class_name: 'Jot'
  # 9 apr 2025 - now that I use composit_photos below may well get deprecated
  # has_many :llm_insights, foreign_key: 'target_photo_uuid', primary_key: 'uuid',
  #                         class_name: 'LiaToAnalysePhotos'

  belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  belongs_to :realty_asset, optional: true, primary_key: 'uuid', foreign_key: 'realty_asset_uuid'
  belongs_to :sale_listing, optional: true, primary_key: 'uuid', foreign_key: 'sale_listing_uuid'
  belongs_to :rental_listing, optional: true, primary_key: 'uuid', foreign_key: 'rental_listing_uuid'
  counter_culture :realty_asset, column_name: 'ra_photos_count'
  counter_culture :sale_listing, column_name: 'sl_photos_count'
  counter_culture :rental_listing, column_name: 'rl_photos_count'
  # RealtyAssetPhoto.counter_culture_fix_counts

  extend Mobility
  translates :photo_ai_desc

  # acts_as_list column: :sort_order

  include Discard::Model

  include FlagShihTzu
  has_flags 1 => :flag_is_hidden,
            2 => :flag_is_local_photo,
            3 => :flag_is_parsed_by_llm,
            :column => 'photo_flags'

  def is_remote_photo?
    # Apr 2022 TODO- use flag-tzu (flag_is_local_photo) for this
    external_img_details.present? && external_img_details['remote_image'].present?
  end

  # def populate_photo_ai_desc
  # end

  def has_full_url_saved?
    # Jan 2022 TODO- when creating photo and request.url is available
    # should save full_image external_img_details in json field for easy retrieval later
    # Will need to pay attention to updates
    external_img_details.present? && external_img_details['full_image'].present?
  end

  def attributes_with_full_urls
    # Nov 2024 - added this method for SaleListingsExporter
    # so I could save full url to S3 image in json field
    attributes unless external_img_details.blank? && realty_image.attached?
    attributes['external_img_details'][:remote_image] = image_details
    # below 2 attributes have a class of NilClass
    # and it seems setting them as below is not working
    attributes['photo_slug'] = realty_image.blob.url.to_s
    attributes['remote_photo_url'] = realty_image.blob.url.to_s
    attributes
    # attributes.merge(
    #   'full_image' => full_image_url('main'),
    #   'full_image_small_fill' => full_image_url('small_fill'),
    #   'full_image_small_fit' => full_image_url('small_fit')
    # )
  end

  def image_details
    if is_remote_photo?

      image_details = {}
      # image_details = {
      #   "url": "/uploads/burns-be1/realty_asset_photo/8029e606da-1-7980e48d732b6fc85c14b43b94d2aa54-jpg.jpg",
      #   "small_fill": {
      #     "url": "/uploads/burns-be1/realty_asset_photo/small_fill_8029e606da-1-7980e48d732b6fc85c14b43b94d2aa54-jpg.jpg",
      #   },
      #   "small_fit": {
      #     "url": "/uploads/burns-be1/realty_asset_photo/small_fit_8029e606da-1-7980e48d732b6fc85c14b43b94d2aa54-jpg.jpg",
      #   },
      # }
      image_details = external_img_details['remote_image'] if is_remote_photo?
      # image_details = realty_image.as_json if realty_image.present?
      return image_details
    end
    return nil unless realty_image.attached?

    {
      url: realty_image.blob.url, # URL for the original image
      small: {
        url: realty_image.blob.url
        #  realty_image.variant(:small).processed.service_url
      },
      medium: {
        url: realty_image.blob.url
        #  realty_image.variant(:medium).processed.service_url
      },
      large: {
        url: realty_image.blob.url
      }
      # url: Rails.application.routes.url_helpers.url_for(realty_image),
      # small_fit: {
      #   url: Rails.application.routes.url_helpers.url_for(
      #     realty_image.variant(resize_to_fit: [200, 200])
      #   )
      # },
      # small_fill: {
      #   url: Rails.application.routes.url_helpers.url_for(
      #     realty_image.variant(resize_to_fill: [200, 200])
      #   )
      # }
    }
  rescue ActiveStorage::FileNotFoundError
    nil
  end

  # def default_image_url
  #   remote_photo_url || full_image_url
  # end

  def full_image_url(image_type = 'main', client_base_url = nil)
    return image_details['url'] if image_details['url'].present?
    return 'https://via.placeholder.com/300x200/09f.png/fff%20C/' unless realty_image.url.present?

    image_url = realty_image.url
    image_url = realty_image.small_fill.url if image_type == 'small_fill'
    image_url = realty_image.small_fit.url if image_type == 'small_fit'
    return client_base_url + image_url if client_base_url

    image_url
  end

  def as_json(options = nil)
    super({ only: %w[
              uuid
              realty_asset_uuid
              sort_order image
              file_size content_type
              height width
            ],
            methods: [] }.merge(options || {}))
  end

  def resize_image
    return unless realty_image.attached?

    original_blob = realty_image.blob
    realty_image.blob.open(tmpdir: Dir.tmpdir) do |file|
      img = MiniMagick::Image.open(file.path)
      img.resize '1000x1000>'
      img.strip

      # Gradually reduce quality until file size is under 1MB
      quality = 90
      while img.size > 1.megabyte && quality > 10
        img.quality quality
        img.write(file.path)
        quality -= 10
      end

      # Re-upload the processed file to S3
      processed_blob = ActiveStorage::Blob.create_and_upload!(
        io: File.open(file.path),
        filename: realty_image.blob.filename,
        content_type: realty_image.blob.content_type
      )

      # Attach the new blob to the record
      realty_image.attach(processed_blob)
    end
    puts "original_blob : #{original_blob}"
  end

  # below method suggested by gpt was failing but above worked
  # # Method to generate a processed version under 1 MB
  # def processed_image
  #   realty_image.variant(
  #     resize_to_limit: [1920, 1080], # Resize dimensions (adjust as needed)
  #     saver: { quality: 75 } # Compression level (MiniMagick specific)
  #   ).processed
  # end

  # Ensure the attached image is purged from S3 when the record is destroyed
  before_destroy :purge_realty_image

  # before_save :ensure_image_is_under_1_mb

  private

  # def ensure_image_is_under_1_mb
  #   return unless realty_image.attached?

  #   processed = processed_image
  # end

  def purge_realty_image
    realty_image.purge_later if realty_image.attached?
  end
end
