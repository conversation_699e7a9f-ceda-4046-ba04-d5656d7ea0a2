# == Schema Information
#
# Table name: geo_photos
#
#  id                    :bigint           not null, primary key
#  agency_tenant_uuid    :uuid
#  content_type          :string
#  details               :jsonb
#  discarded_at          :datetime
#  external_img_details  :jsonb
#  file_size             :integer
#  folder                :string           default("geo-photos")
#  generic_property_uuid :uuid
#  geo_cluster_uuid      :uuid
#  geo_object_type       :string
#  geo_object_uuid       :uuid
#  geo_photo_coords      :geography        point, 4326
#  geo_photo_flags       :integer          default(0), not null
#  geo_photo_latitude    :float
#  geo_photo_longitude   :float
#  geo_photo_postal_code :string
#  geo_photo_slug        :string
#  geo_photo_sort_order  :integer
#  geo_photo_tags        :string           default([]), is an Array
#  height                :string
#  image                 :string
#  is_ai_generated_photo :boolean          default(FALSE)
#  is_external_photo     :boolean          default(FALSE)
#  is_g_place            :boolean          default(FALSE)
#  is_osm_place          :boolean          default(TRUE)
#  photo_description     :string
#  photo_gen_prompt      :text
#  photo_title           :string
#  postcode_area_uuid    :uuid
#  process_options       :json
#  relevant_place_uuid   :uuid
#  remote_photo_url      :string
#  translations          :jsonb
#  uuid                  :uuid
#  width                 :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
# Indexes
#
#  index_geo_photos_on_agency_tenant_uuid                          (agency_tenant_uuid)
#  index_geo_photos_on_discarded_at                                (discarded_at)
#  index_geo_photos_on_geo_cluster_uuid                            (geo_cluster_uuid)
#  index_geo_photos_on_geo_photo_coords                            (geo_photo_coords) USING gist
#  index_geo_photos_on_geo_photo_latitude_and_geo_photo_longitude  (geo_photo_latitude,geo_photo_longitude)
#  index_geo_photos_on_is_ai_generated_photo                       (is_ai_generated_photo)
#  index_geo_photos_on_is_external_photo                           (is_external_photo)
#  index_geo_photos_on_postcode_area_uuid                          (postcode_area_uuid)
#  index_geo_photos_on_relevant_place_uuid                         (relevant_place_uuid)
#  index_geo_photos_on_uuid                                        (uuid)
#
class GeoPhoto < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  has_one_attached :geo_image do |attachable|
    # attachable.variant :thumb, resize_to_limit: [100, 100] #, preprocessed: true
    attachable.variant :small, resize_to_limit: [300, 300] # , preprocessed: true
    attachable.variant :medium, resize_to_limit: [500, 500], preprocessed: true
    attachable.variant :large, resize_to_limit: [800, 800] # , preprocessed: true
    # attachable.variant :large, resize: "600x600" #, monochrome: true
  end

  belongs_to :geo_cluster, optional: true, primary_key: 'uuid', foreign_key: 'geo_cluster_uuid'
  belongs_to :postcode_area, optional: true, primary_key: 'uuid', foreign_key: 'postcode_area_uuid'
  # counter_culture :postcode_area, column_name: 'ra_photos_count'

  # extend Mobility
  # translates :photo_title, :photo_ai_desc

  # # acts_as_list column: :sort_order

  # include FlagShihTzu
  # has_flags 1 => :flag_is_hidden,
  #           2 => :flag_is_local_photo,
  #           :column => 'photo_flags'

  # def is_remote_photo?
  #   # Apr 2022 TODO- use flag-tzu (flag_is_local_photo) for this
  #   external_img_details.present? && external_img_details['remote_image'].present?
  # end

  def self.create_geo_photo(
    photo_data:, center_latitude:, center_longitude:,
    geo_cluster_uuid: nil, postcode_area_uuid: nil
  )
    # ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    location_lat_lon = "#{center_latitude},#{center_longitude}"
    photo_slug = location_lat_lon.parameterize
    photo_filename = photo_slug || File.basename(pcode_photo_url)

    # Check if a photo already exists for the given latitude and longitude
    existing_photo = GeoPhoto.find_by(geo_photo_slug: photo_slug)
    return existing_photo if existing_photo

    pcode_photo_url = construct_street_view_url(
      { location: location_lat_lon }
    )
    fastimage = FastImage.new(pcode_photo_url)
    content_length = fastimage.content_length || 0
    width, height = fastimage.size || [0, 0]

    # Skip small images
    unless content_length >= 10_000
      puts "Skipping small image: #{pcode_photo_url}"
      return
    end

    # Fetch the image from the remote URL
    file = URI.open(pcode_photo_url)
    rap = GeoPhoto.create(
      geo_cluster_uuid:,
      postcode_area_uuid:,
      details: photo_data,
      file_size: content_length,
      width:,
      height:,
      content_type: fastimage.type,
      geo_photo_slug: photo_slug,
      geo_photo_latitude: center_latitude,
      geo_photo_longitude: center_longitude
    ).tap do |photo|
      # Attach the image file to the Active Storage association
      photo.geo_image.attach(io: file, filename: photo_filename, content_type: fastimage.type)
    end
    puts "Created #{rap.id} from URL #{pcode_photo_url}"
    rap
  rescue OpenURI::HTTPError, StandardError => e
    puts "Error creating photo from URL #{pcode_photo_url}: #{e.message}"
    Rails.logger.error "Error creating photo from URL #{pcode_photo_url}: #{e.message}"
    nil
  end
  # def self.create_geo_photo(
  #   photo_data:, center_latitude:, center_longitude:, geo_cluster_uuid: nil,
  #   postcode_area_uuid: nil
  # )
  #   ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
  #   location_lat_lon = "#{center_latitude},#{center_longitude}"
  #   pcode_photo_url = construct_street_view_url(
  #     { location_lat_lon: }
  #   )
  #   fastimage = FastImage.new(pcode_photo_url)
  #   content_length = fastimage.content_length || 0
  #   width, height = fastimage.size || [0, 0]
  #   photo_slug = location_lat_lon.parameterize
  #   photo_filename = photo_slug || File.basename(pcode_photo_url)

  #   # Skip small images
  #   unless content_length >= 10_000
  #     puts "Skipping small image: #{pcode_photo_url}"
  #     return
  #   end

  #   # Fetch the image from the remote URL
  #   file = URI.open(pcode_photo_url)
  #   rap = GeoPhoto.create(
  #     geo_cluster_uuid:,
  #     postcode_area_uuid:,
  #     details: photo_data,
  #     file_size: content_length,
  #     width:,
  #     height:,
  #     content_type: fastimage.type,
  #     photo_slug:
  #   ).tap do |photo|
  #     # Attach the image file to the Active Storage association
  #     photo.geo_image.attach(io: file, filename: photo_filename, content_type: fastimage.type)
  #   end
  #   puts "Created #{rap.id} from URL #{pcode_photo_url}"
  #   rap
  # rescue OpenURI::HTTPError, StandardError => e
  #   puts "Error creating photo from URL #{pcode_photo_url}: #{e.message}"
  #   Rails.logger.error "Error creating photo from URL #{pcode_photo_url}: #{e.message}"
  #   nil
  # end

  def self.construct_street_view_url(params)
    # also have g_maps_key and g_places_key in credentials
    g_maps_static_key = Rails.application.credentials.config[:g_maps_static_key]
    # pcode_photo_url = 'https://maps.googleapis.com/maps/api/streetview?location=52.52675,-1.46076&size=800x400&key=AIzaSyAu5sCjGPAq5gzXoUmPTb6iFMnvYKaB3IQ&heading=0&fov=120'
    base_url = 'https://maps.googleapis.com/maps/api/streetview'

    query_params = {
      'location' => params[:location_lat_lon] || '52.52675,-1.46076',
      'size' => params[:size] || '800x400',
      'key' => params[:key] || g_maps_static_key, # 'AIzaSyAu5sCjGPAq5gzXoUmPTb6iFMnvYKaB3IQ',
      'heading' => params[:heading] || '0',
      'fov' => params[:fov] || '120'
    }
    # Filter out nil values
    query_params = query_params.compact
    query_string = query_params.map { |k, v| "#{k}=#{v}" }.join('&')
    "#{base_url}?#{query_string}"
  end
end
