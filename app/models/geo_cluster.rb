# == Schema Information
#
# Table name: geo_clusters
#
#  id                                      :bigint           not null, primary key
#  agency_tenant_uuid                      :uuid
#  average_altitude                        :integer
#  bbox_max_latitude                       :float
#  bbox_max_longitude                      :float
#  bbox_min_latitude                       :float
#  bbox_min_longitude                      :float
#  benchmark_property_uuid                 :uuid
#  center_latitude                         :float
#  center_longitude                        :float
#  child_clusters_count                    :integer          default(0)
#  cluster_average_property_price_cents    :bigint           default(0), not null
#  cluster_average_property_price_currency :string           default("EUR"), not null
#  cluster_city                            :string
#  cluster_country                         :string
#  cluster_description                     :text
#  cluster_flags                           :integer          default(0), not null
#  cluster_geojsons                        :jsonb
#  cluster_group_name                      :string
#  cluster_name                            :string           not null
#  cluster_outcode                         :string
#  cluster_postcode_district               :string
#  cluster_region                          :string
#  cluster_slug                            :string           not null
#  default_zoom_level                      :string
#  discarded_at                            :datetime
#  epc_details_count                       :integer          default(0), not null
#  extra_cluster_details                   :jsonb
#  g_places                                :jsonb
#  is_new_developments_cluster             :boolean          default(FALSE)
#  is_postcode_cluster                     :boolean          default(FALSE)
#  median_known_floor_area_sqm             :integer          default(0), not null
#  median_price_per_sqm                    :integer          default(0), not null
#  national_park                           :string
#  number_of_postcode_areas                :integer          default(0)
#  parent_cluster_uuid                     :uuid
#  postcode_cluster_tags                   :string           default([]), is an Array
#  sold_transactions_count                 :integer          default(0), not null
#  total_population                        :integer          default(0)
#  translations                            :jsonb
#  travel_to_work_area                     :string
#  uuid                                    :uuid
#  created_at                              :datetime         not null
#  updated_at                              :datetime         not null
#
# Indexes
#
#  index_geo_clusters_on_agency_tenant_uuid                    (agency_tenant_uuid)
#  index_geo_clusters_on_benchmark_property_uuid               (benchmark_property_uuid)
#  index_geo_clusters_on_center_latitude_and_center_longitude  (center_latitude,center_longitude)
#  index_geo_clusters_on_cluster_flags                         (cluster_flags)
#  index_geo_clusters_on_cluster_name                          (cluster_name) UNIQUE
#  index_geo_clusters_on_cluster_slug                          (cluster_slug) UNIQUE
#  index_geo_clusters_on_discarded_at                          (discarded_at)
#  index_geo_clusters_on_parent_cluster_uuid                   (parent_cluster_uuid)
#  index_geo_clusters_on_uuid                                  (uuid)
#
class GeoCluster < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  store_attribute :extra_cluster_details, :neighborhood_cons, :json, default: {}
  store_attribute :extra_cluster_details, :neighborhood_pros, :json, default: {}
  store_attribute :extra_cluster_details, :outlier_transactions, :json, default: {}
  store_attribute :extra_cluster_details, :neighborhood_title, :json, default: {}
  store_attribute :extra_cluster_details, :neighborhood_description, :json, default: {}
  store_attribute :extra_cluster_details, :neighborhood_features, :json, default: {}
  store_attribute :extra_cluster_details, :neighborhood_amenities, :json, default: {}
  store_attribute :extra_cluster_details, :neighborhood_proximity, :json, default: {}
  store_attribute :extra_cluster_details, :places_that_add_value, :json, default: {}
  store_attribute :extra_cluster_details, :places_that_reduce_value, :json, default: {}
  store_attribute :extra_cluster_details, :similar_neighborhoods, :json, default: {}

  belongs_to :parent_cluster, class_name: 'GeoCluster', foreign_key: 'parent_cluster_uuid', primary_key: :uuid, optional: true
  belongs_to :benchmark_property, class_name: 'GenericProperty', foreign_key: 'benchmark_property_uuid', primary_key: :uuid, optional: true

  has_many :geo_cluster_places, foreign_key: 'geo_cluster_uuid', primary_key: :uuid
  has_many :relevant_places, through: :geo_cluster_places

  # 9 Jan 2025: currently an experiment
  has_many :processed_postcode_geo_clusters,
           -> { fully_processed_flg },
           class_name: 'PostcodeGeoCluster', foreign_key: 'geo_cluster_uuid', primary_key: :uuid
  # has_many :processed_postcode_geo_clusters, -> { where(fully_processed_flg: true) },
  #          class_name: 'PostcodeGeoCluster', foreign_key: 'geo_cluster_uuid', primary_key: :uuid
  # 4 apr 2025 : not sure what the idea behind this was but doesn't
  # look like it is currently used
  # has_many :processed_postcode_areas, through: :processed_postcode_geo_clusters,
  #                                     source: 'postcode_area', class_name: 'PostcodeArea'

  has_many :postcode_geo_clusters, foreign_key: 'geo_cluster_uuid', primary_key: :uuid
  has_many :postcode_areas, through: :postcode_geo_clusters
  has_many :sold_transactions, through: :postcode_areas
  has_many :sold_transaction_epcs, through: :sold_transactions
  # Feb 2025: I am not thrilled to be reaching through so many associations
  # to get sold transaction EPCs
  # Conceptually reaching through realty_assets Seems to make more sense...
  #   has_many :realty_assets, through: :postcode_areas
  # Would it make sense to have a geo_cluster_realty_asset association directly??

  # below suggested by deepseek to make above more efficient
  # Use includes to preload associations and avoid N+1 queries
  def sold_transaction_epcs_with_preload
    sold_transaction_epcs.includes(:sold_transactions, :postcode_areas, :postcode_geo_clusters)
  end

  has_many :synthetic_sold_transactions, through: :postcode_areas
  has_many :real_sold_transactions, through: :postcode_areas
  # # If you decide to use has_and_belongs_to_many for a join table approach:
  # has_and_belongs_to_many :postcode_areas, join_table: :postcode_geo_clusters,
  #                                          foreign_key: :geo_cluster_uuid,
  #                                          association_foreign_key: :postcode_area_uuid

  # has_many :child_clusters, class_name: 'GeoCluster', foreign_key: 'parent_cluster_uuid'

  has_many :geo_photos, lambda {
    order 'geo_photo_sort_order asc'
  }, class_name: 'GeoPhoto', primary_key: 'uuid',
     foreign_key: 'geo_cluster_uuid',
     dependent: :destroy

  # def postal_codes_list
  #   postcode_areas.pluck :postal_code
  # end

  def update_sold_transactions
    # useful to run after a land registry ppd import
    postcode_areas.each do |postcode_area|
      postcode_area.update_sold_transactions
    end
  end

  def create_geo_cluster_photo
    photo_data = {}
    GeoPhoto.create_geo_photo(
      photo_data:,
      center_latitude:,
      center_longitude:,
      geo_cluster_uuid: uuid
    )
  end

  monetize :cluster_average_property_price_cents # , with_model_currency: :currency

  def formatted_average_property_price
    cluster_average_property_price.format(no_cents: true)
  end

  def self.find_or_create_from_postcode_areas(postcode_areas)
    # Determine a unique attribute or combination for clustering, e.g., geographic bounds
    bbox = calculate_bbox(postcode_areas)
    if bbox.values.include?(0)
      puts "Skipping cluster creation for #{postcode_areas.map(&:postal_code).join(', ')} due to missing bounding box values"
      return
    end
    # Need to handle case where there are no valid values
    # Attempt to find an existing cluster matching these bounds
    # these are instances of GeoCluster
    cluster = find_or_initialize_by(
      bbox_min_latitude: bbox[:min_lat],
      bbox_max_latitude: bbox[:max_lat],
      bbox_min_longitude: bbox[:min_lon],
      bbox_max_longitude: bbox[:max_lon],
      is_postcode_cluster: true
      # To be added:
      #      t.string :cluster_group_name
      # t.string :cluster_outcode
      # t.string :cluster_postcode_district
    )

    if cluster.new_record?
      # New cluster, so we set up basic attributes
      cluster.cluster_name = generate_cluster_name(postcode_areas)
      cluster.cluster_slug = generate_cluster_slug(postcode_areas)

      cluster.center_latitude, cluster.center_longitude = calculate_center(postcode_areas)
      cluster.number_of_postcode_areas = postcode_areas.count
      # Add any other initial setup for a new cluster

      # Link the postcode areas to this new cluster
      # cluster.postcode_area_uuids = postcode_areas.map(&:uuid)
      cluster.save!
      cluster.update!(
        cluster_city: postcode_areas.map(&:city).compact.first,
        cluster_country: postcode_areas.map(&:country).compact.first,
        cluster_region: postcode_areas.map(&:region).compact.first,
        cluster_outcode: postcode_areas.map(&:outcode).compact.first
      )

      # t.string :cluster_outcode
      # t.string :cluster_postcode_district
      # t.string :cluster_city
      # t.string :cluster_country
      # t.string :cluster_region
      # elsif cluster.postcode_area_uuids.count != postcode_areas.map(&:uuid).count
      #   # Update existing cluster if necessary
      #   # Note: This updates only if the number of areas has changed or bounds have shifted
      #   cluster.update!(
      #     number_of_postcode_areas: postcode_areas.count,
      #     postcode_area_uuids: postcode_areas.map(&:uuid)
      #   )
      #   # Here you might want to recalculate center and other attributes if they've changed
    end

    postcode_areas.each do |postcode_area|
      # thes are instances of PostcodeGeoCluster
      postcode_area.postcode_geo_clusters.find_or_create_by!(
        geo_cluster_uuid: cluster.uuid,
        visible_in_cluster: true,
        is_primary_for_postcode: true
        # is_primary_for_postcode: true
      )
      # postcode_area.update!(primary_cluster_uuid: cluster.uuid)
    end
    cluster
  end

  def self.calculate_bbox(postcode_areas)
    if postcode_areas.map(&:bbox_min_latitude).include?(nil)
      # 4 jan 2025 - update bounding box for all postcode areas
      # if nil detected
      postcode_areas.each do |postcode_area|
        postcode_area.update_bounding_box_from_geojson
      end
    end
    lat_values = postcode_areas.map(&:bbox_min_latitude).compact
    lon_values = postcode_areas.map(&:bbox_min_longitude).compact

    min_lat = lat_values.empty? ? nil : lat_values.min
    max_lat = postcode_areas.map(&:bbox_max_latitude).compact.max
    min_lon = lon_values.empty? ? nil : lon_values.min
    max_lon = postcode_areas.map(&:bbox_max_longitude).compact.max
    # If any of these are nil, you might want to set a default or handle it differently
    { min_lat: min_lat || 0, max_lat: max_lat || 0, min_lon: min_lon || 0, max_lon: max_lon || 0 }
  end

  # def self.calculate_bbox(postcode_areas)
  #   min_lat = postcode_areas.map(&:bbox_min_latitude).min
  #   max_lat = postcode_areas.map(&:bbox_max_latitude).max
  #   min_lon = postcode_areas.map(&:bbox_min_longitude).min
  #   max_lon = postcode_areas.map(&:bbox_max_longitude).max

  #   { min_lat:, max_lat:, min_lon:, max_lon: }
  # end

  def self.generate_cluster_name(postcode_areas)
    # Example: Use the first couple of postcode areas to name the cluster
    "Cluster of #{postcode_areas.first(2).map(&:postal_code).join(' and ')}"
  end

  def self.generate_cluster_slug(postcode_areas)
    # Example: Use the postal codes to create a unique slug
    postcode_areas.map(&:postal_code).join('-').downcase.gsub(/[^a-z0-9-]/, '')
  end

  def self.calculate_center(postcode_areas)
    # Simple average for center, might need something more sophisticated for real-world use

    # Filter out nil values, then calculate the average
    lat_values = postcode_areas.map(&:center_latitude).compact
    lon_values = postcode_areas.map(&:center_longitude).compact

    if lat_values.empty? || lon_values.empty?
      # Handle case where there are no valid values
      return [0, 0] # or any other default value you prefer
    end

    lat = lat_values.sum / lat_values.length.to_f
    lon = lon_values.sum / lon_values.length.to_f
    [lat, lon]

    # lat = postcode_areas.map(&:center_latitude).sum / postcode_areas.count
    # lon = postcode_areas.map(&:center_longitude).sum / postcode_areas.count
    # [lat, lon]
  end

  # def merge_polygons(geojson_items)
  #   combined_geometries = []
  #   geojson_items.each do |geojson|
  #     combined_geometries << geojson['primary']['geometry']['coordinates']
  #   end
  #   merged_polygons = Geo::PolygonMerger.merge_polygons(combined_geometries)
  #   puts "Merged polygons: #{merged_polygons}"
  # end

  def combine_polygons
    areas_missing_geojson = postcode_areas.where(postcode_area_geojson: nil).pluck :id

    if areas_missing_geojson.any?
      puts "Skipping cluster #{id} due to missing GeoJSON for areas: #{areas_missing_geojson.join(', ')}"
      return
    end

    geojson_items = postcode_areas.pluck(:postcode_area_geojson)

    # # Check if we have at least two items to combine
    # if geojson_items.size < 2
    #   puts 'Not enough polygons to combine. Need at least 2.'
    #   return
    # end

    # Use Geos factory for precise geometric operations
    factory = RGeo::Geos.factory(srid: 4326)
    combined_geometries = []

    # Parse each GeoJSON item
    geojson_items.each do |geojson|
      if geojson.blank?
        puts 'Found blank GeoJSON - quitting'
        return
      end
      # Assuming 'primary' is the key for the GeoJSON structure
      feature = RGeo::GeoJSON.decode(geojson['primary'], json_parser: :json, geo_factory: factory)

      if feature.geometry.geometry_type.type_name == 'Polygon'
        combined_geometries << feature.geometry
      elsif feature.geometry.geometry_type.type_name == 'MultiPolygon'
        feature.geometry.each do |polygon|
          combined_geometries << polygon
        end
      else
        puts 'Unsupported geometry type: #{feature.geometry.geometry_type.type_name}'
        return
      end
    end

    # Combine all geometries into one MultiPolygon using union
    combined_geom = combined_geometries.shift
    combined_geometries.each do |geom|
      combined_geom = combined_geom.union(geom)
    end

    # Create new GeoJSON with combined geometry
    combined_geojson = {
      'type': 'Feature',
      'geometry': RGeo::GeoJSON.encode(combined_geom),
      'properties': {
        # Here we're just concatenating properties for simplicity. In practice, you might want to merge or handle these differently.
        # 'postcodes': geojson_items.map { |g| JSON.parse(g)['primary']['properties']['postcodes'] }.join(','),
        # 'mapit_code': geojson_items.map { |g| JSON.parse(g)['primary']['properties']['mapit_code'] }.join(',')
      }
    }
    update!(cluster_geojsons: combined_geojson)
    # puts JSON.pretty_generate(combined_geojson)
  end

  def point_in_cluster?(latitude, longitude)
    # GeoCluster.last.point_in_cluster?(GeoCluster.last.center_latitude, GeoCluster.last.center_longitude )
    # Create a factory for geographic coordinates
    factory = RGeo::Geographic.spherical_factory(srid: 4326)

    # Convert the GeoJSON geometry to an RGeo geometry object
    geometry = RGeo::GeoJSON.decode(
      cluster_geojsons,
      json_parser: :json, geo_factory: factory
    ).geometry

    # Create a point from the given latitude and longitude
    # Note: RGeo uses (longitude, latitude) for point creation
    point = factory.point(longitude, latitude)

    # Check if the point is within the MultiPolygon
    geometry.contains?(point)
  end

  # def combine_polygons_old
  #   # Fetch the GeoJSON data from GeoCluster
  #   geojson_items = postcode_areas.pluck(:postcode_area_geojson)

  #   # Check if we have at least two items to combine
  #   if geojson_items.size < 2
  #     puts 'Not enough polygons to combine. Need at least 2.'
  #     return
  #   end

  #   # Use Geos factory for precise geometric operations
  #   factory = RGeo::Geos.factory(srid: 4326)
  #   combined_geometries = []

  #   # Parse each GeoJSON item
  #   geojson_items.each do |geojson|
  #     # Assuming 'primary' is the key for the GeoJSON structure
  #     geometry = RGeo::GeoJSON.decode(geojson['primary'], json_parser: :json, geo_factory: factory)
  #     combined_geometries << geometry
  #   end

  #   # Combine all geometries into one MultiPolygon using union
  #   combined_geom = combined_geometries.shift
  #   combined_geometries.each do |geom|
  #     # seems this will raise an error if the geometries are not compatible
  #     # RGeo::Cartesian::MultiPolygonImpl#union not defined
  #     # https://www.rubydoc.info/gems/rgeo/RGeo/Feature/Geometry#union-instance_method
  #     combined_geom = combined_geom.union(geom)
  #   end

  #   # Create new GeoJSON with combined geometry
  #   combined_geojson = {
  #     'type': 'Feature',
  #     'geometry': RGeo::GeoJSON.encode(combined_geom),
  #     'properties': {
  #       # Here we're just concatenating properties for simplicity. In practice, you might want to merge or handle these differently.
  #       'postcodes': geojson_items.map { |g| g['primary']['properties']['postcodes'] }.join(','),
  #       'mapit_code': geojson_items.map { |g| g['primary']['properties']['mapit_code'] }.join(',')
  #     }
  #   }

  #   # Output or save the result
  #   puts JSON.pretty_generate(combined_geojson)
  # end
end
