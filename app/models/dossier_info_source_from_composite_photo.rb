# == Schema Information
#
# Table name: dossier_info_sources
#
#  id                                         :bigint           not null, primary key
#  agency_tenant_uuid                         :uuid
#  associated_listing_uuid                    :uuid
#  discarded_at                               :datetime
#  dossier_asset_uuid                         :uuid
#  dossier_info_source_aasm_state             :string
#  dossier_info_source_details                :jsonb
#  dossier_info_source_flags                  :integer          default(0), not null
#  dossier_info_source_rating                 :integer          default(0), not null
#  is_for_most_comparable_property_to_primary :boolean          default(FALSE)
#  is_source_for_primary_property             :boolean          default(FALSE)
#  llm_interaction_uuid                       :uuid
#  property_is_for_rent                       :boolean          default(FALSE)
#  property_is_for_sale                       :boolean          default(TRUE)
#  realty_asset_photo_uuid                    :uuid
#  realty_dossier_uuid                        :uuid
#  realty_search_query_uuid                   :uuid
#  source_is_epc                              :boolean          default(FALSE)
#  source_is_listing                          :boolean          default(TRUE)
#  source_is_photo                            :boolean          default(FALSE)
#  source_is_sold_transaction                 :boolean          default(FALSE)
#  uuid                                       :uuid
#  created_at                                 :datetime         not null
#  updated_at                                 :datetime         not null
#
# Indexes
#
#  index_dossier_info_sources_on_agency_tenant_uuid          (agency_tenant_uuid)
#  index_dossier_info_sources_on_associated_listing_uuid     (associated_listing_uuid)
#  index_dossier_info_sources_on_discarded_at                (discarded_at)
#  index_dossier_info_sources_on_dossier_asset_uuid          (dossier_asset_uuid)
#  index_dossier_info_sources_on_dossier_info_source_flags   (dossier_info_source_flags)
#  index_dossier_info_sources_on_dossier_info_source_rating  (dossier_info_source_rating)
#  index_dossier_info_sources_on_realty_asset_photo_uuid     (realty_asset_photo_uuid)
#  index_dossier_info_sources_on_realty_dossier_uuid         (realty_dossier_uuid)
#  index_dossier_info_sources_on_uuid                        (uuid)
#
class DossierInfoSourceFromCompositePhoto < DossierInfoSource
  # default_scope { where(source_is_photo: true) }
  default_scope { info_source_is_composite_photo.where(source_is_photo: true) }

  # Define stored attributes in dossier_info_source_details
  store_attribute :dossier_info_source_details, :description_medium, :string, default: ''
  store_attribute :dossier_info_source_details, :main_section_or_room_details, :json, default: []
  store_attribute :dossier_info_source_details, :other_section_or_room_details, :json, default: []
  store_attribute :dossier_info_source_details, :sub_images, :json, default: []
  store_attribute :dossier_info_source_details, :unique_rooms_or_sections, :json, default: []
  # NOTE: room_photo_mapping is no longer used but kept for reference; can be removed if not needed for backward compatibility
  # store_attribute :dossier_info_source_details, :room_photo_mapping, :json, default: {}

  belongs_to :realty_dossier, foreign_key: :realty_dossier_uuid, primary_key: :uuid, optional: true
  belongs_to :dossier_asset, foreign_key: :dossier_asset_uuid, primary_key: :uuid, optional: true

  # Return a summary of the analyzed photo data
  # 12 apr - doubt below is in use
  def analysed_photo_summary
    {
      sub_images: sub_images,
      unique_rooms_or_sections: unique_rooms_or_sections,
      main_section_or_room_details: main_section_or_room_details,
      other_section_or_room_details: other_section_or_room_details
    }
  end

  # Main method to create or update dossier info source from AI interaction
  # Called from CompositePhotoAnalyser
  def self.find_or_create_from_ai_interaction(
    llm_interaction, dossier_item, target_dossier_asset
  )
    return unless valid_llm_interaction?(llm_interaction)

    chosen_response_json = JSON.parse(llm_interaction.chosen_response)
    # sanity check here in case the llm respond does not correspond to the target_dossier_asset
    raise 'response might be for wrong dossier' if dossier_item.id.to_s != chosen_response_json['dossier_item_id']
    raise 'response might be for wrong asset' if target_dossier_asset.id.to_s != chosen_response_json['target_dossier_asset_id']

    sub_images_ids = chosen_response_json['sub_images'].pluck 'image_id'
    only_valid_images_included = sub_images_ids.all? { |element_a| target_dossier_asset.asset_photo_short_ids.include?(element_a.to_i) }

    raise 'response has invalid images' unless only_valid_images_included

    photo_info_source = create_or_update_info_source(llm_interaction, dossier_item, target_dossier_asset)

    target_dossier_asset.update!(
      ai_gen_da_title: chosen_response_json['ai_gen_title'],
      ai_gen_da_description: chosen_response_json['ai_gen_description']
    )
    if target_dossier_asset.default_sale_listing.present?
      target_dossier_asset.default_sale_listing.update!(
        ai_gen_title: chosen_response_json['ai_gen_title'],
        ai_gen_description: chosen_response_json['ai_gen_description']
      )
    end

    process_composite_photo_data(photo_info_source, chosen_response_json, target_dossier_asset)
    photo_info_source
  end

  # Validates if LLM interaction has a chosen response
  def self.valid_llm_interaction?(llm_interaction)
    llm_interaction.chosen_response.present?
  end

  # # Parses JSON string into a Ruby hash
  # def self.parse_chosen_response(chosen_response)
  #   JSON.parse(chosen_response)
  # end

  # Creates or finds the dossier info source record
  def self.create_or_update_info_source(llm_interaction, dossier_item, target_dossier_asset)
    info_source = find_or_create_by(
      llm_interaction_uuid: llm_interaction.uuid,
      dossier_asset_uuid: target_dossier_asset.uuid,
      realty_dossier_uuid: dossier_item.uuid,
      agency_tenant_uuid: llm_interaction.agency_tenant_uuid,
      source_is_photo: true
    )
    # Manual update required as of 3 Apr 2025
    info_source.update!(info_source_is_composite_photo: true)
    info_source
  end

  # Processes all photo-related data from the composite analysis
  def self.process_composite_photo_data(photo_info_source, response_json, target_dossier_asset)
    sub_images = response_json['sub_images'] || []
    unique_rooms = response_json['unique_rooms_or_sections'] || []

    section_details = process_sub_images(sub_images)
    process_unique_rooms(unique_rooms, target_dossier_asset)
    update_photo_info_source(photo_info_source, sub_images, unique_rooms, section_details)
    suggested_photo_order = response_json['suggested_photo_order'] || []
    # order the photos
    suggested_photo_order.each_with_index do |photo_id, index|
      photo = RealtyAssetPhoto.find_by(id: photo_id)
      next unless photo

      photo.update!(sort_order: index)
    end
  end

  # Processes sub-images and returns section details
  def self.process_sub_images(sub_images)
    main_details = []
    other_details = []

    sub_images.each do |sub_image|
      update_realty_asset_photo(sub_image)
      collect_section_details(sub_image, main_details, other_details)
    end

    { main: main_details, other: other_details }
  end

  # Updates RealtyAssetPhoto record if needed
  def self.update_realty_asset_photo(sub_image)
    photo = RealtyAssetPhoto.find_by(id: sub_image['image_id'])
    return unless photo && photo.photo_title.blank?

    analysis = sub_image['general_photo_analysis']
    photo.update!(
      flag_is_parsed_by_llm: true,
      photo_title: analysis['photo_catchy_title'],
      photo_description: analysis['photo_general_description'],
      photo_ai_desc: analysis['photo_general_description'],
      raw_ai_analysis: sub_image
    )
  end

  # Collects main and other section details from sub-image
  def self.collect_section_details(sub_image, main_details, other_details)
    # 18 may 2025 - added this when llm made a typo and used sections_or_room_in_photo instead of sections_or_rooms_in_photo
    sections_or_rooms_in_photo = sub_image['sections_or_rooms_in_photo'] || sub_image['sections_or_room_in_photo'] || []
    if sections_or_rooms_in_photo.empty?
      puts "No sections or rooms found in photo with ID: #{sub_image['image_id']}"
      # Handle case where no sections/rooms are found
    else
      sections_or_rooms_in_photo.each do |section|
        section_data = section.merge('image_id' => sub_image['image_id'])
        if section['is_main_section_or_room']
          main_details << section_data
        else
          other_details << section_data
        end
      end
    end

    # sub_image['sections_or_rooms_in_photo'].each do |section|
    #   section_data = section.merge('image_id' => sub_image['image_id'])
    #   if section['is_main_section_or_room']
    #     main_details << section_data
    #   else
    #     other_details << section_data
    #   end
    # end
  end

  # Processes unique rooms/sections and updates DossierAssetParts
  def self.process_unique_rooms(unique_rooms, target_dossier_asset)
    unique_rooms.each do |room|
      asset_part = update_dossier_asset_part(room, target_dossier_asset)
      update_asset_part_photos(asset_part, room, target_dossier_asset)
    end
  end

  # Creates or updates a DossierAssetPart record
  def self.update_dossier_asset_part(room, target_dossier_asset)
    slug = room['section_or_room_identifier'].parameterize
    asset_part = target_dossier_asset.dossier_asset_parts.find_or_create_by(asset_part_slug: slug)
    asset_part.undiscard! if asset_part.discarded?
    asset_part
  end

  # Updates photos and details for a DossierAssetPart
  def self.update_asset_part_photos(asset_part, room, target_dossier_asset)
    photo_ids = room['image_ids'] || []
    photos = RealtyAssetPhoto.where(id: photo_ids)
    puts "asset_photo_short_ids: #{target_dossier_asset.asset_photo_short_ids}"
    # all_included = ['839'].all? { |element_a| target_dossier_asset.asset_photo_short_ids.include?(element_a.to_i) }
    all_included = photo_ids.all? { |element_a| target_dossier_asset.asset_photo_short_ids.include?(element_a.to_i) }

    raise 'something fishy with update_asset_part_photos' unless all_included

    asset_part.update!(
      realty_asset_photo_uuids: photos.pluck(:uuid),
      asset_part_title: room['descriptive_title'] || room['section_or_room_identifier'].titleize,
      asset_part_type: room['section_or_room_type'],
      # Optional: Add more fields if DossierAssetPart supports them
      asset_part_description: room['summary_description'],
      asset_part_main_color: room['dominant_color'],
      raw_llm_json: room
    )
  end

  # Updates the photo info source with final analysis data
  def self.update_photo_info_source(photo_info_source, sub_images, unique_rooms, section_details)
    photo_info_source.update!(
      sub_images: sub_images,
      unique_rooms_or_sections: unique_rooms,
      main_section_or_room_details: section_details[:main],
      other_section_or_room_details: section_details[:other],
      description_medium: "Composite image analysis containing #{sub_images.length} sub-images."
    )
  end

  # # Create or update the dossier info source from an AI interaction
  # def self.find_or_create_from_ai_interaction(llm_interaction, dossier_item, target_dossier_asset)
  #   # primary_dossier_asset_uuid = dossier_item.primary_dossier_asset&.uuid
  #   return unless llm_interaction.chosen_response.present?

  #   chosen_response_json = JSON.parse(llm_interaction.chosen_response)

  #   photo_info_source = DossierInfoSourceFromCompositePhoto.find_or_create_by(
  #     llm_interaction_uuid: llm_interaction.uuid,
  #     dossier_asset_uuid: target_dossier_asset.uuid, # primary_dossier_asset_uuid,
  #     realty_dossier_uuid: dossier_item.uuid,
  #     agency_tenant_uuid: llm_interaction.agency_tenant_uuid,
  #     source_is_photo: true
  #   )
  #   # 3 apr 2025 - I thought below would get set automatically (using DossierInfoSourceFromCompositePhoto)
  #   # but it turns out I need to do it manually
  #   photo_info_source.update!(info_source_is_composite_photo: true)

  #   # Extract sub_images and unique_rooms_or_sections from the response
  #   sub_images = chosen_response_json['sub_images'] || []
  #   unique_rooms_or_sections = chosen_response_json['unique_rooms_or_sections'] || []

  #   # Aggregate main and other sections across all sub-images
  #   main_section_or_room_details = []
  #   other_section_or_room_details = []

  #   # Process each sub-image to update RealtyAssetPhoto and collect section details
  #   sub_images.each do |sub_image|
  #     realty_asset_photo = RealtyAssetPhoto.find_by(id: sub_image['image_id'])
  #     if realty_asset_photo && realty_asset_photo.photo_title.blank?
  #       realty_asset_photo.update!(
  #         flag_is_parsed_by_llm: true,
  #         photo_title: sub_image['general_photo_analysis']['photo_catchy_title'],
  #         photo_description: sub_image['general_photo_analysis']['photo_general_description'],
  #         photo_ai_desc: sub_image['general_photo_analysis']['photo_general_description'],
  #         raw_ai_analysis: sub_image
  #       )
  #     end

  #     sub_image['sections_or_rooms_in_photo'].each do |section|
  #       if section['is_main_section_or_room']
  #         main_section_or_room_details << section.merge('image_id' => sub_image['image_id'])
  #       else
  #         other_section_or_room_details << section.merge('image_id' => sub_image['image_id'])
  #       end
  #     end
  #   end

  #   # dossier_item.primary_dossier_asset.dossier_asset_parts.discard_all
  #   # Process each unique room or section to create/update DossierAssetPart records
  #   unique_rooms_or_sections.each do |room|
  #     # Use section_or_room_identifier to ensure uniqueness (e.g., "kitchen 1")
  #     asset_part_slug = room['section_or_room_identifier'].parameterize
  #     # 7 apr 2025: - seemed wrong to me that the asset_parts are
  #     # only being created for the primary_dossier_asset
  #     # but I think that is because I am only doing composit photo
  #     # analysis once a dossier is created for an asset...
  #     # relevant_dossier_asset_part = dossier_item.primary_dossier_asset.dossier_asset_parts.find_or_create_by(
  #     #   asset_part_slug: asset_part_slug
  #     # )

  #     #  9 apr -  decided against above - don't need a dossier to run composite photo
  #     relevant_dossier_asset_part = target_dossier_asset.dossier_asset_parts.find_or_create_by(
  #       asset_part_slug: asset_part_slug
  #     )

  #     relevant_dossier_asset_part.undiscard! if relevant_dossier_asset_part.discarded?

  #     realty_asset_photo_ids = room['image_ids'] || []
  #     relevant_photos = RealtyAssetPhoto.where(id: realty_asset_photo_ids)
  #     realty_asset_photo_uuids = relevant_photos.pluck(:uuid)

  #     # Update the dossier asset part with photo UUIDs, title, and type
  #     relevant_dossier_asset_part.update!(
  #       realty_asset_photo_uuids: realty_asset_photo_uuids,
  #       asset_part_title: room['descriptive_title'] || room['section_or_room_identifier'].titleize,
  #       asset_part_type: room['section_or_room_type']
  #       # Optional: Add more fields if DossierAssetPart supports them
  #       # asset_part_description: room['summary_description'],
  #       # asset_part_dominant_color: room['dominant_color'],
  #       # raw_llm_json: room
  #     )
  #   end

  #   # Update the photo info source with the new structure
  #   photo_info_source.update!(
  #     sub_images: sub_images,
  #     unique_rooms_or_sections: unique_rooms_or_sections,
  #     main_section_or_room_details: main_section_or_room_details,
  #     other_section_or_room_details: other_section_or_room_details,
  #     description_medium: "Composite image analysis containing #{sub_images.length} sub-images."
  #   )

  #   photo_info_source
  # end
end
