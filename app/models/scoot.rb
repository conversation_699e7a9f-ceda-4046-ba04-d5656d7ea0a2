# == Schema Information
#
# Table name: scoots
#
#  id                                   :bigint           not null, primary key
#  aasm_state                           :string
#  access_code_details                  :jsonb
#  access_token                         :string           default("")
#  agency_tenant_uuid                   :uuid
#  bathrooms_max_absolute               :integer          default(0)
#  bathrooms_max_preferred              :integer          default(0)
#  bathrooms_min_absolute               :integer          default(0)
#  bathrooms_min_preferred              :integer          default(0)
#  bedrooms_max_absolute                :integer          default(0)
#  bedrooms_max_preferred               :integer          default(0)
#  bedrooms_min_absolute                :integer          default(0)
#  bedrooms_min_preferred               :integer          default(0)
#  cloned_from_uuid                     :string
#  country_code                         :string           default("UK")
#  discarded_at                         :datetime
#  dossier_assets_count                 :integer          default(0), not null
#  dossier_ids                          :string
#  dossiers_count                       :integer          default(0), not null
#  feature_preferences                  :jsonb
#  flags                                :integer          default(0), not null
#  guest_uuid                           :uuid
#  indoor_area_max_absolute             :integer          default(0)
#  indoor_area_min_absolute             :integer          default(0)
#  integer                              :integer          default(0), not null
#  is_approved_for_public               :boolean          default(FALSE)
#  is_closed                            :boolean          default(FALSE)
#  is_public_scoot                      :boolean          default(FALSE)
#  is_showcase_scoot                    :boolean          default(FALSE)
#  landing_page_game_ids                :string           default([]), is an Array
#  last_accessed_at                     :datetime
#  lat_lng_bounds                       :jsonb
#  latitude_center                      :float
#  longitude_center                     :float
#  max_distance_from_vicinity_absolute  :integer
#  max_distance_from_vicinity_preferred :integer
#  plot_area_max_absolute               :integer          default(0)
#  plot_area_min_absolute               :integer          default(0)
#  preferred_area_unit                  :integer          default(0)
#  preferred_currency                   :string           default("GBP")
#  preferred_distance_unit              :integer          default(0)
#  preferred_locale                     :string           default("en-UK")
#  preferred_property_type              :string
#  preferred_vicinity_name              :string
#  price_max_absolute_cents             :bigint           default(50000000), not null
#  price_max_preferred_cents            :bigint           default(40000000), not null
#  price_min_absolute_cents             :bigint           default(10000000), not null
#  price_min_preferred_cents            :bigint           default(10000000), not null
#  price_plan                           :integer          default(0)
#  scoot_access_count                   :bigint           default(0)
#  scoot_access_flags                   :integer          default(0), not null
#  scoot_checklists                     :jsonb
#  scoot_description                    :string           default("")
#  scoot_host_domain                    :string
#  scoot_image_url                      :string           default("")
#  scoot_notes                          :jsonb
#  scoot_places_of_interest             :jsonb
#  scoot_related_urls                   :jsonb
#  scoot_share_links                    :jsonb
#  scoot_significant_dates              :jsonb
#  scoot_subdomain                      :string
#  scoot_title                          :string           default("")
#  scoot_type                           :integer          default(0)
#  single_guess_game_ids                :jsonb            is an Array
#  supported_currencies                 :text             default([]), is an Array
#  translations                         :jsonb
#  user_uuid                            :uuid
#  uuid                                 :uuid
#  valid_game_ids                       :text             default([]), is an Array
#  viewport                             :jsonb
#  created_at                           :datetime         not null
#  updated_at                           :datetime         not null
#
# Indexes
#
#  index_scoots_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_scoots_on_discarded_at        (discarded_at)
#  index_scoots_on_flags               (flags)
#  index_scoots_on_guest_uuid          (guest_uuid)
#  index_scoots_on_integer             (integer)
#  index_scoots_on_scoot_access_flags  (scoot_access_flags)
#  index_scoots_on_scoot_type          (scoot_type)
#  index_scoots_on_user_uuid           (user_uuid)
#  index_scoots_on_uuid                (uuid)
#
class Scoot < ApplicationRecord
  # Though scoot was a replacement for quest I can find some inspirational columns from here.
  # https://be-05.homestocompare.com/api_fe1_h2c/v1/house_hunter_personas/show_persona/bold-ben
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  has_many :price_estimates, primary_key: 'uuid', foreign_key: 'scoot_uuid'
  has_many :non_ai_price_estimates, -> { where(is_ai_estimate: false) },
           class_name: 'PriceEstimate', primary_key: 'uuid', foreign_key: 'scoot_uuid'
  has_many :ai_price_estimates, -> { where(is_ai_estimate: true) },
           class_name: 'PriceEstimate', primary_key: 'uuid', foreign_key: 'scoot_uuid'

  has_many :realty_games, primary_key: 'uuid', foreign_key: 'scoot_uuid'
  has_many :realty_game_listings, primary_key: 'uuid', foreign_key: 'scoot_uuid'
  has_many :sale_listings, through: :realty_game_listings, source: :sale_listing

  # serialize :realty_game_ids, Array
  attribute :realty_game_ids, :json, default: []

  store_attribute :scoot_checklists, :realty_game_ids, :json, default: []
  # I came up with realty_games after the idea for scoot which is why I have this messy solution.
  # Mainly for housepriceguess.com
  def all_realty_games
    ids = case realty_game_ids
          when Array
            realty_game_ids
          when String
            begin
              parsed = JSON.parse(realty_game_ids)
              parsed.is_a?(Array) ? parsed : [parsed]
            rescue JSON::ParserError
              []
            end
          else
            []
          end
    # above palavar needed as editing realty_game_ids via
    # administrate can result in string being saved
    RealtyGame.where(id: ids)
  end

  include FlagShihTzu
  has_flags 1 => :is_price_guess_enabled,
            2 => :is_price_guess_public,
            3 => :is_price_guess_only,
            4 => :should_show_out_links,
            5 => :supports_multiple_games,
            :column => 'flags'

  def available_games_details
    available_games = realty_games.kept
    # another chapuzo: use all_realty_games for hpg-scoot
    available_games = all_realty_games if scoot_subdomain == 'hpg-scoot'
    available_games.as_json(
      only: %w[
        uuid
        available_game_listings_count
        realty_game_slug guessed_prices_count game_sessions_count
        game_jots_count game_listings_count game_start_at game_end_at
        game_bg_image_url
        game_starting_url game_title game_description
        game_default_currency game_default_country game_default_locale
      ],
      methods: %w[is_hidden_from_landing_page]
    )
  end

  def scoot_notice
    if is_demo?
      "This is a POC demo. You can access it with the code 'demo'" \
    else
      'Please enter your access code to view the dossier.'
      # ScootNotice.find_by(scoot_subdomain: 'default')
    end
  end

  def is_demo?
    if %w[dee david ed demo st-neots].include?(scoot_subdomain)
      true
    else
      false
    end
  end

  def scoot_dossiers
    if dossier_ids.present?
      # ids = dossier_ids.is_a?(String) ? dossier_ids.split(',').map(&:to_i) : Array(dossier_ids)
      RealtyDossier.where(id: safe_dossier_ids)
    else
      []
    end
  end

  def has_access_to_dossier?(dossier)
    safe_dossier_ids.include?(dossier.id)
  end

  def has_access_to_tasks?(htoc_access_code)
    # TODO: - improve logic here including
    # allowing public access in some cases
    access_token == htoc_access_code
  end

  def safe_dossier_ids
    ids = dossier_ids.is_a?(String) ? dossier_ids.split(',').map(&:to_i) : Array(dossier_ids)
    ids || []
  end

  def listings_for_price_guess
    # lfpg = []
    default_realty_dossier = scoot_dossiers.first
    default_realty_dossier&.price_guess_listings_from_dossier
    # default_realty_dossier&.dossier_assets&.presence&.each do |dossier_asset|
    #   lfpg.push dossier_asset.default_sale_listing
    # end
    # lfpg
  end

  after_create :set_scoot_defaults

  private

  def set_scoot_defaults
    # workaround from 3 June 2025 as I test price guess concept
    update(
      is_price_guess_enabled: true,
      is_price_guess_public: true,
      is_price_guess_only: true
    )
  end
end
