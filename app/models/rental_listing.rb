# == Schema Information
#
# Table name: rental_listings
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  agency_uuid                                   :uuid
#  archived                                      :boolean          default(FALSE)
#  available_from                                :datetime
#  available_till                                :datetime
#  capacity                                      :integer
#  cloned_from_uuid                              :string
#  commission_cents                              :bigint           default(0), not null
#  commission_currency                           :string           default("EUR"), not null
#  currency                                      :string
#  description_bullet_points                     :jsonb
#  design_style                                  :string
#  details_of_rooms                              :jsonb
#  discarded_at                                  :datetime
#  extra_rental_details                          :jsonb
#  for_rent_daily                                :boolean          default(FALSE)
#  for_rent_long_term                            :boolean          default(FALSE)
#  for_rent_monthly                              :boolean          default(FALSE)
#  furnished                                     :boolean          default(FALSE)
#  hide_map                                      :boolean          default(FALSE)
#  highlighted                                   :boolean          default(FALSE)
#  host_on_create                                :string           default("unknown_host"), not null
#  import_url                                    :string
#  is_ai_generated_listing                       :boolean          default(FALSE)
#  listing_pages_count                           :integer          default(0), not null
#  listing_slug                                  :string
#  listing_tags                                  :string           default([]), is an Array
#  llm_interaction_uuid                          :uuid
#  llm_interactions_count                        :integer          default(0)
#  main_video_url                                :string
#  obscure_map                                   :boolean          default(FALSE)
#  occupants                                     :integer
#  page_section_listings_count                   :integer          default(0), not null
#  position_in_list                              :integer
#  price_rental_daily_for_search_cents           :bigint           default(0), not null
#  price_rental_daily_for_search_currency        :string           default("EUR"), not null
#  price_rental_daily_high_season_cents          :bigint           default(0), not null
#  price_rental_daily_high_season_currency       :string           default("EUR"), not null
#  price_rental_daily_low_season_cents           :bigint           default(0), not null
#  price_rental_daily_low_season_currency        :string           default("EUR"), not null
#  price_rental_daily_standard_season_cents      :bigint           default(0), not null
#  price_rental_daily_standard_season_currency   :string           default("EUR"), not null
#  price_rental_monthly_for_search_cents         :bigint           default(0), not null
#  price_rental_monthly_for_search_currency      :string           default("EUR"), not null
#  price_rental_monthly_high_season_cents        :bigint           default(0), not null
#  price_rental_monthly_high_season_currency     :string           default("EUR"), not null
#  price_rental_monthly_low_season_cents         :bigint           default(0), not null
#  price_rental_monthly_low_season_currency      :string           default("EUR"), not null
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  property_board_items_count                    :integer          default(0), not null
#  publish_from                                  :datetime
#  publish_till                                  :datetime
#  realty_asset_uuid                             :uuid
#  reference                                     :string
#  related_urls                                  :jsonb
#  rental_listing_features                       :jsonb
#  rental_listing_flags                          :integer          default(0), not null
#  rental_listing_gen_prompt                     :text
#  reserved                                      :boolean          default(FALSE)
#  rl_uprn                                       :string
#  schedule                                      :text
#  service_charge_yearly_cents                   :bigint           default(0), not null
#  service_charge_yearly_currency                :string           default("EUR"), not null
#  site_visitor_token                            :string
#  sl_photos_count                               :integer          default(0), not null
#  translations                                  :jsonb
#  unique_url                                    :string           default("")
#  units                                         :integer
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  versions_count                                :integer          default(0), not null
#  visible                                       :boolean          default(FALSE)
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#  psq_visit_id                                  :bigint
#  realty_asset_id                               :integer
#
# Indexes
#
#  index_rental_listings_on_agency_tenant_uuid                     (agency_tenant_uuid)
#  index_rental_listings_on_discarded_at                           (discarded_at)
#  index_rental_listings_on_for_rent_daily                         (for_rent_daily)
#  index_rental_listings_on_for_rent_long_term                     (for_rent_long_term)
#  index_rental_listings_on_for_rent_monthly                       (for_rent_monthly)
#  index_rental_listings_on_highlighted                            (highlighted)
#  index_rental_listings_on_is_ai_generated_listing                (is_ai_generated_listing)
#  index_rental_listings_on_listing_slug                           (listing_slug)
#  index_rental_listings_on_occupants                              (occupants)
#  index_rental_listings_on_price_rental_daily_for_search_cents    (price_rental_daily_for_search_cents)
#  index_rental_listings_on_price_rental_monthly_for_search_cents  (price_rental_monthly_for_search_cents)
#  index_rental_listings_on_realty_asset_uuid                      (realty_asset_uuid)
#  index_rental_listings_on_reference                              (reference)
#  index_rental_listings_on_rental_listing_flags                   (rental_listing_flags)
#  index_rental_listings_on_units                                  (units)
#  index_rental_listings_on_uuid                                   (uuid)
#  index_rental_listings_on_visible                                (visible)
#
class RentalListing < ApplicationRecord
end
