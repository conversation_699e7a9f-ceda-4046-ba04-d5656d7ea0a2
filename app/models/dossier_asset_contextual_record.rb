# == Schema Information
#
# Table name: dossier_asset_contextual_records
#
#  id                     :bigint           not null, primary key
#  agency_tenant_uuid     :uuid
#  contextual_record_uuid :uuid
#  discarded_at           :datetime
#  dossier_asset_uuid     :uuid
#  uuid                   :uuid
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_dossier_asset_contextual_records_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_dossier_asset_contextual_records_on_discarded_at        (discarded_at)
#  index_dossier_asset_contextual_records_on_uuid                (uuid)
#
class DossierAssetContextualRecord < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  include Discard::Model

  # store_attribute :asset_part_details, :realty_asset_photo_uuids, :json, default: []
  # store_attribute :asset_part_details, :raw_llm_json, :json, default: {}

  belongs_to :dossier_asset, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid', optional: true
  belongs_to :contextual_record, primary_key: 'uuid', foreign_key: 'contextual_record_uuid', optional: true
  belongs_to :neighbourhood_record, primary_key: 'uuid', foreign_key: 'contextual_record_uuid',
                                    class_name: 'ContextualRecordFromGetthedata', optional: true
  has_one :realty_asset, through: :dossier_asset # ,
  has_one :realty_dossier, through: :dossier_asset # ,
end
