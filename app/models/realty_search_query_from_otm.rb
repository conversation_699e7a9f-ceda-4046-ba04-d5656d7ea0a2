# == Schema Information
#
# Table name: realty_search_queries
#
#  id                        :bigint           not null, primary key
#  aasm_state                :string
#  agency_tenant_uuid        :uuid             not null
#  agency_uuid               :uuid
#  average_results_count     :integer
#  default_sort_criteria     :integer          default(0), not null
#  discarded_at              :datetime
#  indoor_area_max           :integer          default(0)
#  indoor_area_min           :integer          default(0)
#  latest_scrape_item_uuid   :uuid
#  locale                    :string
#  parent_query_uuid         :uuid
#  plot_area_max             :integer          default(0)
#  plot_area_min             :integer          default(0)
#  property_type             :integer          default(0), not null
#  property_type_string      :string
#  query_rating              :integer          default(50), not null
#  query_source_portal       :integer          default("unknown"), not null
#  query_traits              :string
#  realty_search_details     :jsonb
#  realty_search_flags       :integer          default(0), not null
#  search_area_unit          :integer          default(0)
#  search_bathrooms_max      :integer          default(0), not null
#  search_bathrooms_min      :integer          default(0), not null
#  search_bedrooms_max       :integer          default(0), not null
#  search_bedrooms_min       :integer          default(0), not null
#  search_cities             :text             default([]), is an Array
#  search_city               :string
#  search_currency           :string           default("GBP")
#  search_lat_lng_bounds     :jsonb
#  search_latitude_center    :float
#  search_longitude_center   :float
#  search_postcode           :string
#  search_price_max          :bigint           default(0), not null
#  search_price_min          :bigint           default(0), not null
#  search_query_slug         :string
#  search_query_url          :string
#  search_query_url_template :string
#  search_source             :integer          default(0), not null
#  summary_listings_count    :integer
#  transaction_type          :integer          default(0), not null
#  translations              :jsonb
#  uuid                      :uuid
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#
# Indexes
#
#  index_realty_search_queries_on_agency_uuid            (agency_uuid)
#  index_realty_search_queries_on_default_sort_criteria  (default_sort_criteria)
#  index_realty_search_queries_on_discarded_at           (discarded_at)
#  index_realty_search_queries_on_property_type          (property_type)
#  index_realty_search_queries_on_query_rating           (query_rating)
#  index_realty_search_queries_on_realty_search_flags    (realty_search_flags)
#  index_realty_search_queries_on_search_source          (search_source)
#  index_realty_search_queries_on_transaction_type       (transaction_type)
#  index_realty_search_queries_on_uuid                   (uuid)
#
class RealtySearchQueryFromOtm < RealtySearchQuery
  # default_scope { query_is_onthemarket }
  default_scope { where(query_source_portal: 'onthemarket') }
  # OTM_BASE_URL = 'https://www.onthemarket.com/async/search/properties/'.freeze

  def search_and_save_listings!
    retrieval_end_point = construct_otm_search_url
    scrape_item_from_otm_search = ScrapeItemFromOtmSearch.find_or_create_for_otm_search(
      retrieval_end_point
    )

    scrape_item_from_otm_search.update!(
      {
        realty_search_query_uuid: uuid
      }
    )
    update!(
      search_query_url: retrieval_end_point
      # scrape_item_uuid: scrape_item_from_otm_search.uuid
    )
    # scraper_connector_name = 'ScraperConnectors::LocalPlaywright'
    scraper_connector_name = 'ScraperConnectors::Json'
    scrape_result = scrape_item_from_otm_search.retrieve_and_set_content_object(
      scraper_connector_name, include_trailing_slash: true,
                              force_retrieval: true # , is_search_scrape: true
    )

    # `write': "\xC2" from ASCII-8BIT to UTF-8 (Encoding::UndefinedConversionError)
    # Sometimes just printing the scrape_result can cause the error above
    # puts scrape_result

    puts 'No properties found' if scrape_item_from_otm_search.raw_contents_as_json['properties'].blank?

    summary_sale_listings = scrape_item_from_otm_search.summary_sale_listings_from_scrape_item
    puts "Created #{summary_sale_listings.count} summary_sale_listings"
    # each summary_sale_listing will be associated with the realty search query
    # As can be seen by
    # summary_sale_listings.pluck :realty_search_query_uuid
    summary_sale_listings
  end

  def location_id_for_otm
    return search_postcode.gsub(' ', '-').downcase if search_postcode.present?

    'cv11'
  end

  def construct_otm_search_url
    base_url = 'https://www.onthemarket.com/async/search/properties/'

    params = {
      'search-type' => 'for-sale',
      'location-id' => location_id_for_otm
    }

    params['radius'] = 1
    params['min-price'] = search_price_min
    params['max-price'] = search_price_max
    params['min-bedrooms'] = search_bedrooms_min
    params['min-bedrooms'] = search_bedrooms_max
    # params['prop-types'] = inc_property_type if inc_property_type

    uri = URI(base_url)
    uri.query = URI.encode_www_form(params.compact)

    uri.to_s
  end

  after_create :set_portal

  private

  def set_portal
    self.query_source_portal = 'onthemarket'
    # self.query_is_onthemarket = true
    save!
  end

  # def parse_listings(_html)
  #   # Implement parsing logic here (e.g., using Nokogiri)
  #   []
  # end

  # def save_listings(listings)
  #   listings.each do |listing|
  #     SaleListing.create!(listing)
  #   end
  # end
end
