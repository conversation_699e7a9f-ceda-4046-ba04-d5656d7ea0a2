# == Schema Information
#
# Table name: generic_property_photos
#
#  id                            :bigint           not null, primary key
#  agency_tenant_uuid            :uuid
#  cloned_from_uuid              :string
#  content_type                  :string
#  details                       :jsonb
#  discarded_at                  :datetime
#  external_img_details          :jsonb
#  file_size                     :integer
#  folder                        :string           default("generic-props-photos")
#  generic_property_section_uuid :uuid
#  generic_property_uuid         :uuid
#  gp_photo_description          :string
#  gp_photo_flags                :integer          default(0), not null
#  gp_photo_gen_prompt           :text
#  gp_photo_position_in_list     :integer
#  gp_photo_reference            :string
#  gp_photo_slug                 :string
#  gp_photo_tags                 :string           default([]), is an Array
#  gp_photo_title                :string
#  height                        :string
#  is_ai_generated_photo         :boolean          default(TRUE)
#  is_external_photo             :boolean          default(FALSE)
#  llm_interaction_uuid          :uuid
#  process_options               :json
#  remote_photo_url              :string
#  site_visitor_token            :string
#  translations                  :jsonb
#  user_uuid                     :uuid
#  uuid                          :uuid
#  width                         :string
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#
# Indexes
#
#  index_generic_property_photos_on_agency_tenant_uuid             (agency_tenant_uuid)
#  index_generic_property_photos_on_discarded_at                   (discarded_at)
#  index_generic_property_photos_on_generic_property_section_uuid  (generic_property_section_uuid)
#  index_generic_property_photos_on_generic_property_uuid          (generic_property_uuid)
#  index_generic_property_photos_on_gp_photo_reference             (gp_photo_reference)
#  index_generic_property_photos_on_uuid                           (uuid)
#
class GenericPropertyPhoto < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  has_one :zac_active_storage_attachment, foreign_key: 'record_id', primary_key: 'id'
  has_one :zac_active_storage_blob, through: :zac_active_storage_attachment,
                                    source: :blob, class_name: 'ZacActiveStorageBlob'
  #  primary_key: 'id', foreign_key: 'blob_id', optional: true

  has_one_attached :generic_property_image do |attachable|
    # attachable.variant :thumb, resize_to_limit: [100, 100] #, preprocessed: true
    attachable.variant :small, resize_to_limit: [300, 300] # , preprocessed: true
    attachable.variant :medium, resize_to_limit: [500, 500], preprocessed: true
    attachable.variant :large, resize_to_limit: [800, 800] # , preprocessed: true
    # attachable.variant :large, resize: "600x600" #, monochrome: true
  end

  belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  belongs_to :generic_property, optional: true, primary_key: 'uuid', foreign_key: 'generic_property_uuid'
  belongs_to :generic_property_section, optional: true, primary_key: 'uuid', foreign_key: 'generic_property_section_uuid'
  # counter_culture :generic_property, column_name: 'ra_photos_count'
  # counter_culture :generic_property_section, column_name: 'sl_photos_count'
  # GenericPropertyPhoto.counter_culture_fix_counts

  # extend Mobility
  # translates :photo_title, :photo_ai_desc

  # # acts_as_list column: :sort_order

  # include FlagShihTzu
  # has_flags 1 => :flag_is_hidden,
  #           2 => :flag_is_local_photo,
  #           :column => 'photo_flags'

  def is_remote_photo?
    external_img_details.present? && external_img_details['remote_image'].present?
  end

  # Ensure the attached image is purged from S3 when the record is destroyed
  before_destroy :purge_generic_property_image

  # before_save :ensure_image_is_under_1_mb

  def image_details
    if is_remote_photo?
      image_details = external_img_details['remote_image'] || {}
      return image_details
    end
    return {} unless generic_property_image.attached?

    default_gp_photo_url = generic_property_image.blob.url
    # default_gp_photo_url = 'https://pwb-pro-dev.s3.eu-west-1.amazonaws.com/psiivzz7zj558aoe9uv1jdariwu6'
    {
      url: default_gp_photo_url, # URL for the original image
      small: {
        url: default_gp_photo_url
        #  generic_property_image.variant(:small).processed.service_url
      },
      medium: {
        url: default_gp_photo_url
        #  generic_property_image.variant(:medium).processed.service_url
      },
      large: {
        url: default_gp_photo_url
      }
      # url: Rails.application.routes.url_helpers.url_for(generic_property_image),
      # small_fit: {
      #   url: Rails.application.routes.url_helpers.url_for(
      #     generic_property_image.variant(resize_to_fit: [200, 200])
      #   )
      # },
      # small_fill: {
      #   url: Rails.application.routes.url_helpers.url_for(
      #     generic_property_image.variant(resize_to_fill: [200, 200])
      #   )
      # }
    }
  rescue ActiveStorage::FileNotFoundError
    nil
  end

  private

  # def ensure_image_is_under_1_mb
  #   return unless generic_property_image.attached?

  #   processed = processed_image
  # end

  def purge_generic_property_image
    generic_property_image.purge_later if generic_property_image.attached?
  end
end
