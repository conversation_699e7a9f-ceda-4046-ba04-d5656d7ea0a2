# == Schema Information
#
# Table name: sale_listings
#
#  id                             :bigint           not null, primary key
#  agency_tenant_uuid             :uuid
#  agency_uuid                    :uuid
#  archived                       :boolean          default(FALSE)
#  catchy_title                   :string           default("")
#  cloned_from_uuid               :string
#  commission_cents               :bigint           default(0), not null
#  commission_currency            :string           default("EUR"), not null
#  currency                       :string
#  description_bullet_points      :jsonb
#  description_long               :text             default("")
#  description_medium             :string           default("")
#  description_short              :string           default("")
#  design_style                   :string
#  details_of_rooms               :jsonb
#  discarded_at                   :datetime
#  extra_sale_details             :jsonb
#  furnished                      :boolean          default(FALSE)
#  hide_map                       :boolean          default(FALSE)
#  highlighted                    :boolean          default(FALSE)
#  host_on_create                 :string           default("unknown_host"), not null
#  import_url                     :string
#  is_ai_generated_listing        :boolean          default(FALSE)
#  listing_pages_count            :integer          default(0), not null
#  listing_slug                   :string
#  listing_tags                   :string           default([]), is an Array
#  llm_interaction_uuid           :uuid
#  llm_interactions_count         :integer          default(0)
#  main_video_url                 :string
#  obscure_map                    :boolean          default(FALSE)
#  page_section_listings_count    :integer          default(0), not null
#  position_in_list               :integer
#  price_sale_current_cents       :bigint           default(0), not null
#  price_sale_current_currency    :string           default("EUR"), not null
#  price_sale_original_cents      :bigint           default(0), not null
#  price_sale_original_currency   :string           default("EUR"), not null
#  property_board_items_count     :integer          default(0), not null
#  publish_from                   :datetime
#  publish_till                   :datetime
#  realty_asset_uuid              :uuid
#  reference                      :string
#  related_urls                   :jsonb
#  reserved                       :boolean          default(FALSE)
#  sale_listing_features          :jsonb
#  sale_listing_flags             :integer          default(0), not null
#  sale_listing_gen_prompt        :text
#  service_charge_yearly_cents    :bigint           default(0), not null
#  service_charge_yearly_currency :string           default("EUR"), not null
#  site_visitor_token             :string
#  sl_photos_count                :integer          default(0), not null
#  sl_uprn                        :string
#  translations                   :jsonb
#  unique_url                     :string           default("")
#  user_uuid                      :uuid
#  uuid                           :uuid
#  versions_count                 :integer          default(0), not null
#  visible                        :boolean          default(FALSE)
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  psq_visit_id                   :bigint
#  realty_asset_id                :integer
#
# Indexes
#
#  index_sale_listings_on_agency_tenant_uuid         (agency_tenant_uuid)
#  index_sale_listings_on_discarded_at               (discarded_at)
#  index_sale_listings_on_highlighted                (highlighted)
#  index_sale_listings_on_is_ai_generated_listing    (is_ai_generated_listing)
#  index_sale_listings_on_listing_slug               (listing_slug)
#  index_sale_listings_on_price_sale_current_cents   (price_sale_current_cents)
#  index_sale_listings_on_price_sale_original_cents  (price_sale_original_cents)
#  index_sale_listings_on_realty_asset_uuid          (realty_asset_uuid)
#  index_sale_listings_on_reference                  (reference)
#  index_sale_listings_on_sale_listing_flags         (sale_listing_flags)
#  index_sale_listings_on_uuid                       (uuid)
#  index_sale_listings_on_visible                    (visible)
#
class SummarySaleListing < SaleListing
  # #  the idea behind creating this model is just to have something that acknowledges that a sale listing
  # #   exists in the search result somewhere but I don't retrieve the full details
  # # using obscure_map below till I migrate is_summary_listing to a boolean
  # default_scope { where(obscure_map: true) }

  # # a summary sale listing won't have a realty asset so will dump
  # # relevant details here:
  # store_attribute :extra_sale_details, :realty_asset_details, :json, default: {}

  # def self.create_from_summary_hash(property_hash, unique_url)
  #   summary_sale_listing = SummarySaleListing.find_or_create_by(
  #     unique_url: unique_url
  #   )

  #   summary_sale_listing.update!(
  #     {
  #       realty_asset_details: property_hash
  #     }
  #   )
  #   summary_sale_listing
  # end
end
