# frozen_string_literal: true

# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromTrulia < ScrapeItem
  # Scope to only rows flagged as Trulia
  # You'll likely add a flag bit later (eg: scrape_is_trulia). Until then, avoid default_scope.

  # Create or find a ScrapeItem for a Trulia listing URL and mark it as Trulia.
  def self.find_or_create_for_h2c_trulia(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: false
    )
    # If a dedicated flag exists later, update here too: scrape_is_trulia: true
    scrape_item.update!(is_realty_search_scrape: false)
    # Re-load as subclass to get subclass methods
    ScrapeItemFromTrulia.find(scrape_item.id)
  end

  # Minimal property extraction for quick testing; Pasarela handles full mapping.
  # Returns a normalised hash comparable to other portals' property_hash_from_scrape_item.
  def property_hash_from_scrape_item
    html = full_content_before_js
    return nil unless html && html.length > 300

    doc = Nokogiri::HTML(html)

    title = doc.at('h1')&.text&.strip || doc.at('title')&.text&.strip
    price_text = doc.at('[data-testid="on-market-price-details"] [data-testid="price"]')&.text ||
                 doc.at('[data-testid="price"]')&.text ||
                 doc.at('.Text__TextBase-sc-hx4quq-0')&.text
    price_val = price_text&.gsub(/[^0-9]/, '')&.to_i

    beds_text = doc.at('[data-testid="bed-bath-beyond"] [data-testid="beds"]')&.text || doc.text
    beds = beds_text[/([0-9]+)\s*Beds?/i, 1]&.to_i

    baths_text = doc.at('[data-testid="bed-bath-beyond"] [data-testid="baths"]')&.text || doc.text
    baths = baths_text[/([0-9]+(?:\.[0-9]+)?)\s*Baths?/i, 1]&.to_f

    area_text = doc.at('[data-testid="bed-bath-beyond"] [data-testid="sqft"]')&.text || doc.text
    area = area_text[/([0-9,]+)\s*sqft/i, 1]&.gsub(',', '')&.to_i

    address = doc.at('[data-testid="home-details-summary-headline"]')&.text&.strip ||
              doc.at('[data-testid="home-details-summary"]')&.text&.strip ||
              doc.at('h1')&.text&.strip

    {
      'title' => title,
      'price' => price_val,
      'bedrooms' => beds,
      'bathrooms' => baths,
      'area' => area,
      'address' => address,
      'listing_image_urls' => extract_image_urls_from_trulia(doc)
    }
  end

  private

  def extract_image_urls_from_trulia(doc)
    candidates = []
    # Try Trulia gallery images
    doc.css('img').each do |img|
      src = img['src'] || img['data-src']
      next unless src
      if src =~ /\.(jpg|jpeg|png|webp)(\?|$)/i
        candidates << src
      end
    end
    candidates.uniq
  end
end
