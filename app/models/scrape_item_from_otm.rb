# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromOtm < ScrapeItem
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  default_scope { scrape_is_onthemarket }

  def self.find_or_create_for_h2c_onthemarket(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point
    )
    scrape_item.update!(scrape_is_onthemarket: true)
    ScrapeItemFromOtm.find(scrape_item.id)
  end

  # def sale_listing_from_scrape_item
  #   standardised_listing_hash = property_hash_from_scrape_item

  #   request_host = 'guise_listing_server'
  #   creator = Creators::Full::FullListingAndAssetCreator.new
  #   sale_listing = creator.create_from_standardised_hash(
  #     standardised_listing_hash,
  #     request_host
  #   )
  #   sale_listing
  # end

  # 1 mar 2025: below def is to do with scraping a single item
  # from a single listing page!
  def property_hash_from_scrape_item
    unless full_content_before_js && full_content_before_js.length > 1000
      puts "full_content_before_js: #{full_content_before_js}"
      raise 'full_content_before_js unavailabl or too short'
    end
    onthemarket_html = full_content_before_js

    puts top_level_url
    doc = Nokogiri::HTML(onthemarket_html)
    property_data = {}
    next_data_script = doc.at('script#__NEXT_DATA__')
    json_content = next_data_script.text
    # Parse the JSON content
    next_data = JSON.parse(json_content)
    # puts next_data
    # meta_file_path = File.join(Rails.root, 'app', 'otm_data.json')
    # File.write(meta_file_path, next_data)

    # Updated for new OnTheMarket structure (June 2025)
    # Property data is now in initialReduxState instead of pageProps
    otm_property = if next_data.dig('props', 'initialReduxState', 'property')
                     next_data['props']['initialReduxState']['property']
                   else
                     # Fallback to old structure for backward compatibility
                     next_data['props']['pageProps']['property']
                   end

    property_documents = otm_property['documents']
    other_otm_urls = otm_property['moreLikeThis'] ? otm_property['moreLikeThis'].map { |link| { 'text' => link['text'], 'url' => "#{top_level_url}#{link['url']}" } } : []

    related_urls_from_otm = {
      property_documents: property_documents,
      other_otm_urls: other_otm_urls
    }
    listing_data = map_property_to_listing_schema(otm_property)
    asset_data = map_property_to_asset_schema(otm_property, related_urls_from_otm: related_urls_from_otm)

    property_data[:listing_data] = listing_data
    property_data[:asset_data] = asset_data

    # Image URLs - updated selectors for new structure
    # Try multiple selectors to find images
    image_urls = []

    # Primary: Get images from JSON data if available
    image_urls = otm_property['images'].map { |img| img['largeUrl'] || img['url'] }.compact if otm_property['images'] && otm_property['images'].is_a?(Array)

    # Fallback: CSS selectors for both old and new structures
    if image_urls.empty?
      image_urls = doc.css('div.swiper-slide picture img, .swiper-slide img').map do |img|
        img['src'] || img['data-src'] || img['srcset']&.split(',')&.first&.strip&.split(' ')&.first
      end.compact
    end

    property_data[:listing_image_urls] = image_urls

    # 18 mar 2025 - below is a temporary thing to enable
    # me to dig deeper into otm data
    property_data['raw_otm_data'] = otm_property

    # # Data from dataLayer script
    data_layer_script = doc.at('script#dataLayerContainer')
    if data_layer_script
      begin
        data_layer_json = JSON.parse(data_layer_script.text.scan(/window.dataLayer.push\((.*)\)/).flatten.first)
        # All of this needed because postal_code is
        # not available elsewhere
        property_data[:postal_code] = data_layer_json['postcode']
        # property_data[:listing_data][:postal_code] = data_layer_json['postcode']
        property_data[:asset_data][:postal_code] = data_layer_json['postcode']
      rescue JSON::ParserError => e
        puts "Error parsing dataLayer JSON: #{e.message}"
      end
    end

    # URL
    property_data['import_url'] = doc.at('link[rel="canonical"]')['href'] if doc.at('link[rel="canonical"]')

    # data_file_path = File.join(
    #   Rails.root, 'db', 'example_data/standardised_property_data_hash.json'
    # )
    # # Ensure the directory exists
    # FileUtils.mkdir_p(File.dirname(data_file_path))
    # # Write the file
    # File.write(data_file_path, property_data)

    property_data.stringify_keys!
    # property_data.compact # Remove nil values
  end

  private

  def map_property_to_asset_schema(property, related_urls_from_otm)
    # puts related_urls_from_otm
    {
      'related_urls_from_otm' => related_urls_from_otm,
      'categories' => property['features'] ? property['features'].map { |f| { 'id' => f['id'], 'name' => f['feature'] } } : [],
      'city' => property['addressLocality'],
      'city_search_key' => property['addressLocality']&.downcase&.gsub(/\s+/, '-') || '',
      'constructed_area' => 0.0, # Not directly available, default to 0.0
      'count_bathrooms' => property['bathrooms'].to_f || 0.0,
      'count_bedrooms' => property['bedrooms'] || 0,
      'count_garages' => property['description'].to_s.downcase.include?('garage') ? 1 : 0, # Infer from description
      'count_toilets' => 0, # Not available, default to 0
      'country' => property.dig('agent', 'ukCountry')&.capitalize,
      'description' => property['description'],
      'details' => property['rooms'] ? property['rooms']['descriptions'].map { |room| [room['name'], room] }.to_h : {},
      'discarded_at' => nil, # Not available, set to nil
      'energy_performance' => nil, # Not available, set to nil
      'energy_rating' => nil, # Not available, set to nil
      'floor' => nil, # Not directly available, set to nil
      'has_rental_listings' => false, # Default value
      'has_sale_listings' => true, # Set to true as this is a sale listing
      'has_sold_transactions' => false, # Default value
      'host_on_create' => 'unknown_host', # Default value
      # 'import_url' => property['canonicalUrl'],
      'is_ai_generated_realty_asset' => false, # Default value
      'latitude' => property.dig('location', 'lat'),
      'longitude' => property.dig('location', 'lon'),
      'neighborhood' => nil, # Not directly available, set to nil
      'neighborhood_search_key' => '',
      'plot_area' => 0.0, # Not available, default to 0.0
      'postal_code' => property['postal_code'],
      # 'postal_code' => property.dig('agent', 'postcode'),
      # 'prop_origin_key' => property.dig('agent', 'companyName')&.downcase&.gsub(/\s+/, '-') || '',
      'prop_state_key' => 'new', # Assuming "new" based on newHomeFlag
      'prop_type_key' => property['humanisedPropertyType']&.downcase&.gsub(/\s+/, '-') || '',
      'province' => property.dig('metadata', 'dataLayer', 'parent-locations', 2), # Extract region if available
      'ra_photos_count' => property['images']&.size || 0,
      'realty_asset_flags' => 0, # Default value
      'realty_asset_tags' => property['propertyLabels'] || [],
      'reference' => property['id'],
      'region' => property.dig('metadata', 'dataLayer', 'parent-locations', 2),
      'rental_listings_count' => 0, # Default value
      'sale_listings_count' => 1, # Assuming this is the only sale listing for this property
      # 'site_visitor_token' => nil, # Not available, set to nil
      'sold_transactions_count' => 0, # Default value
      'street_address' => property['displayAddress'],
      # 'street_name' => property.dig('agent', 'addressline1'),
      'street_number' => nil, # Not directly available, set to nil
      'title' => property['propertyTitle'],
      'year_construction' => 0 # Not available, default to 0
    }
  end

  def map_property_to_listing_schema(property)
    {
      'title' => property['propertyTitle'],
      'description' => property['description'],
      'archived' => false, # Default value
      'commission_cents' => 0, # Default value
      'commission_currency' => 'GBP', # Default value
      'currency' => 'GBP', # Assuming all prices are in GBP if not specified otherwise
      'design_style' => nil, # Not available, set to nil
      'details_of_rooms' => property['rooms'] ? property['rooms']['descriptions'].map { |room| [room['name'], room] }.to_h : {},
      'discarded_at' => nil, # Not available, set to nil
      'extra_sale_details' => {},
      'furnished' => false, # Default value
      'hide_map' => false, # Default value
      'highlighted' => false, # Default value
      'host_on_create' => 'unknown_host', # Default value
      # 'import_url' => property['canonicalUrl'],
      'is_ai_generated_listing' => false, # Default value
      'listing_pages_count' => 0, # Default value
      'listing_slug' => property['id'],
      'listing_tags' => [],
      'main_video_url' => property.dig('videotours', 0, 'url'),
      'obscure_map' => false, # Default value
      'page_section_listings_count' => 0, # Default value
      'position_in_list' => nil, # Not available, set to nil
      'price_sale_current_cents' => (property['priceRaw'] * 100).to_i, # Convert to cents
      'price_sale_current_currency' => 'GBP', # Assuming GBP as per schema default
      'price_sale_original_cents' => (property['priceRaw'] * 100).to_i, # Assuming original price is the same as current
      'price_sale_original_currency' => 'GBP', # Assuming GBP as per schema default
      'property_board_items_count' => 0, # Default value
      'publish_from' => nil, # Not available, set to nil
      'publish_till' => nil, # Not available, set to nil
      'reference' => property['id'],
      'related_urls' => {},
      'reserved' => false, # Default value
      'sale_listing_features' => property['features']&.map { |feature| [feature['id'], feature['feature']] }.to_h || {},
      'sale_listing_flags' => 0, # Default value
      'sale_listing_gen_prompt' => nil, # Not available, set to nil
      'service_charge_yearly_cents' => 0, # Default value
      'service_charge_yearly_currency' => 'GBP', # Default value
      # 'site_visitor_token' => nil, # Not available, set to nil
      'sl_photos_count' => property['images']&.size || 0,
      'visible' => true # Default value, assuming all listings are visible unless specified otherwise
    }
  end
end
