# == Schema Information
#
# Table name: postcode_geo_clusters
#
#  id                                                                                            :bigint           not null, primary key
#  agency_tenant_uuid                                                                            :uuid
#  discarded_at                                                                                  :datetime
#  geo_cluster_uuid                                                                              :uuid
#  is_primary_for_postcode                                                                       :boolean          default(TRUE)
#  percent_diff_to_cluster_price(Percentage difference from median price of the_to cluster area) :integer          default(0), not null
#  position_in_cluster                                                                           :integer
#  postcode_area_uuid                                                                            :uuid
#  postcode_cluster_description                                                                  :string
#  postcode_cluster_flags                                                                        :integer          default(0), not null
#  postcode_cluster_slug                                                                         :string
#  postcode_cluster_tags                                                                         :string           default([]), is an Array
#  postcode_cluster_title                                                                        :string
#  translations                                                                                  :jsonb
#  uuid                                                                                          :uuid
#  visible_in_cluster                                                                            :boolean          default(TRUE)
#  created_at                                                                                    :datetime         not null
#  updated_at                                                                                    :datetime         not null
#
# Indexes
#
#  index_postcode_geo_clusters_on_agency_tenant_uuid      (agency_tenant_uuid)
#  index_postcode_geo_clusters_on_discarded_at            (discarded_at)
#  index_postcode_geo_clusters_on_geo_cluster_uuid        (geo_cluster_uuid)
#  index_postcode_geo_clusters_on_postcode_area_uuid      (postcode_area_uuid)
#  index_postcode_geo_clusters_on_postcode_cluster_flags  (postcode_cluster_flags)
#  index_postcode_geo_clusters_on_postcode_cluster_slug   (postcode_cluster_slug)
#  index_postcode_geo_clusters_on_uuid                    (uuid)
#
class PostcodeGeoCluster < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  include FlagShihTzu
  # 9 Jan 2025: currently an experiment
  has_flags 1 => :fully_processed_flg,
            :column => 'postcode_cluster_flags'

  belongs_to :geo_cluster, class_name: 'GeoCluster', foreign_key: 'geo_cluster_uuid', primary_key: :uuid, optional: false
  belongs_to :postcode_area, class_name: 'PostcodeArea', foreign_key: 'postcode_area_uuid', primary_key: :uuid, optional: false
  # # If you decide to use has_and_belongs_to_many for a join table approach:
  # has_and_belongs_to_many :postcode_areas, join_table: :postcode_area_clusters_postcode_areas,
  #                                          foreign_key: :postcode_area_cluster_uuid,
  #                                          association_foreign_key: :postcode_area_uuid

  # # Custom method to retrieve related postcode areas using postcode_area_uuids
  # def filtered_postcode_areas
  #   PostcodeArea.where('uuid = ANY(ARRAY[:uuids]::uuid[])', uuids: postcode_area_uuids)
  # end

  # # Alternatively, if you want to use the primary_cluster_uuid and secondary_cluster_uuid:
  # has_many :primary_postcode_areas, class_name: 'PostcodeArea', foreign_key: :primary_cluster_uuid, primary_key: :uuid
  # has_many :secondary_postcode_areas, class_name: 'PostcodeArea', foreign_key: :secondary_cluster_uuid, primary_key: :uuid
  # Validations
  validates :geo_cluster_uuid, presence: true
  validates :postcode_area_uuid, presence: true
  # validates :postcode_cluster_slug, presence: true, uniqueness: true
  # validates :percent_diff_to_cluster_price, presence: true

  # Ensure the combination of geo_cluster_uuid and postcode_area_uuid is unique
  validates :geo_cluster_uuid, uniqueness: { scope: :postcode_area_uuid, message: 'and Postcode Area combination must be unique' }
end
