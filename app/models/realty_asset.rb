# == Schema Information
#
# Table name: realty_assets
#
#  id                           :bigint           not null, primary key
#  agency_tenant_uuid           :uuid
#  agency_uuid                  :uuid
#  area_unit                    :integer          default("sqmt")
#  categories                   :jsonb
#  city                         :string
#  city_search_key              :string           default(""), not null
#  cloned_from_uuid             :string
#  constructed_area             :float            default(0.0), not null
#  count_attics                 :integer          default(0), not null
#  count_basements              :integer          default(0), not null
#  count_bathrooms              :float            default(0.0), not null
#  count_bedrooms               :integer          default(0), not null
#  count_dining_rooms           :integer          default(0), not null
#  count_fireplaces             :integer          default(0), not null
#  count_garages                :integer          default(0), not null
#  count_gyms                   :integer          default(0), not null
#  count_kitchens               :integer          default(0), not null
#  count_living_rooms           :integer          default(0), not null
#  count_parking_spaces         :integer          default(0), not null
#  count_studies                :integer          default(0), not null
#  count_toilets                :integer          default(0), not null
#  count_utility_rooms          :integer          default(0), not null
#  country                      :string
#  description                  :text
#  details                      :jsonb
#  discarded_at                 :datetime
#  dossier_assets_count         :integer
#  energy_performance           :float
#  energy_rating                :integer
#  epc_details_count            :integer          default(0), not null
#  floor                        :string
#  geo_area_uuid                :uuid
#  geocoded_address             :string           default("")
#  has_backyard                 :boolean
#  has_chimney                  :boolean
#  has_driveway                 :boolean
#  has_garden                   :boolean
#  has_pool                     :boolean
#  has_rental_listings          :boolean          default(FALSE)
#  has_sale_listings            :boolean          default(FALSE)
#  has_sold_transactions        :boolean          default(FALSE)
#  host_on_create               :string           default("unknown_host"), not null
#  import_url                   :string
#  initial_source               :string
#  is_ai_generated_realty_asset :boolean          default(FALSE)
#  latitude                     :float
#  llm_interaction_uuid         :uuid
#  llm_interactions_count       :integer          default(0)
#  longitude                    :float
#  neighborhood                 :string
#  neighborhood_search_key      :string           default(""), not null
#  number_habitable_rooms       :string           default(""), not null
#  number_heated_rooms          :string           default(""), not null
#  plot_area                    :float            default(0.0), not null
#  postal_code                  :string
#  postcode_area_uuid           :uuid
#  prop_origin_key              :string           default(""), not null
#  prop_state_key               :string           default(""), not null
#  prop_type_key                :string           default(""), not null
#  province                     :string
#  ra_photos_count              :integer          default(0), not null
#  ra_sources                   :jsonb
#  ra_uprn                      :string
#  realty_asset_flags           :integer          default(0), not null
#  realty_asset_tags            :string           default([]), is an Array
#  reference                    :string
#  region                       :string
#  related_urls                 :jsonb
#  rental_listings_count        :integer          default(0), not null
#  room_details                 :jsonb
#  sale_listings_count          :integer          default(0), not null
#  site_visitor_token           :string
#  sold_transaction_epcs_count  :integer          default(0), not null
#  sold_transactions_count      :integer          default(0), not null
#  street_address               :string
#  street_name                  :string
#  street_number                :string
#  title                        :string
#  total_floor_area             :string           default(""), not null
#  translations                 :jsonb
#  unique_url                   :string           default("")
#  user_uuid                    :uuid
#  uuid                         :uuid
#  versions_count               :integer          default(0), not null
#  year_construction            :integer          default(0), not null
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  google_place_id              :string
#  psq_visit_id                 :bigint
#
# Indexes
#
#  index_realty_assets_on_agency_tenant_uuid       (agency_tenant_uuid)
#  index_realty_assets_on_categories               (categories) USING gin
#  index_realty_assets_on_city_search_key          (city_search_key)
#  index_realty_assets_on_constructed_area         (constructed_area)
#  index_realty_assets_on_count_bathrooms          (count_bathrooms)
#  index_realty_assets_on_count_bedrooms           (count_bedrooms)
#  index_realty_assets_on_count_garages            (count_garages)
#  index_realty_assets_on_count_toilets            (count_toilets)
#  index_realty_assets_on_discarded_at             (discarded_at)
#  index_realty_assets_on_geo_area_uuid            (geo_area_uuid)
#  index_realty_assets_on_has_sale_listings        (has_sale_listings)
#  index_realty_assets_on_has_sold_transactions    (has_sold_transactions)
#  index_realty_assets_on_latitude_and_longitude   (latitude,longitude)
#  index_realty_assets_on_neighborhood_search_key  (neighborhood_search_key)
#  index_realty_assets_on_plot_area                (plot_area)
#  index_realty_assets_on_postcode_area_uuid       (postcode_area_uuid)
#  index_realty_assets_on_reference                (reference)
#  index_realty_assets_on_sale_listings_count      (sale_listings_count)
#  index_realty_assets_on_sold_transactions_count  (sold_transactions_count)
#  index_realty_assets_on_uuid                     (uuid)
#
class RealtyAsset < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: true)

  store_attribute :related_urls, :related_urls_from_otm, :json, default: {}
  store_attribute :details, :close_uprn_details_json, :json, default: {}

  store_attribute :details, :asset_polygon, :json, default: {}

  has_many :dossier_assets, primary_key: 'uuid', foreign_key: 'realty_asset_uuid'

  before_validation :fix_nil_values, on: %i[create update]
  # https://karolgalanciak.com/blog/2017/10/29/the-case-against-exotic-usage-of-before-validate-callbacks/
  # Might consider alternative solution as per above later
  def fix_nil_values
    %w[count_bathrooms count_bedrooms count_garages count_toilets
       year_construction plot_area constructed_area].each do |non_null_number_col|
      self[non_null_number_col] = 0 if self[non_null_number_col].nil?
    end
  end

  include Discard::Model

  # def ra_uprn
  #   cloned_from_uuid
  #   # also need fields from epc like: epc_details_count
  #   # total_floor_area
  #   # total_rooms
  # end
  # # Geocoding setup
  # geocoded_by :full_street_address
  # after_validation :geocode, if: ->(obj) { obj.street_address.present? and obj.street_address_changed? }

  # # build an address from street, city, and state attributes
  # geocoded_by :address_from_components

  # store the fetched address in the address attribute
  reverse_geocoded_by :latitude, :longitude, address: :street_address

  def figure_out_close_uprn_details(save_to_json: true)
    uprn_details = UprnDetail.near([latitude, longitude], 0.04, units: :mi)

    results = uprn_details.map do |uprn_detail|
      {
        uprn_id: uprn_detail.uprn, # Or uprn_detail.uprn if you prefer UPRN value
        distance: uprn_detail.distance,
        uprn_possible_street: uprn_detail.uprn_possible_street,
        sold_transactions: uprn_detail.sold_transactions.as_minimal_json,
        epc_details: uprn_detail.epc_details.as_summary_json
        #  || uprn_detail.epc_detail&.address1
      }
    end
    if save_to_json
      update(close_uprn_details_json: results) # Assuming you have a json column named close_uprn_details_json.
    end
    results # Return the results if needed.
  end

  # def find_closest_uprn_detail
  #   UprnDetail.near([latitude, longitude], 0.04, units: :mi).first
  # end
  # reverse_geocoded_by :latitude, :longitude do |obj, results|
  # Thought about filter here for Google Maps results which fitted below:
  #  "location_type"=>"GEOMETRIC_CENTER",
  #   if geo = results.first
  #     obj.city = geo.city
  #   end
  # end
  # adds methods like:
  # RealtyAsset.last.nearbys(0.3).length
  # RealtyAsset.last.distance_from([40.714,-100.234])

  # visitable :psq_visit
  # below only needed for counter_cache - above would normally take care of r/n
  # belongs_to :visit, foreign_key: "psq_visit_id", optional: true, class_name: "Ahoy::Visit", counter_cache: true

  # has_paper_trail

  belongs_to :postcode_area, class_name: 'PostcodeArea', foreign_key: 'postcode_area_uuid', optional: true

  belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  has_many :asset_photos, lambda {
                            order 'sort_order asc'
                          }, class_name: 'RealtyAssetPhoto', primary_key: 'uuid',
                             foreign_key: 'realty_asset_uuid',
                             dependent: :destroy
  # # Apr 2023 - below not currently used
  # has_many :ordered_visible_asset_photos, lambda {
  #   not_flag_is_hidden.order('sort_order asc')
  #   # where(visible_for_sale_listing: true).order("sort_order asc")
  # }, class_name: 'RealtyAssetPhoto', primary_key: 'uuid', foreign_key: 'realty_asset_uuid'
  has_many :rental_listings, primary_key: 'uuid', foreign_key: 'realty_asset_uuid'
  #  dependent: :destroy
  # enabling dependent: :destroy above or below would lead to
  # stack level too deep (SystemStackError) when deleting

  has_many :sale_listings, primary_key: 'uuid', foreign_key: 'realty_asset_uuid'

  belongs_to :uprn_detail, class_name: 'UprnDetail', foreign_key: 'ra_uprn', primary_key: 'uprn', optional: true
  # I could get below through above - not sure which makes more sense
  has_many :sold_transactions, primary_key: 'uuid', foreign_key: 'realty_asset_uuid'
  has_many :epc_details, primary_key: 'uuid', foreign_key: 'realty_asset_uuid'
  has_many :sold_transaction_epcs, primary_key: 'uuid', foreign_key: 'realty_asset_uuid'

  has_many :uk_sold_transactions, class_name: 'SoldTransaction', primary_key: 'ra_uprn', foreign_key: 'st_uprn'
  has_many :uk_epc_details, class_name: 'EpcDetail', primary_key: 'ra_uprn', foreign_key: 'uprn'

  # has_many :scraped_page_realty_asset_items,
  #          foreign_key: 'realty_asset_uuid',
  #          primary_key: 'uuid'
  # has_many :scraped_pages, through: :scraped_page_realty_asset_items # , source_type: "SaleListing" #source: :listable,

  enum area_unit: { sqmt: 0, sqft: 1 }

  def process_rightmove_address(address_string)
    # Regular expression to match UK postcode format
    postcode_regex = /\b([A-Z]{1,2}[0-9][A-Z0-9]? [0-9][A-Z]{2})\b/

    # Extract postcode
    postcode_match = address_string.match(postcode_regex)
    self.postal_code = postcode_match[1] if postcode_match

    # Remove postcode and split remaining string by commas
    address_parts = address_string.gsub(postcode_regex, '').split(',')

    # Assign address parts to model attributes
    self.street_number = address_parts[0].strip if address_parts[0]
    self.street_name = address_parts[1].strip if address_parts[1]
    self.city = address_parts[2].strip if address_parts[2]
    self.province = address_parts[3].strip if address_parts[3]
    save
  end

  def find_or_create_listings
    # ensure asset has uuid:
    reload
    listing_slug = reference.presence || 'no-ref'
    listing_slug += '-' + uuid.presence
    # TODO: - rethink above to figure out better way of creating slugs
    new_sale_listing = sale_listings.first_or_create
    # 2 lines below needed to ensure validation of slug presence on create doesn't fail!
    new_sale_listing.slug = listing_slug
    new_sale_listing.save!
    new_sale_listing.reload
    I18n.available_locales.each do |locale|
      new_sale_listing.slug_backend.write locale, listing_slug # new_sale_listing.uuid
    end
    new_sale_listing.visible = true
    new_sale_listing.save!
    new_rental_listing = rental_listings.first_or_create
    # 2 lines below needed to ensure validation of slug presence on create doesn't fail!
    new_rental_listing.slug = listing_slug
    new_rental_listing.save!
    new_rental_listing.reload
    I18n.available_locales.each do |locale|
      new_rental_listing.slug_backend.write locale, listing_slug # new_sale_listing.uuid
    end
    new_rental_listing.visible = true
    new_rental_listing.save!
    reload
  end

  def formatted_constructed_area
    suffix = ' ㎡'
    # Might need to use encoding for above to stop errors in linter
    # https://www.compart.com/en/unicode/U+33A1
    suffix = ' sq ft' if area_unit == 'sqft'
    # constructed_area is a float - round(0) removes decimal places
    constructed_area.round(0).to_s + suffix
  end

  # below to help with display for admins:
  def default_sale_listing
    sale_listings.first
  end

  def default_formatted_sale_price
    return default_sale_listing.formatted_display_price if default_sale_listing

    nil
  end

  def default_rental_listing
    rental_listings.first
  end

  def default_formatted_rental_price
    return default_rental_listing.formatted_display_price if default_rental_listing

    nil
  end

  def client_mgmt_url(client_base_url, model_name, content_locale)
    # return Rails.application.routes.url_helpers.rental_listing_with_asset_path(slug: uuid, locale: I18n.locale)
    client_base_url +
      Rails.application.routes.url_helpers.pwb_clients_listing_edit_path(
        editor_ui_locale: 'en',
        realty_asset_uuid: uuid,
        content_locale:,
        model_name:,
        action_name: 'text'
      )
    # "/fr/pwb_clients/edit_listing/73d429b8-ffb8-411c-8da3-232174a77166/fr/m/sale/text"
  end

  def rental_show_url
    return default_rental_listing.listing_show_url if default_rental_listing

    nil
  end

  def sale_show_url
    return default_sale_listing.listing_show_url if default_sale_listing

    nil
  end

  def rental_slug
    return default_rental_listing.slug if default_rental_listing

    nil
  end

  def sale_slug
    return default_sale_listing.slug if default_sale_listing

    nil
  end

  def destroy_assoc_listings
    # puts "#{RealtyAssetPhoto.count} RealtyAssetPhotos"
    # puts "#{ListingPage.count} ListingPages"
    # puts "#{Page.count} Pages"
    if default_rental_listing
      # TODO: - use dependent destroy or some better way of doing this:
      default_rental_listing.listing_photos.destroy_all
      default_rental_listing.rendering_pages.destroy_all
      default_rental_listing.listing_pages.destroy_all
    end
    rental_listings.destroy_all
    if default_sale_listing
      default_sale_listing.listing_photos.destroy_all
      default_sale_listing.rendering_pages.destroy_all
      default_sale_listing.listing_pages.destroy_all
    end
    sale_listings.destroy_all
    # puts "#{RealtyAssetPhoto.count} RealtyAssetPhotos"
    # puts "#{ListingPage.count} ListingPages"
    # puts "#{Page.count} Pages"
  end

  # after_create :set_bvh_geojson
  # after_commit :set_bvh_geojson, on: :create

  # def set_bvh_geojson_async
  #   GeoJsonJob.perform_later(id)
  # end

  # below didn't work in after_create callback as url etc
  # was not always set on creation.
  # Calling from dossier asset creator instead.t
  def set_bvh_geojson
    return unless import_url.include? 'buenavistahomes'

    location_name = "#{city}, #{country}"
    service = Geo::GeoJsonExtractor.new
    puts "fetching geojson for #{location_name}"
    result = service.fetch_geojson_from_nominatim(location_name) # ('CV11 6FA')
    self.asset_polygon = result
    save!
  end
end
