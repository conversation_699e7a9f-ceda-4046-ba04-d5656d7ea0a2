# == Schema Information
#
# Table name: llm_interactions
#
#  id                            :bigint           not null, primary key
#  aasm_state                    :string
#  agency_tenant_uuid            :uuid
#  ai_model                      :string
#  chosen_response               :jsonb
#  chosen_response_finish_reason :string
#  cloned_from_uuid              :string
#  discarded_at                  :datetime
#  endpoint                      :string
#  extra_llm_interaction_details :jsonb
#  extra_params                  :jsonb
#  flags                         :integer          default(0), not null
#  full_prompt                   :text
#  full_response                 :jsonb
#  has_errored                   :boolean          default(TRUE)
#  llm_error_message             :string
#  llm_interaction_slug          :string
#  max_tokens                    :integer
#  related_llm_interactions      :jsonb
#  response_choices              :jsonb
#  response_completion_tokens    :integer
#  response_model                :string
#  response_object               :string
#  response_prompt_tokens        :integer
#  response_total_tokens         :integer
#  result_confidence             :integer
#  temperature                   :float
#  translations                  :jsonb
#  user_uuid                     :uuid
#  uuid                          :uuid
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  response_id                   :string
#
# Indexes
#
#  index_llm_interactions_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_llm_interactions_on_discarded_at        (discarded_at)
#  index_llm_interactions_on_flags               (flags)
#  index_llm_interactions_on_uuid                (uuid)
#
class LlmInteraction < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  include Discard::Model

  store_attribute :extra_llm_interaction_details, :vcr_cassette_path, :string, default: ''
  store_attribute :extra_llm_interaction_details, :images_for_llm, :string, default: ''

  has_many :dossier_info_sources, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid'

  # has_one :sale_listing, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid'
  has_one :realty_asset_photo, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid'
  has_one :realty_asset, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid'
  # feb 2025 - would make a lot more sense for the relationships above
  # to be reversed so that the LlmInteraction belongs_to the SaleListing etc
  belongs_to :sale_listing, primary_key: 'uuid', foreign_key: 'sale_listing_uuid', optional: true

  has_many :llm_interaction_associations, foreign_key: 'llm_interaction_uuid', primary_key: 'uuid'
  has_many :sale_listings, through: :llm_interaction_associations, source: :associable, source_type: 'SaleListing'
  # ... other LlmInteraction attributes and methods ...

  include FlagShihTzu
  has_flags 1 => :flag_is_selected_interaction,
            # 2 => :flag_is_realty_asset_interaction,
            :column => 'flags'
  # has_flags 1 => :flag_is_sale_listing_interaction,
  #           2 => :flag_is_realty_asset_interaction,
  #           :column => 'flags'

  def self.initialize_llm_interaction(prompt, llm_interaction_slug, llm_parameters)
    # TODO: - add a @client.llm_client_slug that I can use below
    # llm_interaction_slug = "#{model_name}-#{endpoint}-#{SecureRandom.uuid[0..4]}"
    llm_i = LlmInteraction.find_or_create_by!(
      llm_interaction_slug: llm_interaction_slug
    )
    llm_i.update!(
      ai_model: llm_parameters[:model],
      # endpoint:,
      full_prompt: prompt,
      temperature: llm_parameters[:temperature],
      has_errored: true
    )
    llm_i
  end

  def update_generic_llm_interaction_success(success_details)
    response ||= {}
    # response_model = response['model']
    # response_id = response['id'] || '??..??'
    update!(
      success_details
    )
  end

  def update_langchanrb_llm_interaction_success(
    langchanrb_response, chosen_response
  )
    # response ||= {}
    response_prompt_tokens = langchanrb_response.raw_response.dig('usageMetadata', 'promptTokenCount')
    response_completion_tokens = langchanrb_response.raw_response.dig('usageMetadata', 'candidatesTokenCount')
    response_total_tokens = langchanrb_response.raw_response.dig('usageMetadata', 'totalTokenCount')
    update!(
      full_response: langchanrb_response,
      chosen_response:,
      # response_object: response['object'],
      # response_id:,
      response_model: langchanrb_response.model,
      response_prompt_tokens:,
      response_completion_tokens:,
      response_total_tokens:,
      has_errored: false
    )
  end

  def update_llm_interaction_success(response, chosen_response)
    response ||= {}
    response_model = response['model']
    response_id = response['id'] || '??..??'
    update!(
      full_response: response,
      chosen_response:,
      response_object: response['object'],
      response_id:,
      response_model: response_model,
      response_prompt_tokens: response.dig('usage', 'prompt_tokens'),
      response_completion_tokens: response.dig('usage', 'completion_tokens'),
      response_total_tokens: response.dig('usage', 'total_tokens'),
      has_errored: false
    )
  end

  def update_llm_interaction_with_error(response, error_message)
    response ||= {}
    if response.is_a?(RubyLLM::Message)
      response = response.as_json
      # client_slug = @client.get_client_slug
      response_model = response['model_id']
      response_id = 'unknown_response_id'
    else
      # client_slug = @client.get_client_slug
      response_model = response['model']
      response_id = response['id'] || '??..??'
    end
    update!(
      full_response: response,
      response_object: response['object'],
      response_id:,
      response_model:,
      response_prompt_tokens: response.dig('usage', 'prompt_tokens'),
      response_completion_tokens: response.dig('usage', 'completion_tokens'),
      response_total_tokens: response.dig('usage', 'total_tokens'),
      has_errored: true,
      llm_error_message: error_message
    )
  end

  def listing_hash_from_llm_interaction(extracted_content: {})
    property_data = {}
    json_from_llm = JSON.parse(chosen_response)
    listing_data = map_to_listing_schema(json_from_llm.merge(extracted_content))
    asset_data = map_to_asset_schema(json_from_llm.merge(extracted_content))

    property_data[:listing_data] = listing_data
    property_data[:asset_data] = asset_data
    # Image URLs
    # property_data[:listing_image_urls] = doc.css('div.swiper-slide picture img').map { |img| img['src'] }
    property_data
  end

  def map_to_listing_schema(llm_attributes)
    # Convert price to cents (integer)
    price_raw = begin
      (llm_attributes['price_sale_current'].to_f * 100).to_i
    rescue StandardError
      0
    end

    # Build the listing data hash based on the LLM extracted attributes
    {
      'archived' => false,
      'commission_cents' => 0,
      'commission_currency' => llm_attributes['currency'] || 'GBP',
      'currency' => llm_attributes['currency'] || 'GBP',
      'design_style' => nil,
      'details_of_rooms' => {}, # Could be improved with more specific LLM prompting
      'discarded_at' => nil,
      'extra_sale_details' => {},
      'furnished' => llm_attributes['description']&.downcase&.include?('furnished') || false,
      'hide_map' => false,
      'highlighted' => false,
      'host_on_create' => 'unknown_host',
      'import_url' => llm_attributes['import_url'],
      'is_ai_generated_listing' => false,
      'listing_pages_count' => 0,
      'listing_slug' => llm_attributes['reference']&.downcase&.gsub(/\s+/, '-') || '',
      'listing_tags' => [],
      'main_video_url' => nil, # Could extract this with more specific LLM prompting
      'obscure_map' => false,
      'page_section_listings_count' => 0,
      'position_in_list' => nil,
      'price_sale_current_cents' => price_raw,
      'price_sale_current_currency' => llm_attributes['currency'] || 'GBP',
      'price_sale_original_cents' => price_raw,
      'price_sale_original_currency' => llm_attributes['currency'] || 'GBP',
      'property_board_items_count' => 0,
      'publish_from' => nil,
      'publish_till' => nil,
      'reference' => llm_attributes['reference'],
      'related_urls' => {},
      'reserved' => llm_attributes['property_status']&.downcase&.include?('reserved') || false,
      'sale_listing_features' => llm_attributes['features']&.map&.with_index { |f, i| [i.to_s, f] }&.to_h || {},
      'sale_listing_flags' => 0,
      'sale_listing_gen_prompt' => nil,
      'service_charge_yearly_cents' => 0,
      'service_charge_yearly_currency' => llm_attributes['currency'] || 'GBP',
      'site_visitor_token' => nil,
      'sl_photos_count' => 0, # Will be updated after processing
      'visible' => true
    }
  end

  def map_to_asset_schema(llm_attributes)
    # Process features into categories
    categories = llm_attributes['features']&.map&.with_index do |feature, idx|
      { 'id' => idx, 'name' => feature }
    end || []

    # Build the asset data hash based on the LLM extracted attributes
    {
      'categories' => categories,
      'city' => llm_attributes['city'],
      'city_search_key' => llm_attributes['city']&.downcase&.gsub(/\s+/, '-') || '',
      'constructed_area' => llm_attributes['constructed_area']&.to_f || 0.0,
      'count_bathrooms' => llm_attributes['count_bathrooms']&.to_f || 0.0,
      'count_bedrooms' => llm_attributes['count_bedrooms']&.to_i || 0,
      'count_garages' => llm_attributes['description']&.downcase&.include?('garage') ? 1 : 0,
      'count_toilets' => 0,
      'country' => llm_attributes['country'],
      'description' => llm_attributes['description'],
      'details' => {},
      'discarded_at' => nil,
      'energy_performance' => nil,
      'energy_rating' => llm_attributes['energy_rating'],
      'floor' => nil,
      'has_rental_listings' => llm_attributes['property_status']&.downcase&.include?('rent') || false,
      'has_sale_listings' => llm_attributes['property_status']&.downcase&.include?('sale') || true,
      'has_sold_transactions' => llm_attributes['property_status']&.downcase&.include?('sold') || false,
      'host_on_create' => 'unknown_host',
      'is_ai_generated_realty_asset' => false,
      'latitude' => llm_attributes['latitude']&.to_f,
      'longitude' => llm_attributes['longitude']&.to_f,
      'neighborhood' => nil,
      'neighborhood_search_key' => '',
      'plot_area' => llm_attributes['plot_area']&.to_f || 0.0,
      'postal_code' => llm_attributes['postal_code'],
      'prop_state_key' => llm_attributes['is_new_home'] ? 'new' : 'resale',
      'prop_type_key' => llm_attributes['property_type']&.downcase&.gsub(/\s+/, '-') || '',
      'province' => llm_attributes['region'],
      'ra_photos_count' => 0, # Will be updated after processing
      'realty_asset_flags' => 0,
      'realty_asset_tags' => [],
      'reference' => llm_attributes['reference'],
      'region' => llm_attributes['region'],
      'rental_listings_count' => 0,
      'sale_listings_count' => 1,
      'site_visitor_token' => nil,
      'sold_transactions_count' => 0,
      'street_address' => llm_attributes['street_address'],
      'street_number' => llm_attributes['street_address']&.match(/^\d+/)&.[](0),
      'title' => llm_attributes['title'],
      'year_construction' => llm_attributes['year_construction']&.to_i || 0
    }
  end
end
