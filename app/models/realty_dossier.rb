# == Schema Information
#
# Table name: realty_dossiers
#
#  id                            :bigint           not null, primary key
#  agency_tenant_uuid            :uuid             not null
#  discarded_at                  :datetime
#  dossier_assets_count          :integer          default(0)
#  dossier_checklists            :jsonb
#  dossier_description           :string
#  dossier_notes                 :jsonb
#  dossier_places_of_interest    :jsonb
#  dossier_related_urls          :jsonb
#  dossier_searches_count        :integer          default(0)
#  dossier_significant_dates     :jsonb
#  dossier_source_portal         :integer          default(0), not null
#  dossier_starting_url          :string
#  dossier_title                 :string
#  dossier_user_uuid             :uuid
#  listing_history               :jsonb
#  mortgage_projections          :jsonb
#  params_for_similar_properties :jsonb
#  realty_dossier_aasm_state     :string
#  realty_dossier_details        :jsonb
#  realty_dossier_flags          :integer          default(0), not null
#  site_visitor_token            :string
#  translations                  :jsonb
#  uuid                          :uuid
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#
# Indexes
#
#  index_realty_dossiers_on_discarded_at          (discarded_at)
#  index_realty_dossiers_on_realty_dossier_flags  (realty_dossier_flags)
#  index_realty_dossiers_on_uuid                  (uuid)
#
class RealtyDossier < ApplicationRecord
  # Multi-tenancy setup
  acts_as_tenant :agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false

  include Discard::Model

  # Associations
  # Search-related associations
  has_many :dossier_searches, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid'
  has_many :realty_search_queries, through: :dossier_searches

  has_many :dossier_tasks, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid'

  has_many :dossier_jots, class_name: 'DossierJot',
                          primary_key: 'uuid', foreign_key: 'realty_dossier_uuid'

  has_many :price_estimates, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid'

  # # Dossier listings
  # has_many :dossier_listings, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid'
  # has_many :secondary_dossier_listings,
  #          -> { where(is_rental_listing: false) },
  #          primary_key: 'uuid',
  #          foreign_key: 'realty_dossier_uuid',
  #          class_name: 'DossierListing'
  # has_many :primary_dossier_listings,
  #          -> { where(is_rental_listing: true) },
  #          primary_key: 'uuid',
  #          foreign_key: 'realty_dossier_uuid',
  #          class_name: 'DossierListing'

  # has_many :info_sources, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid'
  # has_many :secondary_info_sources,
  #          -> { where(is_source_for_primary_property: false) },
  #          primary_key: 'uuid',
  #          foreign_key: 'realty_dossier_uuid',
  #          class_name: 'DossierInfoSourceForComparable'
  # has_many :primary_info_sources,
  #          -> { where(is_source_for_primary_property: true) },
  #          primary_key: 'uuid',
  #          foreign_key: 'realty_dossier_uuid',
  #          class_name: 'DossierInfoSourceForPrimaryProperty'

  has_many :dossier_assets_comparisons, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid',
                                        class_name: 'DossierAssetsComparison'

  has_many :dossier_assets, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid'
  # 17 may 2025 - add dossier_assets above as AI gen code in PortalDossierCreator expected to have it.

  has_many :secondary_dossier_assets,
           -> { where(is_primary_dossier_asset: false) },
           primary_key: 'uuid',
           foreign_key: 'realty_dossier_uuid',
           class_name: 'DossierAsset'
  has_many :secondary_realty_assets, through: :secondary_dossier_assets,
                                     source: :realty_asset
  has_many :secondary_dossier_asset_parts, through: :secondary_dossier_assets,
                                           source: :dossier_asset_parts

  # Will need to rethink below when assets start having more than one listing associated....
  has_many :secondary_sale_listings, through: :secondary_dossier_assets,
                                     source: :sale_listings

  has_one :primary_dossier_asset,
          -> { where(is_primary_dossier_asset: true) },
          primary_key: 'uuid',
          foreign_key: 'realty_dossier_uuid',
          class_name: 'DossierAsset'

  has_many :primary_asset_parts, through: :primary_dossier_asset,
                                 source: :dossier_asset_parts

  # TODO: - use above which replaces below
  # def dossier_primary_asset_parts
  #   primary_dossier_asset&.dossier_asset_parts
  # end

  # not sure below can be through has_many association
  def primary_sale_listing
    # 6 may 2025 - default_sale_listing below is a terrible idea!!
    # If a realty asset gets a new sale listing, that could suddenly
    # become the default_sale_listing....
    primary_dossier_asset&.default_sale_listing
  end

  # 6 may 2025 - longer term solution will be to only use above
  # and below to populate fields in the DossierAsset
  def primary_realty_asset
    primary_dossier_asset&.realty_asset
  end

  # dossier_item.primary_dossier_asset.dossier_asset_parts
  # has_many :dossier_asset_parts, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid'

  # LLM-related associations
  has_many :llm_interaction_associations, as: :associable, primary_key: 'uuid'

  # 13 May 2025 - below not currently used but I'd like to get it back:
  has_many :recent_sales_evaluations,
           as: :associable,
           primary_key: 'uuid',
           class_name: 'LiaToEvalRecentSales',
           foreign_key: 'associable_id'

  # # 9 apr 2025 - now that I use composit_photos below may well get deprecated
  # has_many :analysed_photos,
  #          as: :associable,
  #          primary_key: 'uuid',
  #          class_name: 'LiaToAnalysePhotos',
  #          foreign_key: 'associable_id'

  #  below  redundant
  # has_one :expanded_listing_details,
  #         as: :associable,
  #         primary_key: 'uuid',
  #         class_name: 'LiaToExpandSingleListing',
  #         foreign_key: 'associable_id'

  # Delegations
  delegate :count_toilets, :count_garages, :count_bedrooms, :area_unit, :plot_area,
           :constructed_area, :year_construction, :latitude, :longitude, :city,
           :region, :country, :street_name, :neighborhood, :formatted_constructed_area,
           :street_number, :postal_code, :street_address, :count_bathrooms,
           to: :primary_realty_asset, allow_nil: true
  delegate :price_sale_current_cents, to: :primary_sale_listing, allow_nil: true

  # Constants
  MIN_PRICE_FACTOR = 0.67  # Approximately 2/3 of original price
  MAX_PRICE_FACTOR = 1.5   # 50% above original price
  MIN_ROOMS = 1            # Minimum allowable rooms

  # Validations
  validates :agency_tenant_uuid, presence: true
  validates :uuid, presence: true, uniqueness: true

  # Instance Methods

  # Instance Methods
  # def analysed_photos_summary
  #   analysed_photos.map(&:analysed_photo_summary)
  # end

  def dossier_display_title
    if dossier_title.present?
      dossier_title
    elsif primary_sale_listing&.title.present?
      "Researching #{primary_sale_listing.title} - #{primary_sale_listing.price_sale_current}"
    else
      'Researching a property to buy'
    end
  end

  def dossier_main_photo
    # TODO: - get llm to set this
    return unless primary_sale_listing&.listing_photos&.kept.present?

    primary_sale_listing.listing_photos.kept.first.image_details
  end

  def dossier_postcode
    primary_realty_asset&.postal_code
  end

  def dossier_city
    primary_realty_asset&.city
  end

  def dossier_neigbourhood
    {}
    # TOFIX: This is a placeholder method
    # but further down the line
    # GeoCluster.last
  end
  # primary_dossier_asset effectively replaces below
  # def main_eval_for_primary_property
  #   #  I have two decisions to make regarding main eval.
  #   # Number one which of the different sources for the primary property should be the main source.
  #   #  Number two: which of the Various interactions with an LLM for that particular source should I use?
  #   #  Most often I will only have one LLM interaction for a source so that is less of an issue.
  #   primary_info_sources.where(associated_listing_uuid: primary_sale_listing&.uuid).last
  # end

  def similar_property_search_params
    bedrooms_base = count_bedrooms.to_i
    bathrooms_base = count_bathrooms.to_i
    price_in_pounds = (price_sale_current_cents.to_f / 100.0) || 0.0

    {
      search_postcode: postal_code,
      search_bedrooms_min: [MIN_ROOMS, bedrooms_base - 1].max,
      search_bedrooms_max: bedrooms_base + 1,
      search_bathrooms_min: [MIN_ROOMS, bathrooms_base - 1].max,
      search_bathrooms_max: bathrooms_base + 1,
      search_price_min: (price_in_pounds * MIN_PRICE_FACTOR).round(2),
      search_price_max: (price_in_pounds * MAX_PRICE_FACTOR).round(2)
    }
  end

  def add_dossier_search_to_use(realty_search_query)
    ds = dossier_searches.find_or_create_by!(
      realty_search_query: realty_search_query,
      agency_tenant_uuid: realty_search_query.agency_tenant_uuid
    )
    dossier_searches.update_all(is_most_useful: false)
    ds.update!(is_most_useful: true)
    ds
  end

  def dossier_search_to_use
    dossier_searches.where(is_most_useful: true).last # TODO: Add selection logic
  end

  def recent_sales_evaluation_to_use
    nil
    # recent_sales_evaluations.first
  end

  def relevant_sold_transactions
    return [] unless primary_realty_asset&.latitude && primary_realty_asset.longitude

    dossier_lat_lng = [primary_realty_asset.latitude, primary_realty_asset.longitude]
    SoldTransactionEpc.near(dossier_lat_lng, 25)
                      .within_days_difference(-1000, 1000)
                      .with_habitable_rooms
  end

  def most_comparable_summary_listings
    # TODO: add logic to select the most relevant listings
    dossier_search_to_use&.realty_search_query&.summary_listings
  end

  # # 1st Step: Analyze Listing Photos
  # def analyze_primary_photos(max_photos_to_analyze = 2)
  #   # 14 apr - analyze_primary_photos no longer the best...
  #   primary_dossier_asset.analyse_photos(max_photos_to_analyze)
  # end
  # 23 apr 2025 -  analyze_primary_photos now replaced by
  # primary_dossier_asset.analyse_composite_photo

  # # 2nd Step: Evaluate Listing Content with benefit of photos
  # def evaluate_with_photos_content
  #   # AiGen::DossierEvalService.new.evaluate_with_photos(self)
  #   raise 'No processed photos to evaluate against' if dossier.analysed_photos_summary.blank?

  #   Ai::PropertyInsights::LlmListingEvaluator.new(client_to_use: 'gemini')
  #                                            .evaluate(self)
  # end

  # 3rd Step: Create and Run Default Search
  def create_and_run_default_search
    rsq = RealtySearchQueryFromOtm.find_or_create_unique_query(similar_property_search_params)
    add_dossier_search_to_use(rsq)
    rsq.search_and_save_listings!.tap do |summary|
      Rails.logger.debug { "Summary sale listings: #{summary.inspect}" }
    end
  rescue StandardError => e
    Rails.logger.error("Failed to create and run search: #{e.message}")
    raise
  end

  # 4th Step: populate comparable listings
  # might want to run something before this to figure
  # out which search results are best to use as comparables
  # Scraping Method (Delegated to Service)
  # torename: populate_comparables_from_best_summary_listings
  def populate_dossier_listings_from_search
    raise 'No summary listings available' if most_comparable_summary_listings.blank?

    RealtyScrapers::DossierScrapingService.new.populate_listings(
      self, most_comparable_summary_listings
    )
  end

  # # possible 5th Step: Evaluate against Comparable Listings
  # # TO deprecate:
  # def evaluate_against_comparable_listings
  #   # In March I wanted to replace dossier_listings below with secondary_info_sources
  #   # 1 Apr 2025 update -  both dossier_listings and secondary_info_sources
  #   # have been replaced by secondary_dossier_assets
  #   # AiGen::DossierEvalService.new.evaluate_comparables(self)
  #   # 7 apr 2025 - all above does is below so might as well invoke it directly:

  #   raise 'No comparable listings to evaluate against' if secondary_dossier_assets.blank?

  #   # 1 apr 2025 - have decided to replace below with above
  #   # raise 'No comparable listings to evaluate against' if dossier.secondary_info_sources.blank?

  #   Ai::PropertyInsights::LlmDossierComparableListingsEvaluator.new(client_to_use: 'gemini')
  #                                                              .evaluate(self)
  # rescue StandardError => e
  #   Rails.logger.error("Comparable listings evaluation failed for dossier #{id}: #{e.message}")
  #   raise
  # end
  # # Above created LiaToEvalComparableListings records
  # # and populate .comparable_listings_evaluation_to_use
  # # Currently .comparable_listings_evaluation_to_use is not great

  # better 5th Step: Create detailed comparison
  def create_detailed_comparison(secondary_asset_uuid)
    # secondary_asset_uuid ||= '38225442-9c29-4e14-a6b3-86dfb41d99d8' # Replace with an actual UUID from your data

    side_by_side_analyser = Ai::PropertyInsights::SideBySideSectionsAnalyser.new
    secondary_asset = secondary_dossier_assets.find_by_uuid(secondary_asset_uuid)
    secondary_asset_parts_count = secondary_asset&.dossier_asset_parts&.kept&.count
    # 8 apr 2025 - decided analyse_composite_photo is the best way to populate dossier_asset_parts
    # so likely hasn't been run if error thrown here
    raise 'this secondary_asset has less than 2 asset_parts' unless secondary_asset_parts_count > 2

    # It is after below that the DossierAssetsComparison is created
    # and the comparable becomes available on the site
    comparison = side_by_side_analyser.fully_process_comparison_for_dossier(self, secondary_asset)
    comparison
  end

  #   # added below 2 for the "/superwiser/dossiers" view where I want to be able to link
  #   # to screenshot urls for each listing
  # but later decided to retrieve through dossier_assets
  # def secondary_listing_uuids
  #   # Will need to rethink this when assets start having more than one listing associated....
  #   secondary_sale_listings.pluck :uuid
  # end

  def add_asset_from_onthemarket_url(source_url)
    creator = Creators::DossierAssetCreator.new
    asset = creator.create_asset_for_dossier(
      uuid,
      source_url,
      'onthemarket',
      is_primary: false
    )
    asset
  end

  def add_asset_from_portal_url(source_url, portal_name)
    creator = Creators::DossierAssetCreator.new
    asset = creator.create_asset_for_dossier(
      uuid,
      source_url,
      portal_name,
      is_primary: false
    )
    asset
  end

  # may 2025 - below so if initial dossier_from_onthemarket_url etc
  # misses out on some detail of the main listing I can try again
  def update_from_source_url(source_url, portal_name)
    Creators::PortalDossierCreator.new.update_from_url(source_url, portal_name)
  end

  def price_guess_listings_from_dossier
    lfpg = []
    dossier_assets&.presence&.each do |dossier_asset|
      lfpg.push dossier_asset.default_sale_listing if dossier_asset.default_sale_listing.visible
    end
    lfpg
  end

  # June 2025 - added below for admin view - to be used..
  def price_guess_listings_from_dossier_for_admin
    lfpg = []
    dossier_assets&.presence&.each do |dossier_asset|
      lfpg.push dossier_asset.default_sale_listing
    end
    lfpg
  end

  # Class Methods
  class << self
    # defines methods like dossier_from_onthemarket_url
    %w[purplebricks onthemarket zoopla rightmove idealista zillow].each do |portal|
      define_method("dossier_from_#{portal}_url") do |url|
        Creators::PortalDossierCreator.new.create_from_url(url, portal)
      end
    end
  end
end
