# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromJitty < ScrapeItem
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  default_scope { scrape_is_jitty }

  def self.find_or_create_for_h2c_jitty(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point
    )
    scrape_item.update!(scrape_is_jitty: true)
    ScrapeItemFromJitty.find(scrape_item.id)
  end

  # def sale_listing_from_scrape_item
  #   standardised_listing_hash = property_hash_from_scrape_item

  #   request_host = 'guise_listing_server'
  #   creator = Creators::Full::FullListingAndAssetCreator.new
  #   sale_listing = creator.create_from_standardised_hash(
  #     standardised_listing_hash,
  #     request_host
  #   )
  #   sale_listing
  # end

  # 1 mar 2025: below def is to do with scraping a single item
  # from a single listing page!
  def property_hash_from_scrape_item
    return unless full_content_before_js

    jitty_html = full_content_before_js

    # listing_file_path = File.join(
    #   Rails.root, 'db', 'external_sites/jitty-com/jitty.html'
    # )
    # # Ensure the directory exists
    # FileUtils.mkdir_p(File.dirname(listing_file_path))
    # # Write the file
    # File.write(listing_file_path, jitty_html)

    puts top_level_url
    @doc = Nokogiri::HTML(jitty_html)
    property_data = extract_data

    property_data.stringify_keys!
    # property_data.compact # Remove nil values
  end

  def extract_data
    standardized_data = {
      listing_data: extract_listing_data,
      asset_data: extract_asset_data,
      listing_image_urls: extract_image_urls,
      postal_code: extract_postal_code,
      import_url: scrapable_url,
      alternative_listing_url: extract_import_url
    }

    standardized_data
  end

  private

  def extract_listing_data
    data = {
      'archived' => false,
      'commission_cents' => 0,
      'commission_currency' => 'GBP',
      'currency' => 'GBP',
      'design_style' => nil,
      'details_of_rooms' => {},
      'discarded_at' => nil,
      'extra_sale_details' => {},
      'furnished' => extract_furnished,
      'hide_map' => false,
      'highlighted' => false,
      'host_on_create' => 'unknown_host',
      'is_ai_generated_listing' => false,
      'listing_pages_count' => 0,
      'listing_slug' => extract_listing_id,
      'listing_tags' => [],
      'main_video_url' => extract_video_url,
      'obscure_map' => false,
      'page_section_listings_count' => 0,
      'position_in_list' => nil,
      'price_sale_current_cents' => extract_price_cents,
      'price_sale_current_currency' => 'GBP',
      'price_sale_original_cents' => extract_price_cents,
      'price_sale_original_currency' => 'GBP',
      'property_board_items_count' => 0,
      'publish_from' => nil,
      'publish_till' => nil,
      'reference' => extract_listing_id,
      'related_urls' => {},
      'reserved' => false,
      'sale_listing_features' => extract_features,
      'sale_listing_flags' => 0,
      'sale_listing_gen_prompt' => nil,
      'service_charge_yearly_cents' => extract_service_charge_cents,
      'service_charge_yearly_currency' => 'GBP',
      'site_visitor_token' => nil,
      'sl_photos_count' => count_photos,
      'visible' => true
    }

    data
  end

  def extract_asset_data
    data = {
      'related_urls_from_otm' => extract_related_urls,
      'categories' => extract_categories,
      'city' => extract_city,
      'city_search_key' => extract_city&.downcase,
      'constructed_area' => 0.0,
      'count_bathrooms' => extract_bathrooms,
      'count_bedrooms' => extract_bedrooms,
      'count_garages' => extract_garages,
      'count_toilets' => 0,
      'country' => 'England',
      'description' => extract_description,
      'details' => {},
      'discarded_at' => nil,
      'energy_performance' => nil,
      'energy_rating' => extract_energy_rating,
      'floor' => nil,
      'has_rental_listings' => false,
      'has_sale_listings' => true,
      'has_sold_transactions' => false,
      'host_on_create' => 'unknown_host',
      'is_ai_generated_realty_asset' => false,
      'latitude' => extract_latitude,
      'longitude' => extract_longitude,
      'neighborhood' => nil,
      'neighborhood_search_key' => '',
      'plot_area' => 0.0,
      'postal_code' => nil,
      'prop_state_key' => 'new',
      'prop_type_key' => extract_property_type,
      'province' => nil,
      'ra_photos_count' => count_photos,
      'realty_asset_flags' => 0,
      'realty_asset_tags' => extract_tags,
      'reference' => extract_listing_id,
      'region' => nil,
      'rental_listings_count' => 0,
      'sale_listings_count' => 1,
      'site_visitor_token' => nil,
      'sold_transactions_count' => 0,
      'street_address' => extract_address,
      'street_number' => nil,
      'title' => extract_title,
      'year_construction' => 0
    }

    data
  end

  def extract_listing_id
    # Extract from URL or a visible ID
    url = extract_import_url
    return ::Regexp.last_match(1) if url =~ %r{/(\d+)(?:/|$)}

    # Fallback to anything in the page that looks like a reference
    @doc.css('div:contains("Reference")').each do |el|
      return ::Regexp.last_match(1) if el.text =~ /Reference[:\s]+(\w+)/i
    end

    # Get from share URL if available
    share_url = @doc.css('input[value*="properties/"]').first&.attr('value')
    return ::Regexp.last_match(1) if share_url && share_url =~ %r{properties/([^/?]+)}

    'unknown'
  end

  def extract_price_cents
    price_text = @doc.css('div.flex.flex-row.gap-2.-mt-2.text-lg.font-medium').text.strip
    if price_text =~ /£([\d,]+)/
      price = ::Regexp.last_match(1).gsub(',', '').to_i
      return price * 100
    end
    0
  end

  def extract_features
    features = {}

    feature_elements =
      @doc.css('div.flex.flex-row.items-center.w-fit.content-center.font-medium.text-tertiary-700.bg-tertiary-100')

    feature_elements.each_with_index do |element, index|
      feature_text = element.text.strip
      features[index + 1] = feature_text
    end

    # If no features are found using the above selector, try another approach
    if features.empty?
      feature_items = @doc.css('section:contains("Features") div.text-nowrap')
      feature_items.each_with_index do |item, index|
        features[index + 1] = item.text.strip
      end
    end

    # Add standard features if available
    tenure_text = extract_tenure
    features[1] = "Tenure: #{tenure_text}" if tenure_text && features[1].nil?

    features
  end

  def extract_categories
    features = extract_features
    categories = []

    features.each do |id, name|
      categories << {
        'id' => id,
        'name' => name
      }
    end

    categories
  end

  def extract_service_charge_cents
    service_charge_text = @doc.css('div:contains("Service Charge")').last

    if service_charge_text
      parent_div = service_charge_text.parent.parent
      amount_div = parent_div.css('div.justify-self-end.whitespace-nowrap').first

      if amount_div && amount_div.text =~ /£(\d+)/
        return ::Regexp.last_match(1).to_i * 100 * 12 # Converting monthly to yearly
      end
    end

    0
  end

  def extract_furnished
    @doc.css('div:contains("Furnished")').any? do |el|
      el.text.downcase.include?('furnished')
    end
  end

  def extract_video_url
    video_element = @doc.css('a[href*="youtu"]').first
    video_element&.attr('href') || nil
  end

  def count_photos
    @doc.css('a[href*="/gallery"] img').count
  end

  def extract_bedrooms
    bedroom_div = @doc.css('div:contains("Bedrooms")').last

    if bedroom_div
      bedroom_text = bedroom_div.parent.parent.css('div').last.text.strip
      if bedroom_text =~ /(\d+)/
        return ::Regexp.last_match(1).to_i
      elsif bedroom_text.downcase.include?('studio')
        return 0
      end
    end

    0
  end

  def extract_bathrooms
    bathroom_div = @doc.css('div:contains("Bathrooms")').last

    if bathroom_div
      bathroom_text = bathroom_div.parent.parent.css('div').last.text.strip
      return ::Regexp.last_match(1).to_f if bathroom_text =~ /(\d+)/
    end

    0
  end

  def extract_garages
    garage_element = @doc.css('div.text-nowrap:contains("Garage")').first
    garage_element ? 1 : 0
  end

  def extract_address
    address = @doc.css('h1.w-full.text-2xl.font-medium').text.strip
    address
  end

  def extract_city
    address = extract_address
    return ::Regexp.last_match(1) if address =~ /,\s*([^,]+)$/

    'Unknown'
  end

  def extract_title
    property_type = extract_property_type_text
    bedrooms = extract_bedrooms

    if bedrooms > 0
      "#{bedrooms} bedroom #{property_type}"
    else
      "Studio #{property_type}"
    end
  end

  def extract_property_type
    type_text = extract_property_type_text.downcase

    case type_text
    when /flat/
      'flat'
    when /detached/
      'detached-house'
    when /semi-detached/
      'semi-detached-house'
    when /terraced/
      'terraced-house'
    when /bungalow/
      'bungalow'
    when /apartment/
      'apartment'
    else
      'flat' # Default
    end
  end

  def extract_property_type_text
    type_div = @doc.css('div:contains("Type")').last

    if type_div
      type_div.parent.parent.css('div').last.text.strip
    else
      # Try to find from meta tags
      meta_description = @doc.css('meta[property="og:description"]').first&.attr('content')
      if meta_description && meta_description =~
                             /(Flat|House|Bungalow|Apartment|Detached|Semi-Detached|Terraced)/i
        return ::Regexp.last_match(1)
      end

      # Check page title
      title = @doc.css('title').text
      return ::Regexp.last_match(1) if title =~ /(Flat|House|Bungalow|Apartment|Detached|Semi-Detached|Terraced)/i

      'Flat' # Default
    end
  end

  def extract_tenure
    tenure_div = @doc.css('div:contains("Tenure")').last

    if tenure_div
      tenure_text = tenure_div.parent.parent.parent.css('div').last.text.strip
      return tenure_text.gsub(/^Tenure:\s*/, '')
    end

    # Try to find it in features
    @doc.css('div.text-nowrap').each do |element|
      return ::Regexp.last_match(1) if element.text =~ /Tenure:\s*(.+)/i
    end

    'Leasehold' # Default
  end

  def extract_description
    desc_section = @doc.css('section.flex.flex-col.gap-5.pt-6').find do |section|
      section.css('h3').text.include?('Description')
    end

    if desc_section
      desc_section.css('div').text.strip
    else
      # Look for a large block of text that might be the description
      paragraphs = @doc.css('p, div').select do |el|
        el.text.length > 200 && !el.text.include?('script') && !el.text.include?('function')
      end

      paragraphs.first&.text&.strip || ''
    end
  end

  def extract_latitude
    map_element = @doc.css('div[data-controller="map"]').first
    map_element&.attr('data-map-lat-value')&.to_f || 0.0
  end

  def extract_longitude
    map_element = @doc.css('div[data-controller="map"]').first
    map_element&.attr('data-map-lon-value')&.to_f || 0.0
  end

  def extract_tags
    tags = []

    # Virtual tour
    tags << 'Virtual tour' if @doc.css('a[href*="youtu"]').any? || @doc.css('iframe[src*="youtu"]').any?

    tags
  end

  def extract_image_urls
    image_elements = @doc.css('a[href*="/gallery"] img')
    urls = image_elements.map do |img|
      img['src']
    end.uniq

    urls
  end

  def extract_postal_code
    # Try to find postal code in the page
    text = @doc.text
    return ::Regexp.last_match(1) if text =~ /([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})/

    # Check if it's in a specific element
    @doc.css('div:contains("Postal Code")').each do |el|
      return ::Regexp.last_match(1) if el.text =~ /([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})/
    end

    # Get it from og:image if it contains postcode
    og_image = @doc.css('meta[property="og:image"]').first&.attr('content')
    return ::Regexp.last_match(1) if og_image && og_image =~ /([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})/

    nil
  end

  def extract_energy_rating
    energy_div = @doc.css('div:contains("Energy Rating")').last

    if energy_div
      rating_text = energy_div.parent.parent.css('div').last.text.strip
      return ::Regexp.last_match(1) if rating_text =~ /([A-G])/
    end

    nil
  end

  def extract_related_urls
    {
      related_urls_from_otm: {
        property_documents: extract_property_documents,
        other_otm_urls: extract_other_urls
      }
    }
  end

  def extract_property_documents
    documents = []

    # Look for links to PDF documents
    @doc.css('a[href*=".pdf"]').each_with_index do |link, index|
      documents << {
        'positionCaption' => "Document #{index + 1}",
        'text' => link.text.strip.presence || 'Document',
        'brochure' => true,
        'url' => link['href'],
        'isPdf' => true
      }
    end

    documents
  end

  def extract_other_urls
    urls = []

    # Find relevant links
    @doc.css('a[href*="for-sale"]').each do |link|
      next if link['href'].include?('mailto') || link.text.strip.empty?

      urls << {
        'text' => link.text.strip,
        'url' => link['href']
      }
    end

    urls.uniq { |url| url['url'] }
  end

  def extract_import_url
    # Check for canonical URL
    canonical = @doc.css('link[rel="canonical"]').first&.attr('href')
    return canonical if canonical

    # Try to find the original listing URL
    listing_url = @doc.css('a:contains("View listing")').first&.attr('href')
    return listing_url if listing_url

    # Fallback to share URL
    share_url = @doc.css('input[value*="properties/"]').first&.attr('value')
    return share_url if share_url

    nil
  end
end
