# == Schema Information
#
# Table name: dossier_info_sources
#
#  id                                         :bigint           not null, primary key
#  agency_tenant_uuid                         :uuid
#  associated_listing_uuid                    :uuid
#  discarded_at                               :datetime
#  dossier_asset_uuid                         :uuid
#  dossier_info_source_aasm_state             :string
#  dossier_info_source_details                :jsonb
#  dossier_info_source_flags                  :integer          default(0), not null
#  dossier_info_source_rating                 :integer          default(0), not null
#  is_for_most_comparable_property_to_primary :boolean          default(FALSE)
#  is_source_for_primary_property             :boolean          default(FALSE)
#  llm_interaction_uuid                       :uuid
#  property_is_for_rent                       :boolean          default(FALSE)
#  property_is_for_sale                       :boolean          default(TRUE)
#  realty_asset_photo_uuid                    :uuid
#  realty_dossier_uuid                        :uuid
#  realty_search_query_uuid                   :uuid
#  source_is_epc                              :boolean          default(FALSE)
#  source_is_listing                          :boolean          default(TRUE)
#  source_is_photo                            :boolean          default(FALSE)
#  source_is_sold_transaction                 :boolean          default(FALSE)
#  uuid                                       :uuid
#  created_at                                 :datetime         not null
#  updated_at                                 :datetime         not null
#
# Indexes
#
#  index_dossier_info_sources_on_agency_tenant_uuid          (agency_tenant_uuid)
#  index_dossier_info_sources_on_associated_listing_uuid     (associated_listing_uuid)
#  index_dossier_info_sources_on_discarded_at                (discarded_at)
#  index_dossier_info_sources_on_dossier_asset_uuid          (dossier_asset_uuid)
#  index_dossier_info_sources_on_dossier_info_source_flags   (dossier_info_source_flags)
#  index_dossier_info_sources_on_dossier_info_source_rating  (dossier_info_source_rating)
#  index_dossier_info_sources_on_realty_asset_photo_uuid     (realty_asset_photo_uuid)
#  index_dossier_info_sources_on_realty_dossier_uuid         (realty_dossier_uuid)
#  index_dossier_info_sources_on_uuid                        (uuid)
#
class DossierInfoSource < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # belongs_to :sold_transaction, class_name: 'SoldTransaction', foreign_key: 'sold_transaction_uuid', primary_key: :uuid, optional: false
  # has_one :postcode_area, through: :sold_transaction # , source: :postcode_area

  belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  belongs_to :realty_dossier, class_name: 'RealtyDossier', foreign_key: 'realty_dossier_uuid', primary_key: :uuid, optional: true
  belongs_to :realty_search_query, primary_key: 'uuid', foreign_key: 'realty_search_query_uuid', optional: true
  belongs_to :sale_listing, class_name: 'SaleListing', primary_key: 'uuid',
                            foreign_key: 'associated_listing_uuid', optional: true

  belongs_to :dossier_asset, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid', optional: true
  counter_culture :dossier_asset, column_name: 'info_sources_count'
  # TODO: dossier_asset_parts_count

  validates :realty_dossier_uuid, presence: true
  validates :dossier_asset_uuid, presence: true

  # Ensure the combination of realty_dossier_uuid and associated_listing_uuid is unique
  # validates :realty_dossier_uuid, uniqueness: { scope: :associated_listing_uuid, message: 'and realty_dossier combination must be unique' }
  # validates :is_most_comparable, uniqueness: { scope: :realty_dossier_uuid }, if: -> { is_most_comparable }

  include FlagShihTzu
  has_flags 1 => :info_source_is_composite_photo,
            2 => :info_source_is_composite_image_a,
            :column => 'dossier_info_source_flags'
end
