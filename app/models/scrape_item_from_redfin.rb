# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
require 'json'

class ScrapeItemFromRedfin < ScrapeItem
  default_scope { scrape_is_redfin }

  def self.find_or_create_for_h2c_redfin(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: false
    )
    scrape_item.update!(
      scrape_is_redfin: true,
      is_realty_search_scrape: false,
      content_is_html: true
    )
    ScrapeItemFromRedfin.find(scrape_item.id)
  end

  # Extract standardized listing + asset data and all image URLs from a Redfin listing page
  def property_hash_from_scrape_item
    unless full_content_before_js && full_content_before_js.length > 500
      puts "full_content_before_js: #{full_content_before_js&.length || 0} characters"
      puts "Content preview: #{full_content_before_js&.first(200)}"
      raise 'full_content_before_js unavailable or too short'
    end

    html = full_content_before_js
    doc = Nokogiri::HTML(html)

    listing_data = map_html_to_listing_schema(doc)
    asset_data = map_html_to_asset_schema(doc)
    image_urls = extract_images(doc)

    property_data = {
      listing_data: listing_data,
      asset_data: asset_data,
      listing_image_urls: image_urls
    }

    property_data.stringify_keys!
  end

  private

  def extract_price_from_text(text)
    return 0 unless text
    text.to_s.gsub(/[^0-9]/, '').to_i
  end

  def map_html_to_listing_schema(doc)
    # Prefer Twitter meta tags as seen in sample HTML
    price_text =
      # Most reliable on sample pages
      doc.at('meta[name="twitter:text:price"]')&.[]('content') ||
      # Other common meta price markers
      doc.at('meta[property="product:price:amount"]')&.[]('content') ||
      doc.at('meta[itemprop="price"]')&.[]('content') ||
      doc.at('meta[name="price"]')&.[]('content') ||
      # Visible DOM fallbacks
      doc.at('[data-rf-test-id="abp-price"]')&.text ||
  doc.at('[data-testid="price"]')&.text ||
  doc.at('.price, [class*="price"]')&.text

    price_int = extract_price_from_text(price_text)

    {
      'title' => doc.at('title')&.text,
      'description' => doc.at('meta[name="description"]')&.[]('content'),
      'price_sale_current_cents' => (price_int.to_i * 100),
      'price_sale_current_currency' => 'USD',
      'price_sale_original_cents' => (price_int.to_i * 100),
      'price_sale_original_currency' => 'USD',
      'reference' => doc.at('meta[property="og:url"]')&.[]('content') || doc.at('link[rel="canonical"]')&.[]('href'),
      'visible' => true,
      'sale_listing_flags' => 0,
      'currency' => 'USD'
    }
  end

  def map_html_to_asset_schema(doc)
    address_line = doc.at('meta[name="twitter:text:street_address"]')&.[]('content')
    city = doc.at('meta[name="twitter:text:city"]')&.[]('content')
    state_code = doc.at('meta[name="twitter:text:state_code"]')&.[]('content')
    zip = doc.at('meta[name="twitter:text:zip"]')&.[]('content')
    desc = doc.at('meta[name="description"]')&.[]('content')

    bedrooms = doc.at('meta[name="twitter:text:beds"]')&.[]('content')&.to_s&.gsub(/[^0-9]/, '')&.to_i
    bathrooms = doc.at('meta[name="twitter:text:baths"]')&.[]('content')&.to_s&.gsub(/[^0-9]/, '')&.to_i
    area = doc.at('meta[name="twitter:text:sqft"]')&.[]('content')&.to_s&.gsub(/[^0-9]/, '')&.to_i

    {
      'title' => doc.at('title')&.text,
      'description' => desc,
      'count_bedrooms' => bedrooms || 0,
      'count_bathrooms' => bathrooms || 0,
      'constructed_area' => area || 0,
      'plot_area' => 0,
      'year_construction' => nil,
      'prop_type_key' => 'unknown',
      'city' => city,
      'province' => state_code,
      'postal_code' => zip,
      'street_address' => address_line,
      'country' => 'US',
      'realty_asset_flags' => 0,
      'has_sale_listings' => true,
      'sale_listings_count' => 1
    }
  end

  def extract_images(doc)
    urls = []

    # 1) Twitter meta images (photo0..N, plus generic twitter:image variants)
    urls += doc.css('meta[name^="twitter:image"]')
               .map { |m| m['content'] }
               .compact

    # 2) OpenGraph images
    urls += [
      doc.at('meta[property="og:image"]')&.[]('content'),
      doc.at('meta[property="og:image:secure_url"]')&.[]('content')
    ].compact

    # 3) Preload big photos
    urls += doc.css('link[rel="preload"][as="image"]')
               .map { |ln| ln['href'] }
               .compact

    # 4) <img> sources (src, data-src and common lazy attrs)
    img_attrs = %w[src data-src data-original data-lazy data-lazy-src]
    urls += doc.css('img').flat_map do |img|
      img_attrs.map { |a| img[a] }
    end.compact

    # 5) srcset on <img> and <source> inside <picture>
    srcset_attrs = %w[srcset data-srcset]
    urls += (doc.css('img, source').flat_map do |node|
      srcset_attrs.map { |a| node[a] }
    end.compact.flat_map do |srcset|
      # srcset: "url1 1x, url2 2x" or "url 800w, url2 1600w"
      srcset.split(',').map { |part| part.strip.split(' ').first }
    end)

    # 6) JSON-LD images (application/ld+json)
    urls += extract_images_from_json_ld(doc)

    # 7) Next.js payload (__NEXT_DATA__) – try to discover any image-like URLs
    urls += extract_images_from_next_data(doc)

    # Keep only likely image URLs and de-duplicate while preserving order
    image_like = urls.compact.select { |u| u =~ /\.(jpg|jpeg|png|webp)(\?|$)/i }
    uniq_preserve_order(image_like)
  end

  def extract_images_from_json_ld(doc)
    doc.css('script[type="application/ld+json"]').flat_map do |script|
      begin
        payload = JSON.parse(script.text) rescue nil
        next [] unless payload
        objs = payload.is_a?(Array) ? payload : [payload]
        objs.flat_map do |obj|
          imgs = obj['image'] || obj.dig('offers', 'image')
          case imgs
          when String
            [imgs]
          when Array
            imgs.map { |it| it.is_a?(Hash) ? it['url'] : it }
          when Hash
            [imgs['url']]
          else
            []
          end
        end
      rescue StandardError
        []
      end
    end.compact
  end

  def extract_images_from_next_data(doc)
    script = doc.at('script#__NEXT_DATA__')
    return [] unless script
    begin
      data = JSON.parse(script.text) rescue nil
      return [] unless data
      collect_image_urls_from_object(data)
    rescue StandardError
      []
    end
  end

  def collect_image_urls_from_object(obj)
    urls = []
    case obj
    when Hash
  obj.each do |_k, v|
        if v.is_a?(String) && v =~ /https?:\/\/[^\s\"]+\.(jpg|jpeg|png|webp)(\?|$)/i
          urls << v
        else
          urls.concat(collect_image_urls_from_object(v))
        end
      end
    when Array
      obj.each { |v| urls.concat(collect_image_urls_from_object(v)) }
    end
    urls
  end

  def uniq_preserve_order(arr)
    seen = {}
    arr.select { |e| !seen.key?(e) && (seen[e] = true) }
  end
end

