# == Schema Information
#
# Table name: postcode_area_clusters
#
#  id                                      :bigint           not null, primary key
#  agency_tenant_uuid                      :uuid
#  bbox_max_latitude                       :decimal(10, 6)
#  bbox_max_longitude                      :decimal(10, 6)
#  bbox_min_latitude                       :decimal(10, 6)
#  bbox_min_longitude                      :decimal(10, 6)
#  center_latitude                         :decimal(10, 6)
#  center_longitude                        :decimal(10, 6)
#  cluster_average_property_price_cents    :bigint           default(0), not null
#  cluster_average_property_price_currency :string           default("EUR"), not null
#  cluster_city                            :string
#  cluster_country                         :string
#  cluster_description                     :text
#  cluster_geojsons                        :jsonb
#  cluster_name                            :string           not null
#  cluster_region                          :string
#  cluster_slug                            :string           not null
#  discarded_at                            :datetime
#  extra_cluster_details                   :jsonb
#  number_of_postcode_areas                :integer          default(0)
#  postcode_area_uuids                     :jsonb
#  total_population                        :integer          default(0)
#  translations                            :jsonb
#  uuid                                    :uuid
#  created_at                              :datetime         not null
#  updated_at                              :datetime         not null
#
# Indexes
#
#  index_postcode_area_clusters_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_postcode_area_clusters_on_cluster_name        (cluster_name) UNIQUE
#  index_postcode_area_clusters_on_cluster_slug        (cluster_slug) UNIQUE
#  index_postcode_area_clusters_on_discarded_at        (discarded_at)
#  index_postcode_area_clusters_on_uuid                (uuid)
#
class PostcodeAreaCluster < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # # This assumes that 'postcode_area_uuids' contains UUIDs of PostcodeArea records
  # has_many :postcode_areas, lambda { |cluster|
  #   where('uuid = ANY(ARRAY[:uuids]::uuid[])', uuids: cluster.postcode_area_uuids)
  # }, class_name: 'PostcodeArea', primary_key: 'uuid', foreign_key: 'uuid'
  # above was suggested by AI but does not work

  # Custom method to retrieve related postcode areas using postcode_area_uuids
  def filtered_postcode_areas
    PostcodeArea.where('uuid = ANY(ARRAY[:uuids]::uuid[])', uuids: postcode_area_uuids)
  end

  # Alternatively, if you want to use the primary_cluster_uuid and secondary_cluster_uuid:
  has_many :primary_postcode_areas, class_name: 'PostcodeArea', foreign_key: :primary_cluster_uuid, primary_key: :uuid
  has_many :secondary_postcode_areas, class_name: 'PostcodeArea', foreign_key: :secondary_cluster_uuid, primary_key: :uuid

  # If you decide to use has_and_belongs_to_many for a join table approach:
  # has_and_belongs_to_many :postcode_areas, join_table: :postcode_area_clusters_postcode_areas,
  #                                         foreign_key: :postcode_area_cluster_uuid,
  #                                         association_foreign_key: :postcode_area_uuid

  def self.find_or_create_from_postcode_areas(postcode_areas)
    # Determine a unique attribute or combination for clustering, e.g., geographic bounds
    bbox = calculate_bbox(postcode_areas)
    # Need to handle case where there are no valid values
    # Attempt to find an existing cluster matching these bounds
    cluster = find_or_initialize_by(
      bbox_min_latitude: bbox[:min_lat],
      bbox_max_latitude: bbox[:max_lat],
      bbox_min_longitude: bbox[:min_lon],
      bbox_max_longitude: bbox[:max_lon]
    )

    if cluster.new_record?
      # New cluster, so we set up basic attributes
      cluster.cluster_name = generate_cluster_name(postcode_areas)
      cluster.cluster_slug = generate_cluster_slug(postcode_areas)

      cluster.center_latitude, cluster.center_longitude = calculate_center(postcode_areas)
      cluster.number_of_postcode_areas = postcode_areas.count
      # Add any other initial setup for a new cluster

      # Link the postcode areas to this new cluster
      cluster.postcode_area_uuids = postcode_areas.map(&:uuid)
      cluster.save!
      # elsif cluster.postcode_area_uuids.count != postcode_areas.map(&:uuid).count
      #   # Update existing cluster if necessary
      #   # Note: This updates only if the number of areas has changed or bounds have shifted
      #   cluster.update!(
      #     number_of_postcode_areas: postcode_areas.count,
      #     postcode_area_uuids: postcode_areas.map(&:uuid)
      #   )
      #   # Here you might want to recalculate center and other attributes if they've changed
    end

    postcode_areas.each do |postcode_area|
      postcode_area.update!(primary_cluster_uuid: cluster.uuid)
    end
    cluster
  end

  def self.calculate_bbox(postcode_areas)
    lat_values = postcode_areas.map(&:bbox_min_latitude).compact
    lon_values = postcode_areas.map(&:bbox_min_longitude).compact

    min_lat = lat_values.empty? ? nil : lat_values.min
    max_lat = postcode_areas.map(&:bbox_max_latitude).compact.max
    min_lon = lon_values.empty? ? nil : lon_values.min
    max_lon = postcode_areas.map(&:bbox_max_longitude).compact.max
    # If any of these are nil, you might want to set a default or handle it differently
    { min_lat: min_lat || 0, max_lat: max_lat || 0, min_lon: min_lon || 0, max_lon: max_lon || 0 }
  end

  # def self.calculate_bbox(postcode_areas)
  #   min_lat = postcode_areas.map(&:bbox_min_latitude).min
  #   max_lat = postcode_areas.map(&:bbox_max_latitude).max
  #   min_lon = postcode_areas.map(&:bbox_min_longitude).min
  #   max_lon = postcode_areas.map(&:bbox_max_longitude).max

  #   { min_lat:, max_lat:, min_lon:, max_lon: }
  # end

  def self.generate_cluster_name(postcode_areas)
    # Example: Use the first couple of postcode areas to name the cluster
    "Cluster of #{postcode_areas.first(2).map(&:postal_code).join(' and ')}"
  end

  def self.generate_cluster_slug(postcode_areas)
    # Example: Use the postal codes to create a unique slug
    postcode_areas.map(&:postal_code).join('-').downcase.gsub(/[^a-z0-9-]/, '')
  end

  def self.calculate_center(postcode_areas)
    # Simple average for center, might need something more sophisticated for real-world use

    # Filter out nil values, then calculate the average
    lat_values = postcode_areas.map(&:center_latitude).compact
    lon_values = postcode_areas.map(&:center_longitude).compact

    if lat_values.empty? || lon_values.empty?
      # Handle case where there are no valid values
      return [0, 0] # or any other default value you prefer
    end

    lat = lat_values.sum / lat_values.length.to_f
    lon = lon_values.sum / lon_values.length.to_f
    [lat, lon]

    # lat = postcode_areas.map(&:center_latitude).sum / postcode_areas.count
    # lon = postcode_areas.map(&:center_longitude).sum / postcode_areas.count
    # [lat, lon]
  end
end
