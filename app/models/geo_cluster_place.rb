# == Schema Information
#
# Table name: geo_cluster_places
#
#  id                         :bigint           not null, primary key
#  added_to_cluster_by_system :boolean          default(TRUE)
#  added_to_cluster_by_user   :boolean          default(FALSE)
#  agency_tenant_uuid         :uuid
#  cluster_place_details      :jsonb
#  cluster_place_flags        :integer          default(0), not null
#  cluster_place_tags         :string           default([]), is an Array
#  cluster_place_title        :string
#  discarded_at               :datetime
#  distance_between           :string
#  distance_between_value     :integer
#  distance_duration          :string
#  distance_duration_unit     :string
#  distance_unit              :string
#  geo_cluster_uuid           :uuid
#  place_within_cluster       :boolean          default(FALSE)
#  place_within_one_mile      :boolean          default(FALSE)
#  position_in_cluster        :integer
#  relevant_place_uuid        :uuid
#  translations               :jsonb
#  uuid                       :uuid
#  value_added_by_place       :integer          default(0)
#  visible_in_cluster         :boolean          default(TRUE)
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
# Indexes
#
#  index_geo_cluster_places_on_agency_tenant_uuid   (agency_tenant_uuid)
#  index_geo_cluster_places_on_cluster_place_flags  (cluster_place_flags)
#  index_geo_cluster_places_on_discarded_at         (discarded_at)
#  index_geo_cluster_places_on_geo_cluster_uuid     (geo_cluster_uuid)
#  index_geo_cluster_places_on_relevant_place_uuid  (relevant_place_uuid)
#  index_geo_cluster_places_on_uuid                 (uuid)
#
class GeoClusterPlace < ApplicationRecord
  include Json::GeoClusterPlaceJson
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # store_attribute :extra_cluster_details, :neighborhood_cons, :json, default: {}

  delegate :relevant_place_title, :relevant_place_slug, :relevant_place_description,
           :relevant_place_latitude, :relevant_place_longitude,
           :google_place_id,
           to: :relevant_place

  belongs_to :geo_cluster, class_name: 'GeoCluster', foreign_key: 'geo_cluster_uuid', primary_key: :uuid, optional: false
  belongs_to :relevant_place, class_name: 'RelevantPlace', foreign_key: 'relevant_place_uuid', primary_key: :uuid, optional: false

  validates :geo_cluster_uuid, presence: true
  validates :relevant_place_uuid, presence: true
  # validates :postcode_cluster_slug, presence: true, uniqueness: true
  # validates :percent_diff_to_cluster_price, presence: true

  # Ensure the combination of geo_cluster_uuid and relevant_place_uuid is unique
  validates :geo_cluster_uuid, uniqueness: { scope: :relevant_place_uuid, message: 'and relevant_place combination must be unique' }
end
