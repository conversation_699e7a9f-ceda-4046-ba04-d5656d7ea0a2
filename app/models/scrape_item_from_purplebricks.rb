# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromPurplebricks < ScrapeItem
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  default_scope { scrape_is_purplebricks }

  def self.find_or_create_for_h2c_purplebricks(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point
    )
    scrape_item.update!(scrape_is_purplebricks: true)
    ScrapeItemFromPurplebricks.find(scrape_item.id)
  end

  # def sale_listing_from_scrape_item
  #   standardised_listing_hash = property_hash_from_scrape_item
  #   standardised_listing_hash['import_url'] = scrapable_url
  #   request_host = 'guise_listing_server'
  #   creator = Creators::Full::FullListingAndAssetCreator.new
  #   sale_listing = creator.create_from_standardised_hash(
  #     standardised_listing_hash,
  #     request_host
  #   )
  #   sale_listing
  # end

  # 1 mar 2025: below def is to do with scraping a single item
  # from a single listing page!
  def property_hash_from_scrape_item
    return unless full_content_before_js

    purplebricks_json = full_content_before_js

    # listing_file_path = File.join(
    #   Rails.root, 'db', 'external_sites/purplebricks-com/purplebricks.json'
    # )
    # # Ensure the directory exists
    # FileUtils.mkdir_p(File.dirname(listing_file_path))
    # # Write the file
    # File.write(listing_file_path, purplebricks_json)

    # puts top_level_url
    # @doc = Nokogiri::HTML(purplebricks_json)
    property_data = transform_json(purplebricks_json)

    property_data.stringify_keys!
    # property_data.compact # Remove nil values
  end

  def transform_json(json_string)
    data = JSON.parse(json_string)

    transformed_data = {
      listing_data: {
        archived: !data['isLive'], # Assuming isLive false means archived
        commission_cents: 0,
        commission_currency: 'GBP',
        currency: 'GBP',
        design_style: nil,
        details_of_rooms: {},
        discarded_at: nil,
        extra_sale_details: {},
        furnished: false,
        hide_map: false,
        highlighted: false,
        host_on_create: 'unknown_host',
        is_ai_generated_listing: false,
        listing_pages_count: 0,
        listing_slug: data['id'],
        listing_tags: [],
        main_video_url: data['videoTourUrl'] || '', # default
        obscure_map: false,
        page_section_listings_count: 0,
        position_in_list: nil,
        price_sale_current_cents: (data['marketPrice'] * 100).to_i,
        price_sale_current_currency: 'GBP',
        price_sale_original_cents: (data['marketPrice'] * 100).to_i,
        price_sale_original_currency: 'GBP',
        property_board_items_count: 0,
        publish_from: nil,
        publish_till: nil,
        reference: data['id'],
        related_urls: {},
        reserved: false,
        sale_listing_features: data['starPoints'].each_with_index.with_object({}) { |(point, index), hash| hash[index + 1] = point },
        sale_listing_flags: 0,
        sale_listing_gen_prompt: nil,
        service_charge_yearly_cents: (data['tenureDetails']['serviceChargeAnnualAmount'] * 100).to_i,
        service_charge_yearly_currency: 'GBP',
        site_visitor_token: nil,
        sl_photos_count: data['images'].count,
        visible: true
      },
      asset_data: {
        related_urls_from_otm: {
          related_urls_from_otm: {
            property_documents: [],
            other_otm_urls: []
          }
        },
        categories: data['starPoints'].each_with_index.map { |point, index| { id: index + 1, name: point } },
        city: data['location']['postTown'].capitalize,
        city_search_key: data['location']['postTown'].downcase,
        constructed_area: data['features']['propertyArea'].to_f,
        count_bathrooms: data['features']['bathrooms'].to_i,
        count_bedrooms: data['features']['bedrooms'].to_i,
        count_garages: data['features']['carSpaces'].to_i,
        count_toilets: 0,
        country: data['regionType'],
        description: data['description'],
        details: {},
        discarded_at: nil,
        energy_performance: nil,
        energy_rating: data['tenureDetails']['taxBand'],
        floor: nil,
        has_rental_listings: false,
        has_sale_listings: true,
        has_sold_transactions: false,
        host_on_create: 'unknown_host',
        is_ai_generated_realty_asset: false,
        latitude: data['latitude'],
        longitude: data['longitude'],
        neighborhood: data['location']['dependentLocality'],
        neighborhood_search_key: data['location']['dependentLocality'].downcase,
        plot_area: data['features']['landArea'].to_f,
        postal_code: data['postcode'],
        prop_state_key: data['isLive'] ? 'live' : 'not_live',
        prop_type_key: data['title'].downcase.include?('flat') ? 'flat' : 'unknown', # basic example
        province: nil,
        ra_photos_count: data['images'].count,
        realty_asset_flags: 0,
        realty_asset_tags: [],
        reference: data['id'],
        region: data['propertyExpertData']['region'],
        rental_listings_count: 0,
        sale_listings_count: 1,
        site_visitor_token: nil,
        sold_transactions_count: 0,
        street_address: data['address'],
        street_number: nil,
        title: data['title'],
        year_construction: 0
      },
      listing_image_urls: data['images'].map { |image| image['url'] },
      postal_code: data['postcode'],
      import_url: nil # No provided import URL
    }

    transformed_data
  end
end
