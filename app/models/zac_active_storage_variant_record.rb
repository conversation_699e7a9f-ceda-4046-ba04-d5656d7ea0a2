# == Schema Information
#
# Table name: active_storage_variant_records
#
#  id               :bigint           not null, primary key
#  variation_digest :string           not null
#  blob_id          :bigint           not null
#
# Indexes
#
#  index_active_storage_variant_records_uniqueness  (blob_id,variation_digest) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (blob_id => active_storage_blobs.id)
#
class ZacActiveStorageVariantRecord < ActiveStorage::VariantRecord
  # created to get around issues with namespacing in administrate gem
  # belongs_to :blob
  # has_one_attached :image

  delegate :created_at, to: :zac_active_storage_blob

  belongs_to :zac_active_storage_blob, primary_key: 'id', foreign_key: 'blob_id', optional: true
  has_one_attached :image
  has_one :zac_active_storage_attachment, foreign_key: 'record_id', primary_key: 'id'
end
