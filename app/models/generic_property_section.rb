# == Schema Information
#
# Table name: generic_property_sections
#
#  id                          :bigint           not null, primary key
#  agency_tenant_uuid          :uuid
#  discarded_at                :datetime
#  generic_property_uuid       :uuid
#  gp_section_area_sq_ft       :decimal(10, 2)   default(0.0), not null
#  gp_section_area_sq_mt       :decimal(10, 2)   default(0.0), not null
#  gp_section_description      :string
#  gp_section_details          :jsonb
#  gp_section_dimensions       :string           default("")
#  gp_section_flags            :integer          default(0), not null
#  gp_section_photos_count     :integer          default(0), not null
#  gp_section_position_in_list :integer
#  gp_section_reference        :string
#  gp_section_slug             :string
#  gp_section_tags             :string           default([]), is an Array
#  gp_section_title            :string
#  gp_section_type             :integer          default("generic_section"), not null
#  is_room                     :boolean          default(TRUE)
#  llm_interaction_uuid        :uuid
#  translations                :jsonb
#  uuid                        :uuid
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#
# Indexes
#
#  index_generic_property_sections_on_agency_tenant_uuid     (agency_tenant_uuid)
#  index_generic_property_sections_on_discarded_at           (discarded_at)
#  index_generic_property_sections_on_generic_property_uuid  (generic_property_uuid)
#  index_generic_property_sections_on_gp_section_flags       (gp_section_flags)
#  index_generic_property_sections_on_gp_section_reference   (gp_section_reference)
#  index_generic_property_sections_on_gp_section_type        (gp_section_type)
#  index_generic_property_sections_on_is_room                (is_room)
#  index_generic_property_sections_on_uuid                   (uuid)
#
class GenericPropertySection < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  belongs_to :generic_property, optional: true, primary_key: 'uuid', foreign_key: 'generic_property_uuid'
  counter_culture :generic_property, column_name: 'generic_property_sections_count'

  has_many :generic_property_photos, -> { order 'gp_photo_position_in_list asc' },
           class_name: 'GenericPropertyPhoto', primary_key: 'uuid', foreign_key: 'generic_property_section_uuid'

  # Validations
  # validates :gp_section_title, presence: true
  # validates :gp_section_type, numericality: { only_integer: true }
  # validates :gp_section_area_sq_mt, :gp_section_area_sq_ft, numericality: { greater_than_or_equal_to: 0 }

  # Enums
  enum gp_section_type: {
    generic_section: 0,
    bedroom: 1,
    kitchen: 2,
    bathroom: 3,
    living_room: 4,
    garden: 5, # Specific garden areas
    hallway: 6, # Hallways or entryways
    dining_room: 7, # Formal dining areas
    office: 8, # Home offices or study rooms
    storage_room: 9, # Rooms specifically for storage
    garage: 10, # Parking or vehicle storage
    balcony: 11, # Outdoor balconies
    terrace: 12, # Larger outdoor terraces
    utility_room: 13, # Laundry or utility rooms
    attic: 14, # Attics or loft spaces
    basement: 15, # Basements or cellars
    playroom: 16, # Rooms designed for children or recreational activities
    gym: 17, # Home gym or exercise areas
    pool_area: 18, # Swimming pool areas
    guest_room: 19, # Rooms for guests
    library: 20, # Library or reading rooms
    theater_room: 21, # Home theater or media rooms
    pantry: 22, # Food storage areas
    cloakroom: 23, # Coat storage or small entry rooms
    porch: 24, # Covered entryways or porches
    outdoor_space: 25 # General outdoor areas

  }

  # Scopes
  scope :rooms, -> { where(is_room: true) }
  scope :discarded, -> { where.not(discarded_at: nil) }
  scope :ordered_by_position, -> { order(gp_section_position_in_list: :asc) }
end
