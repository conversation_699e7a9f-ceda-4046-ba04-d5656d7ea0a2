# == Schema Information
#
# Table name: ad_hoc_data
#
#  id                        :bigint           not null, primary key
#  ad_hoc_class_slug         :string           default(""), not null
#  ad_hoc_data_enum          :integer          default(0), not null
#  ad_hoc_data_flags         :integer          default(0), not null
#  ad_hoc_data_item_slug     :string           default(""), not null
#  ad_hoc_data_tags          :string           default(""), not null
#  ad_hoc_foreign_class_slug :string           default(""), not null
#  ad_hoc_foreign_item_slug  :string           default(""), not null
#  ad_hoc_meta               :jsonb            not null
#  ad_hoc_sub_class_slug     :string           default(""), not null
#  agency_tenant_uuid        :uuid
#  agency_uuid               :uuid
#  discarded_at              :datetime
#  json_representation       :jsonb            not null
#  site_visitor_token        :string
#  translations              :jsonb
#  uuid                      :uuid
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#
# Indexes
#
#  index_ad_hoc_data_on_ad_hoc_class_slug          (ad_hoc_class_slug)
#  index_ad_hoc_data_on_ad_hoc_data_flags          (ad_hoc_data_flags)
#  index_ad_hoc_data_on_ad_hoc_data_item_slug      (ad_hoc_data_item_slug)
#  index_ad_hoc_data_on_ad_hoc_foreign_class_slug  (ad_hoc_foreign_class_slug)
#  index_ad_hoc_data_on_ad_hoc_sub_class_slug      (ad_hoc_sub_class_slug)
#  index_ad_hoc_data_on_agency_tenant_uuid         (agency_tenant_uuid)
#  index_ad_hoc_data_on_discarded_at               (discarded_at)
#  index_ad_hoc_data_on_json_representation        (json_representation) USING gin
#  index_ad_hoc_data_on_site_visitor_token         (site_visitor_token)
#  index_ad_hoc_data_on_uuid                       (uuid)
#
module AdHocData
  class ScraperHost < AdHocDatum
    # AdHocData::ScraperHost
    default_scope { where(ad_hoc_class_slug: 'is_scraper_host') }

    store_attribute :json_representation, :include_trailing_slash, :boolean, default: false

    # user_agent = scraper_host.user_agent || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.47 Safari/537.36'
    # accept = scraper_host.accept || 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'
    # accept_encoding = scraper_host.accept_encoding || 'gzip, deflate, br'

    # store_attribute :json_representation, :all_page_images, :json, default: {}
    # store_attribute :json_representation, :all_page_images_length, :integer, default: 0

    store_attribute :json_representation, :user_agent, :string, default: ''
    store_attribute :json_representation, :accept, :string, default: ''
    store_attribute :json_representation, :accept_encoding, :string, default: ''
    store_attribute :json_representation, :cache_control, :string, default: ''
    # store_attribute :json_representation, :full_content_before_js, :text, default: ''
    # store_attribute :json_representation, :full_content_before_js_length, :integer, default: 0
  end
end
