# == Schema Information
#
# Table name: llm_interaction_associations
#
#  id                               :bigint           not null, primary key
#  agency_tenant_uuid               :uuid             not null
#  associable_type                  :string           not null
#  association_metadata             :jsonb
#  association_type                 :string           not null
#  discarded_at                     :datetime
#  llm_interaction_assoc_aasm_state :string
#  llm_interaction_assoc_flags      :integer          default(0), not null
#  llm_interaction_uuid             :uuid             not null
#  translations                     :jsonb
#  uuid                             :uuid
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  associable_id                    :uuid             not null
#
# Indexes
#
#  idx_on_llm_interaction_assoc_flags_d5d80b59f6               (llm_interaction_assoc_flags)
#  index_llm_interaction_associations_on_agency_tenant_uuid    (agency_tenant_uuid)
#  index_llm_interaction_associations_on_association_type      (association_type)
#  index_llm_interaction_associations_on_discarded_at          (discarded_at)
#  index_llm_interaction_associations_on_llm_interaction_uuid  (llm_interaction_uuid)
#  index_llm_interaction_associations_on_uuid                  (uuid)
#
class LiaToEvalComparableListings < LlmInteractionAssociation
  # Mar 19 2025
  # This class is used to persist the results of an LlmInteraction
  # to evaluate recent sales
  belongs_to :realty_dossier, foreign_key: :associable_id, primary_key: :uuid, optional: true

  default_scope { where(association_type: 'to_eval_comparable_listings') }

  store_attribute :association_metadata, :uuid_of_most_similar_cbl_listing, :string, default: ''
  store_attribute :association_metadata, :comparable_listings_analysis, :json, default: []
  store_attribute :association_metadata, :estimated_fair_value_price, :decimal, default: 0.0
  store_attribute :association_metadata, :is_asking_price_competitive_or_overpriced, :string, default: ''
  store_attribute :association_metadata, :reasoning_content, :json, default: {}

  # belongs_to :comparable_listing, primary_key: 'uuid', foreign_key: 'uuid_of_most_similar_cbl_listing', optional: true
  def summary_console_print
    attributes.except('full_prompt', 'full_response', 'chosen_response')
  end

  def most_similar_comparable_listing
    SaleListing.find_by_uuid(uuid_of_most_similar_cbl_listing)
  end

  # this gets called from RealtyDossier.last.evaluate_against_comparable_listings
  # via LlmDossierComparableListingsEvaluator.evaluate
  def self.find_or_create_from_llm_interaction(
    llm_interaction, dossier_sale_listing, dossier_item
  )
    return unless llm_interaction.chosen_response.present?

    chosen_response_json = JSON.parse(llm_interaction.chosen_response)
    association_type = 'to_eval_comparable_listings'

    responsible_association = LiaToEvalComparableListings.find_or_create_by(
      llm_interaction: llm_interaction,
      associable: dossier_item,
      association_type: association_type,
      # association_metadata: metadata,
      agency_tenant_uuid: llm_interaction.agency_tenant_uuid
    )

    # keys = %w[catchy_title
    #           description_short
    #           description_long
    #           description_bullet_points
    #           id_of_most_similar_comparable_listing
    #           uuid_of_most_similar_comparable_listing
    #           estimated_fair_value_price
    #           is_asking_price_competitive_or_overpriced
    #           reasoning_content
    #           comparable_listings_analysis]
    responsible_association.update!(
      uuid_of_most_similar_cbl_listing: chosen_response_json['uuid_of_most_similar_comparable_listing'],
      estimated_fair_value_price: chosen_response_json['estimated_fair_value_price'],
      is_asking_price_competitive_or_overpriced: chosen_response_json['is_asking_price_competitive_or_overpriced'],
      reasoning_content: chosen_response_json['reasoning_content'],
      comparable_listings_analysis: chosen_response_json['comparable_listings_analysis']
    )

    chosen_response_json['comparable_listings_analysis'].each do |cbl_analysis|
      relevant_sale_listing = SaleListing.find_by_uuid(cbl_analysis['uuid'])
      relevant_dossier_asset = dossier_item.secondary_dossier_assets.find_or_create_by(
        realty_asset_uuid: relevant_sale_listing.realty_asset.uuid
      )
      relevant_dossier_asset.update!(
        # realty_asset_uuid: dossier_sale_listing.realty_asset.uuid,
        is_most_comparable_to_primary: false,
        is_good_comparable_to_primary: true,
        property_is_for_sale: true,
        is_primary_dossier_asset: false
        # associated_listing_uuid: dossier.primary_sale_listing.uuid,
      )

      # relevant_dossier_listing = dossier_item.dossier_listings.find_by(
      #   associated_listing_uuid: cbl_analysis['uuid']
      # )
      # relevant_dossier_listing.update!(
      #   dossier_listing_details: cbl_analysis
      # )
    end
    # March 2025 - perhaps the cols below should be in the realty_dossier item
    # also ....
    dossier_sale_listing.update!(
      catchy_title: chosen_response_json['catchy_title'],
      description_short: chosen_response_json['description_short'],
      description_medium: chosen_response_json['description_medium'],
      description_bullet_points: chosen_response_json['description_bullet_points'],
      description_long: chosen_response_json['description_long']
    )
    responsible_association
  end

  private

  def associable_type_valid?
    associable_type == 'SaleListing'
  end

  validate :associable_type_valid?, if: :new_record?
end
