# == Schema Information
#
# Table name: epc_details
#
#  id                             :bigint           not null, primary key
#  address                        :string
#  address1                       :string
#  address2                       :string
#  address3                       :string
#  agency_tenant_uuid             :uuid
#  building_reference_number      :string
#  built_form                     :string
#  city                           :string
#  co2_emiss_curr_per_floor_area  :decimal(10, 2)
#  co2_emissions_current          :decimal(10, 2)
#  co2_emissions_potential        :decimal(10, 2)
#  constituency                   :string
#  constituency_label             :string
#  construction_age_band          :string
#  construction_age_band_full     :string
#  county                         :string
#  current_energy_efficiency      :integer
#  current_energy_rating          :string
#  discarded_at                   :datetime
#  energy_consumption_current     :float
#  energy_consumption_potential   :float
#  energy_tariff                  :string
#  environmental_impact_current   :integer
#  environmental_impact_potential :integer
#  epc_extra_details              :jsonb
#  epc_flags                      :integer          default(0), not null
#  epc_latitude                   :float
#  epc_longitude                  :float
#  epc_outcode                    :string
#  epc_postal_code                :string
#  epc_recommendations_count      :integer          default(0), not null
#  epc_tags                       :string           default([]), is an Array
#  epc_uprn                       :string
#  extension_count                :integer
#  fixed_lighting_outlets_count   :float
#  flat_storey_count              :decimal(, )
#  flat_top_storey                :string
#  floor_description              :string
#  floor_energy_eff               :string
#  floor_env_eff                  :string
#  floor_height                   :decimal(10, 2)
#  floor_level                    :string
#  glazed_area                    :string
#  glazed_type                    :string
#  heat_loss_corridor             :string
#  heating_cost_current           :float
#  heating_cost_potential         :float
#  hot_water_cost_current         :float
#  hot_water_cost_potential       :float
#  hot_water_energy_eff           :string
#  hot_water_env_eff              :string
#  hotwater_description           :string
#  inspection_date                :date
#  last_sale_listing_uuid         :uuid
#  last_sold_transaction_uuid     :uuid
#  lighting_cost_current          :float
#  lighting_cost_potential        :float
#  lighting_description           :string
#  lighting_energy_eff            :string
#  lighting_env_eff               :string
#  lmk_key                        :string           not null
#  local_authority                :string
#  local_authority_label          :string
#  lodgement_date                 :date
#  lodgement_datetime             :datetime
#  low_energy_fixed_light_count   :float
#  low_energy_lighting            :integer
#  main_fuel                      :string
#  main_heating_controls          :string
#  mainheat_description           :string
#  mainheat_energy_eff            :string
#  mainheat_env_eff               :string
#  mainheatc_energy_eff           :string
#  mainheatc_env_eff              :string
#  mainheatcont_description       :string
#  mains_gas_flag                 :string
#  mechanical_ventilation         :string
#  multi_glaze_proportion         :float
#  number_habitable_rooms         :float
#  number_heated_rooms            :float
#  number_open_fireplaces         :integer
#  photo_supply                   :float
#  postcode_area_uuid             :uuid
#  posttown                       :string
#  potential_energy_efficiency    :integer
#  potential_energy_rating        :string
#  property_type                  :string
#  province                       :string
#  realty_asset_uuid              :uuid
#  roof_description               :string
#  roof_energy_eff                :string
#  roof_env_eff                   :string
#  secondheat_description         :string
#  sheating_energy_eff            :string
#  sheating_env_eff               :string
#  solar_water_heating_flag       :string
#  tenure                         :string
#  total_floor_area               :decimal(10, 2)
#  transaction_type               :string
#  translations                   :jsonb
#  unheated_corridor_length       :decimal(10, 2)
#  uprn                           :bigint
#  uprn_source                    :string
#  uuid                           :uuid
#  walls_description              :string
#  walls_energy_eff               :string
#  walls_env_eff                  :string
#  wind_turbine_count             :float
#  windows_description            :string
#  windows_energy_eff             :string
#  windows_env_eff                :string
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#
# Indexes
#
#  index_epc_details_on_agency_tenant_uuid          (agency_tenant_uuid)
#  index_epc_details_on_discarded_at                (discarded_at)
#  index_epc_details_on_epc_flags                   (epc_flags)
#  index_epc_details_on_epc_postal_code             (epc_postal_code)
#  index_epc_details_on_last_sold_transaction_uuid  (last_sold_transaction_uuid)
#  index_epc_details_on_lmk_key                     (lmk_key) UNIQUE
#  index_epc_details_on_postcode_area_uuid          (postcode_area_uuid)
#  index_epc_details_on_realty_asset_uuid           (realty_asset_uuid)
#  index_epc_details_on_uprn                        (uprn)
#  index_epc_details_on_uuid                        (uuid)
#
class EpcDetail < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  belongs_to :uprn_detail, class_name: 'UprnDetail', foreign_key: 'uprn', primary_key: 'uprn', optional: true
  belongs_to :postcode_area, class_name: 'PostcodeArea', foreign_key: 'postcode_area_uuid', primary_key: :uuid, optional: true
  # counter_culture :postcode_area, column_name: 'epc_details_count'
  # belongs_to :postcode_area, class_name: 'PostcodeArea', foreign_key: 'postcode_area_uuid', primary_key: :uuid, optional: true

  belongs_to :uk_realty_asset, class_name: 'RealtyAsset', foreign_key: 'ra_uprn', primary_key: 'uprn', optional: true
  # Feb 2025 - can't decide if above is better than below
  belongs_to :realty_asset, primary_key: 'uuid', foreign_key: 'realty_asset_uuid', optional: true
  # counter_culture :realty_asset, column_name: 'epc_details_count'
  has_one :sold_transaction_epc, primary_key: 'uuid', foreign_key: 'epc_detail_uuid'

  store_attribute :epc_extra_details, :extra_website_content, :json, default: {}
  store_attribute :epc_extra_details, :website_ref, :string, default: ''

  # might want to add construction_age_min and construction_age_max
  # fields to the model to make it easier to query
  enum construction_age_band: {
    pre_1900: 'pre-1900',
    age_1900_1929: '1900-1929',
    age_1930_1949: '1930-1949',
    age_1950_1966: '1950-1966',
    age_1967_1975: '1967-1975',
    age_1976_1982: '1976-1982',
    age_1983_1990: '1983-1990',
    age_1991_1995: '1991-1995',
    age_1996_2002: '1996-2002',
    age_2003_2006: '2003-2006',
    age_2007_2011: '2007-2011',
    age_2007_onwards: '2007-onwards',
    age_2012_onwards: '2012-onwards',
    age_2020_onwards: '2020-onwards'
  }

  def self.convert_construction_age_band(value)
    case value
    when 'NO DATA!', 'INVALID!', 'Not applicable', ''
      nil
    when 'England and Wales: before 1900'
      construction_age_bands[:pre_1900]
    when 'England and Wales: 1900-1929'
      construction_age_bands[:age_1900_1929]
    when 'England and Wales: 1930-1949'
      construction_age_bands[:age_1930_1949]
    when 'England and Wales: 1950-1966'
      construction_age_bands[:age_1950_1966]
    when 'England and Wales: 1967-1975'
      construction_age_bands[:age_1967_1975]
    when 'England and Wales: 1976-1982'
      construction_age_bands[:age_1976_1982]
    when 'England and Wales: 1983-1990'
      construction_age_bands[:age_1983_1990]
    when 'England and Wales: 1991-1995'
      construction_age_bands[:age_1991_1995]
    when 'England and Wales: 1996-2002'
      construction_age_bands[:age_1996_2002]
    when 'England and Wales: 2003-2006'
      construction_age_bands[:age_2003_2006]
    when 'England and Wales: 2007-2011'
      construction_age_bands[:age_2007_2011]
    when 'England and Wales: 2007 onwards'
      construction_age_bands[:age_2007_onwards]
    when 'England and Wales: 2012 onwards'
      construction_age_bands[:age_2012_onwards]
    when value.to_i > 2019
      construction_age_bands[:age_2020_onwards]
    # when '2020'
    #   construction_age_bands[:age_2020]
    else
      if value.to_i > 2016
        'age_2020_onwards'
      else
        puts "Unexpected construction_age_band value: #{value}"
        # Rails.logger.warn "Unexpected construction_age_band value: #{value}"
        nil # or some default value if applicable
      end
    end
  end

  def as_summary_json(options = {})
    as_json(options.merge(
              only: %i[total_floor_area
                       inspection_date address1],
              methods: [],
              include: {}
            ))
  end

  # Class method for collections
  def self.as_summary_json(options = {})
    all.map { |epc| epc.as_summary_json(options) }
  end

  def as_detailed_json(options = {})
    as_json(options.merge(
              only: %i[total_floor_area
                       construction_age_band_full
                       address
                       address1
                       address2
                       building_reference_number
                       built_form
                       city
                       co2_emiss_curr_per_floor_area
                       co2_emissions_current
                       co2_emissions_potential
                       constituency
                       constituency_label
                       construction_age_band
                       county
                       current_energy_efficiency
                       current_energy_rating
                       discarded_at
                       energy_consumption_current
                       energy_consumption_potential
                       energy_tariff
                       environmental_impact_current
                       environmental_impact_potential
                       epc_extra_details
                       epc_flags
                       epc_latitude
                       epc_longitude
                       epc_outcode
                       epc_tags
                       extension_count
                       fixed_lighting_outlets_count
                       flat_storey_count
                       flat_top_storey
                       floor_description
                       floor_energy_eff
                       floor_env_eff
                       floor_height
                       floor_level
                       glazed_area
                       glazed_type
                       heat_loss_corridor
                       heating_cost_current
                       heating_cost_potential
                       hot_water_cost_current
                       hot_water_cost_potential
                       hot_water_energy_eff
                       hot_water_env_eff
                       hotwater_description
                       inspection_date
                       lighting_cost_current
                       lighting_cost_potential
                       lighting_description
                       lighting_energy_eff
                       lighting_env_eff
                       lmk_key
                       local_authority
                       local_authority_label
                       lodgement_date
                       lodgement_datetime
                       low_energy_fixed_light_count
                       low_energy_lighting
                       main_fuel
                       main_heating_controls
                       mainheat_description
                       mainheat_energy_eff
                       mainheat_env_eff
                       mainheatc_energy_eff
                       mainheatc_env_eff
                       mainheatcont_description
                       mains_gas_flag
                       mechanical_ventilation
                       multi_glaze_proportion
                       number_habitable_rooms
                       number_heated_rooms
                       number_open_fireplaces
                       photo_supply
                       epc_postal_code
                       posttown
                       potential_energy_efficiency
                       potential_energy_rating
                       property_type
                       province
                       roof_description
                       roof_energy_eff
                       roof_env_eff
                       secondheat_description
                       sheating_energy_eff
                       sheating_env_eff
                       solar_water_heating_flag
                       tenure
                       transaction_type
                       translations
                       unheated_corridor_length
                       uprn
                       walls_description
                       walls_energy_eff
                       walls_env_eff
                       wind_turbine_count
                       windows_description
                       windows_energy_eff
                       windows_env_eff
                       inspection_date],
              methods: [],
              include: {}
            ))
  end
end
