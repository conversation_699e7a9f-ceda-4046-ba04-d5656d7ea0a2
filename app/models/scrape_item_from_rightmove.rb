# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromRightmove < ScrapeItem
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  default_scope { scrape_is_rightmove }

  def self.find_or_create_for_h2c_rightmove(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: false
    )
    scrape_item.update!(
        scrape_is_rightmove: true,
        content_is_html: true
      )
    ScrapeItemFromRightmove.find(scrape_item.id)
  end

  # def sale_listing_from_scrape_item
  #   standardised_listing_hash = property_hash_from_scrape_item
  #   standardised_listing_hash['import_url'] = scrapable_url
  #   # request_host = 'guise_listing_server'
  #   creator = Creators::Full::FullListingAndAssetCreator.new
  #   sale_listing = creator.create_from_standardised_hash(
  #     standardised_listing_hash
  #     # request_host
  #   )
  #   sale_listing
  # end

  def property_hash_from_scrape_item
    return unless script_json

    rightmove_json = script_json

    property_data = {}

    listing_data = map_property_to_listing_schema(rightmove_json)
    asset_data = map_property_to_asset_schema(rightmove_json)

    property_data[:listing_data] = listing_data
    property_data[:asset_data] = asset_data
    property_data[:listing_image_urls] = rightmove_json['propertyData']['images'].map { |img| img['url'] }

    property_data.stringify_keys!
    property_data
  end

  private

  def map_property_to_asset_schema(property)
    property_data = property['propertyData']
    {
      'title' => property_data['text']['pageTitle'],
      'categories' => property_data['keyFeatures'] ? property_data['keyFeatures'].map { |f| { 'id' => f.downcase.gsub(/\s+/, '-'), 'name' => f } } : [],
      'city' => property_data.dig('address', 'displayAddress').split(',').last.strip,
      'city_search_key' => property_data.dig('address', 'displayAddress').split(',').last.strip.downcase.gsub(/\s+/, '-') || '',
      'constructed_area' => 0.0,
      'count_bathrooms' => property_data['bathrooms'].to_f || 0.0,
      'count_bedrooms' => property_data['bedrooms'] || 0,
      'count_garages' => property_data['text']['description'].to_s.downcase.include?('garage') ? 1 : 0,
      'count_toilets' => 0,
      'country' => property_data.dig('address', 'ukCountry')&.capitalize,
      'description' => property_data['text']['description'],
      'details' => property_data['rooms'] ? property_data['rooms'].map { |room| [room['name'], room] }.to_h : {},
      'discarded_at' => nil,
      'energy_performance' => nil,
      'energy_rating' => property_data.dig('epcGraphs', 0, 'caption') == 'EPC' ? 'Available' : nil,
      'floor' => nil,
      'has_rental_listings' => false,
      'has_sale_listings' => true,
      'has_sold_transactions' => false,
      'host_on_create' => 'unknown_host',
      'latitude' => property_data.dig('location', 'latitude'),
      'longitude' => property_data.dig('location', 'longitude'),
      'neighborhood' => nil,
      'neighborhood_search_key' => '',
      'plot_area' => 0.0,
      'postal_code' => property_data.dig('address', 'outcode') + ' ' + property_data.dig('address', 'incode'),
      'prop_origin_key' => '', # Not directly available
      'prop_state_key' => 'new',
      'prop_type_key' => property_data.dig('infoReelItems', 0, 'primaryText')&.downcase&.gsub(/\s+/, '-') || '',
      'province' => property_data.dig('address', 'ukCountry')&.capitalize,
      'ra_photos_count' => property_data['images'].size,
      'realty_asset_flags' => 0,
      'realty_asset_tags' => property_data['tags'] || [],
      'reference' => property_data['id'],
      'region' => property_data.dig('address', 'ukCountry')&.capitalize,
      'rental_listings_count' => 0,
      'sale_listings_count' => 1,
      'site_visitor_token' => nil,
      'sold_transactions_count' => 0,
      'street_address' => property_data.dig('address', 'displayAddress'),
      'street_number' => nil,
      'year_construction' => 0
    }
  end

  def map_property_to_listing_schema(property)
    property_data = property['propertyData']
    price_raw = property_data.dig('prices', 'primaryPrice')&.gsub(/[^0-9.]/, '')&.to_f || 0.0
    # may 18th 2025 - like an eejit I had only been mapping title and description
    # for the asset schema and not the listing schema here!!!
    {
      'title' => property_data['text']['pageTitle'],
      'description' => property_data['text']['description'],
      'archived' => property_data.dig('status', 'archived') || false,
      'commission_cents' => 0,
      'commission_currency' => 'GBP',
      'currency' => 'GBP',
      'design_style' => nil,
      'details_of_rooms' => property_data['rooms'] ? property_data['rooms'].map { |room| [room['name'], room] }.to_h : {},
      'discarded_at' => nil,
      'extra_sale_details' => {},
      'furnished' => false,
      'hide_map' => false,
      'highlighted' => property_data.dig('misInfo', 'premiumDisplay') || false,
      'host_on_create' => 'unknown_host',
      'is_ai_generated_listing' => false,
      'listing_pages_count' => 0,
      'listing_slug' => property_data['id'],
      'listing_tags' => property_data['tags'] || [],
      'main_video_url' => property_data.dig('virtualTours', 0, 'url'),
      'obscure_map' => false,
      'page_section_listings_count' => 0,
      'position_in_list' => nil,
      'price_sale_current_cents' => (price_raw * 100).to_i,
      'price_sale_current_currency' => 'GBP',
      'price_sale_original_cents' => (price_raw * 100).to_i,
      'price_sale_original_currency' => 'GBP',
      'property_board_items_count' => 0,
      'publish_from' => nil,
      'publish_till' => nil,
      'reference' => property_data['id'],
      'related_urls' => {},
      'reserved' => false,
      'sale_listing_features' => property_data['keyFeatures'] ? property_data['keyFeatures'].map { |feature| [feature.downcase.gsub(/\s+/, '-'), feature] }.to_h : {},
      'sale_listing_flags' => 0,
      'sale_listing_gen_prompt' => nil,
      'service_charge_yearly_cents' => property_data.dig('livingCosts', 'annualServiceCharge') ? (property_data.dig('livingCosts', 'annualServiceCharge') * 100).to_i : 0,
      'service_charge_yearly_currency' => 'GBP',
      'site_visitor_token' => nil,
      'sl_photos_count' => property_data['images'].size,
      'visible' => property_data.dig('status', 'published') || true
    }
  end
end
