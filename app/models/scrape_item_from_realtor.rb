# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
require 'json'

class ScrapeItemFromRealtor < ScrapeItem
  # No dedicated flag bit yet; we can add one later if needed.

  def self.find_or_create_for_h2c_realtor(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: false
    )
    scrape_item.update!(
      is_realty_search_scrape: false,
      content_is_html: true
    )
    ScrapeItemFromRealtor.find(scrape_item.id)
  end

  # Keep interface parity with other ScrapeItemFrom* models
  def property_hash_from_scrape_item
    html = full_content_before_js
    raise 'full_content_before_js unavailable' unless html.present?

    doc = Nokogiri::HTML(html)

  # very light extraction for parity; actual persistence is handled by pasarela on RealtyScrapedItem
    price_text = doc.at('meta[property="product:price:amount"]')&.[]('content') || doc.at('[data-testid="ldp-list-price"]')&.text
    price_cents = price_text.to_s.gsub(/[^0-9]/, '').to_i * 100

    listing_data = {
      'title' => doc.at('title')&.text,
      'price_sale_current_cents' => price_cents,
      'price_sale_current_currency' => 'USD',
      'price_sale_original_cents' => price_cents,
      'price_sale_original_currency' => 'USD',
      'reference' => doc.at('link[rel="canonical"]')&.[]('href')
    }

    address = {
      'street_address' => doc.at('meta[property="og:street-address"]')&.[]('content'),
      'city' => doc.at('meta[property="og:locality"]')&.[]('content'),
      'province' => doc.at('meta[property="og:region"]')&.[]('content'),
      'postal_code' => doc.at('meta[property="og:postal-code"]')&.[]('content'),
      'country' => doc.at('meta[property="og:country-name"]')&.[]('content')
    }

    asset_data = {
      'title' => listing_data['title'],
      'street_address' => address['street_address'],
      'city' => address['city'],
      'province' => address['province'],
      'postal_code' => address['postal_code'],
      'country' => address['country'],
      'has_sale_listings' => true,
      'sale_listings_count' => 1
    }

    image_urls = []
    image_urls += doc.css('meta[property="og:image"]').map { |m| m['content'] }.compact
    image_urls += doc.css('meta[name^="twitter:image"]').map { |m| m['content'] }.compact

    out = { listing_data: listing_data, asset_data: asset_data, listing_image_urls: image_urls }
    out.stringify_keys!
  end
end
