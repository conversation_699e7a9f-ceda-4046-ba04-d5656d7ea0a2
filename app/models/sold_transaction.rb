# == Schema Information
#
# Table name: sold_transactions
#
#  id                                    :bigint           not null, primary key
#  agency_tenant_uuid                    :uuid
#  archived                              :boolean          default(FALSE)
#  auction                               :boolean          default(FALSE)
#  cloned_from_uuid                      :string
#  discarded_at                          :datetime
#  furnished                             :boolean          default(FALSE)
#  generic_property_uuid                 :uuid
#  geo_area_uuid                         :uuid
#  highest_listed_price_cents            :bigint           default(0), not null
#  highest_listed_price_currency         :string           default("EUR"), not null
#  is_synthetic_transaction              :boolean          default(FALSE)
#  land_registry_reference               :string
#  leasehold_or_freehold                 :integer          default(0)
#  leasehold_years_remaining             :integer          default(0)
#  likely_service_charge_yearly_cents    :bigint           default(0), not null
#  likely_service_charge_yearly_currency :string           default("EUR"), not null
#  lowest_listed_price_cents             :bigint           default(0), not null
#  lowest_listed_price_currency          :string           default("EUR"), not null
#  new_home                              :boolean          default(FALSE)
#  postcode_area_uuid                    :uuid
#  potential_rental_daily_cents          :bigint           default(0), not null
#  potential_rental_daily_currency       :string           default("EUR"), not null
#  potential_rental_monthly_cents        :bigint           default(0), not null
#  potential_rental_monthly_currency     :string           default("EUR"), not null
#  ppd_category_type                     :string
#  published                             :boolean          default(FALSE)
#  realty_asset_uuid                     :uuid
#  record_status                         :string
#  related_urls                          :jsonb
#  retirement_home                       :boolean          default(FALSE)
#  rightmove_tracking_url                :string
#  shared_ownership                      :boolean          default(FALSE)
#  site_visitor_token                    :string
#  sold_date                             :date
#  sold_price_cents                      :bigint           default(0), not null
#  sold_price_currency                   :string           default("EUR"), not null
#  sold_transaction_currency             :string
#  sold_transaction_description          :string
#  sold_transaction_details              :jsonb
#  sold_transaction_flags                :integer          default(0), not null
#  sold_transaction_photos_count         :integer          default(0), not null
#  sold_transaction_reference            :string
#  sold_transaction_slug                 :string
#  sold_transaction_source               :string
#  sold_transaction_tags                 :string           default([]), is an Array
#  sold_transaction_title                :string
#  st_age_of_property                    :string
#  st_latitude                           :float
#  st_long_address                       :string
#  st_longitude                          :float
#  st_outcode                            :string
#  st_paon                               :string
#  st_position_in_list                   :integer
#  st_postal_code                        :string
#  st_property_type                      :string
#  st_saon                               :string
#  st_street                             :string
#  st_tenure                             :string
#  st_uprn                               :string
#  translations                          :jsonb
#  user_uuid                             :uuid
#  uuid                                  :uuid
#  versions_count                        :integer          default(0), not null
#  visible                               :boolean          default(FALSE)
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#
# Indexes
#
#  index_sold_transactions_on_agency_tenant_uuid            (agency_tenant_uuid)
#  index_sold_transactions_on_discarded_at                  (discarded_at)
#  index_sold_transactions_on_generic_property_uuid         (generic_property_uuid)
#  index_sold_transactions_on_geo_area_uuid                 (geo_area_uuid)
#  index_sold_transactions_on_land_registry_reference       (land_registry_reference)
#  index_sold_transactions_on_postcode_area_uuid            (postcode_area_uuid)
#  index_sold_transactions_on_published                     (published)
#  index_sold_transactions_on_realty_asset_uuid             (realty_asset_uuid)
#  index_sold_transactions_on_rightmove_tracking_url        (rightmove_tracking_url)
#  index_sold_transactions_on_sold_transaction_flags        (sold_transaction_flags)
#  index_sold_transactions_on_sold_transaction_slug         (sold_transaction_slug)
#  index_sold_transactions_on_st_latitude_and_st_longitude  (st_latitude,st_longitude)
#  index_sold_transactions_on_uuid                          (uuid)
#  index_sold_transactions_on_visible                       (visible)
#
class SoldTransaction < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # self.table_name = 'sale_listings'
  # Above to solve error when I tried to access sale_listing via administrate:
  # base_listing  has no table configured. Set one with BaseListing.table_name=

  # Should really have been called a SaleTransaction & rental equivalent
  # RentalTransaction

  delegate :count_toilets, :count_garages, :count_bedrooms,
           :area_unit, :plot_area, :constructed_area, :year_construction,
           :latitude, :longitude, :city, :region, :country, :street_name,
           :neighborhood,
           :formatted_constructed_area,
           :street_number, :postal_code, :street_address,
           :count_bathrooms, to: :realty_asset, allow_nil: true

  include Discard::Model
  has_paper_trail

  store_attribute :sold_transaction_details, :st_predicted_price, :string, default: ''
  store_attribute :sold_transaction_details, :st_predicted_price_off_by, :integer, default: 0

  store_attribute :sold_transaction_details, :st_predicted_price_b, :string, default: ''
  store_attribute :sold_transaction_details, :st_predicted_price_off_by_b, :integer, default: 0
  store_attribute :sold_transaction_details, :st_predicted_price_c, :string, default: ''
  store_attribute :sold_transaction_details, :st_predicted_price_off_by_c, :integer, default: 0

  scope :real, -> { where(is_synthetic_transaction: false) }
  # validates :sold_date, uniqueness: { message: 'must be unique' }
  # validates :sold_date, uniqueness: {
  #   scope: %i[agency_tenant_uuid realty_asset_uuid generic_property_uuid],
  #   message: 'must be unique for this combination of sold_date, property, agency, and realty asset'
  # }

  belongs_to :uprn_detail, class_name: 'UprnDetail', foreign_key: 'st_uprn', primary_key: 'uprn', optional: true

  belongs_to :generic_property, primary_key: 'uuid', foreign_key: 'generic_property_uuid', optional: true
  # counter_culture :generic_property, column_name: 'sold_transactions_count'

  has_many :sold_transaction_epcs, primary_key: 'uuid', foreign_key: 'sold_transaction_uuid'

  belongs_to :uk_realty_asset, class_name: 'RealtyAsset', foreign_key: 'ra_uprn', primary_key: 'st_uprn', optional: true
  # Feb 2025 - can't decide if above is better than below
  belongs_to :realty_asset, primary_key: 'uuid', foreign_key: 'realty_asset_uuid', optional: true

  counter_culture :realty_asset, column_name: 'sold_transactions_count'
  belongs_to :postcode_area, class_name: 'PostcodeArea', primary_key: 'uuid',
                             foreign_key: 'postcode_area_uuid', optional: true
  counter_culture :postcode_area, column_name: 'sold_transactions_count'

  # might make sense to have ListingPhoto
  # model instead of using RealtyAssetPhoto below
  has_many :listing_photos, -> { order 'sort_order asc' },
           # If I keep RealtyAssetPhoto, I need to add sold_transaction_uuid to it
           class_name: 'RealtyAssetPhoto', primary_key: 'uuid', foreign_key: 'sale_listing_uuid'
  has_many :ordered_visible_listing_photos, lambda {
    not_flag_is_hidden.order('sort_order asc')
    # where(visible_for_sale_listing: true).order("sort_order asc")
  }, class_name: 'RealtyAssetPhoto', primary_key: 'uuid', foreign_key: 'sale_listing_uuid'

  monetize :sold_price_cents, with_model_currency: :sold_transaction_currency

  def formatted_sold_price
    sold_price.format
  end

  def short_formatted_sold_price
    # Convert the sold price to thousands
    thousands_price = sold_price.cents / 100 / 1000

    # Format the price and append "k"
    "#{sold_price.currency.symbol}#{thousands_price}k"
  end

  def as_summary_json(options = {})
    as_json(options.merge(
              only: %i[
                uuid new_home sold_date sold_transaction_reference
                sold_transaction_title st_latitude st_longitude
                st_long_address st_postal_code st_property_type
              ],
              methods: %i[formatted_sold_price short_formatted_sold_price],
              include: {
                listing_photos: {
                  methods: [:image_details],
                  only: %i[uuid sort_order]
                }
              }
            ))
  end

  def as_minimal_json(options = {})
    as_json(options.merge(
              only: %i[new_home sold_date
                       st_long_address]
            ))
  end

  # Class method for collections
  def self.as_minimal_json(options = {})
    all.map { |epc| epc.as_minimal_json(options) }
  end
end
