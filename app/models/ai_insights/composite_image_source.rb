# == Schema Information
#
# Table name: ai_insights_composite_image_sources
#
#  id                            :bigint           not null, primary key
#  agency_tenant_uuid            :uuid
#  associated_listing_uuid       :uuid
#  batch_limit                   :integer
#  batch_offset                  :integer
#  composite_checksum            :string
#  composite_image_type          :string           default("type_a"), not null
#  composite_photo_capture_url   :string
#  composite_photo_file_path     :string
#  description_medium            :text             default("")
#  discarded_at                  :datetime
#  dossier_asset_uuid            :uuid             not null
#  duration_ms                   :integer
#  error_message                 :text
#  file_size_bytes               :integer
#  flags                         :integer          default(0), not null
#  llm_interaction_uuid          :uuid             not null
#  llm_model                     :string
#  main_section_or_room_details  :jsonb            not null
#  other_section_or_room_details :jsonb            not null
#  prompt_payload                :text
#  prompt_template_version       :string
#  property_is_for_rent          :boolean          default(FALSE), not null
#  property_is_for_sale          :boolean          default(TRUE), not null
#  realty_dossier_uuid           :uuid             not null
#  response_status               :string
#  screenshot_dimensions         :jsonb
#  screenshot_url                :string
#  sub_images                    :jsonb            not null
#  sub_images_count              :integer
#  unique_rooms_or_sections      :jsonb            not null
#  uuid                          :uuid
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#
# Indexes
#
#  idx_on_agency_tenant_uuid_10c59a2382                       (agency_tenant_uuid)
#  idx_on_associated_listing_uuid_207e1028bc                  (associated_listing_uuid)
#  idx_on_composite_image_type_a1444eb42a                     (composite_image_type)
#  idx_on_dossier_asset_uuid_9e85d171e8                       (dossier_asset_uuid)
#  idx_on_llm_interaction_uuid_6a2c19d0de                     (llm_interaction_uuid)
#  idx_on_realty_dossier_uuid_77733528d6                      (realty_dossier_uuid)
#  index_ai_insights_composite_image_sources_on_discarded_at  (discarded_at)
#  index_ai_insights_composite_image_sources_on_flags         (flags)
#  index_ai_insights_composite_image_sources_on_uuid          (uuid) UNIQUE
#
module AiInsights
  class CompositeImageSource < ApplicationRecord
    acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

    # Constants for JSON keys
    SUB_IMAGE_DESCRIPTIONS_KEY = 'sub_image_descriptions'.freeze
    IMAGE_ID_KEY = 'image_id'.freeze

    # Associations
    belongs_to :realty_dossier, foreign_key: :realty_dossier_uuid, primary_key: :uuid, optional: true
    belongs_to :llm_interaction, foreign_key: :llm_interaction_uuid, primary_key: :uuid, optional: true
    belongs_to :dossier_asset, foreign_key: :dossier_asset_uuid, primary_key: :uuid, optional: true
    # belongs_to :realty_asset_photo, foreign_key: :realty_asset_photo_uuid, primary_key: :uuid, optional: true
    # belongs_to :agency_tenant, foreign_key: :agency_tenant_uuid, primary_key: :uuid, optional: true

    # Validations
    validates :uuid, presence: true, uniqueness: true
    validates :llm_interaction_uuid, presence: true
    validates :dossier_asset_uuid, presence: true
    validates :realty_dossier_uuid, presence: true
    validates :composite_image_type, presence: true, inclusion: { in: %w[type_a] } # Add more types as needed

    # Before validation: set UUID if not present
    before_validation :set_uuid, on: :create

    # Finds or creates a composite image source from an AI interaction
    #
    # @param llm_interaction [LlmInteraction] The AI interaction containing the photo analysis
    # @param dossier_item [RealtyDossier] The associated realty dossier
    # @param target_dossier_asset [DossierAsset] The target asset being analyzed
    # @param composite_photo_file_path [String] File path to the composite image used
    # @return [CompositeImageSource] The created or updated composite image source
    # @raise [InvalidLlmResponseError] If the LLM response is invalid
    # @raise [MismatchedDossierError] If the response doesn't match the dossier or asset
    # @raise [InvalidSubImagesError] If the response contains invalid sub-image IDs
    def self.composite_image_insight_from_ai_interaction(
      llm_interaction,
      dossier_item,
      target_dossier_asset,
      composite_photo_file_path: nil,
      batch_offset: nil,
      batch_limit: nil,
      llm_model: nil,
      prompt_template_version: nil,
      prompt_payload: nil,
      response_status: nil,
      error_message: nil,
      duration_ms: nil,
      screenshot_dimensions: nil,
      file_size_bytes: nil,
      sub_images_count: nil,
      composite_checksum: nil,
      screenshot_url: nil
    )
      Rails.logger.info("Processing LLM interaction #{llm_interaction.uuid} for dossier #{dossier_item.uuid}")
      validate_interaction_and_response(llm_interaction, dossier_item, target_dossier_asset)
      chosen_response_json = parse_chosen_response(llm_interaction.chosen_response)
      validate_sub_images(chosen_response_json, target_dossier_asset)

      photo_info_source = create_or_update_info_source(
        llm_interaction,
        dossier_item,
        target_dossier_asset,
        composite_photo_file_path: composite_photo_file_path,
        batch_offset: batch_offset,
        batch_limit: batch_limit,
        llm_model: llm_model,
        prompt_template_version: prompt_template_version,
        prompt_payload: prompt_payload,
        response_status: response_status,
        error_message: error_message,
        duration_ms: duration_ms,
        screenshot_dimensions: screenshot_dimensions,
        file_size_bytes: file_size_bytes,
        sub_images_count: sub_images_count,
        composite_checksum: composite_checksum,
        screenshot_url: screenshot_url
      )
      process_composite_photo_data(photo_info_source, chosen_response_json, target_dossier_asset)
      photo_info_source
    rescue StandardError => e
      Rails.logger.error("Failed to process LLM interaction #{llm_interaction.uuid}: #{e.message}")
      raise
    end

    private

    # Custom error classes
    class InvalidLlmResponseError < StandardError; end
    class MismatchedDossierError < StandardError; end
    class InvalidSubImagesError < StandardError; end

    # Sets a UUID if not already present
    def set_uuid
      self.uuid ||= SecureRandom.uuid
    end

    # Validates the LLM interaction and response
    def self.validate_interaction_and_response(llm_interaction, dossier_item, target_dossier_asset)
      raise InvalidLlmResponseError, 'Invalid LLM interaction' unless valid_llm_interaction?(llm_interaction)

      chosen_response_json = parse_chosen_response(llm_interaction.chosen_response)
      raise MismatchedDossierError, 'Response for wrong dossier' if dossier_item.id.to_s != chosen_response_json['dossier_item_id']
      raise MismatchedDossierError, 'Response for wrong asset' if target_dossier_asset.id.to_s != chosen_response_json['target_dossier_asset_id']
    end

    # Parses the chosen response JSON
    def self.parse_chosen_response(chosen_response)
      JSON.parse(chosen_response)
    rescue JSON::ParserError => e
      raise InvalidLlmResponseError, "Failed to parse LLM response: #{e.message}"
    end

    # Validates sub-image IDs against the target dossier asset
    def self.validate_sub_images(chosen_response_json, target_dossier_asset)
      sub_images_ids = chosen_response_json[SUB_IMAGE_DESCRIPTIONS_KEY]&.pluck(IMAGE_ID_KEY) || []
      asset_photo_ids = target_dossier_asset.asset_photo_short_ids.to_set
      puts "Images IDs referrenced by LLM: #{sub_images_ids.inspect}"
      puts "Actual asset photo IDs: #{asset_photo_ids.inspect}"
      raise InvalidSubImagesError, 'LLM seems to ref invalid images' unless sub_images_ids.all? { |id| asset_photo_ids.include?(id.to_i) }
      # below does not make sense as depending on the offset and limit set I could have just 1 image
      # raise InvalidSubImagesError, 'Hardly any images returned by LLM' if sub_images_ids.length < 2
    end

    # Creates or updates the composite image source, storing metadata and composite photo path
    def self.create_or_update_info_source(
      llm_interaction,
      dossier_item,
      target_dossier_asset,
      composite_photo_file_path: nil,
      batch_offset: nil,
      batch_limit: nil,
      llm_model: nil,
      prompt_template_version: nil,
      prompt_payload: nil,
      response_status: nil,
      error_message: nil,
      duration_ms: nil,
      screenshot_dimensions: nil,
      file_size_bytes: nil,
      sub_images_count: nil,
      composite_checksum: nil,
      screenshot_url: nil
    )
      info_source = find_or_create_by(
        llm_interaction_uuid: llm_interaction.uuid,
        dossier_asset_uuid: target_dossier_asset.uuid,
        realty_dossier_uuid: dossier_item.uuid,
        agency_tenant_uuid: llm_interaction.agency_tenant_uuid
      )
      info_source.update!(
        composite_image_type: 'type_a',
        composite_photo_file_path: composite_photo_file_path.to_s,
        batch_offset: batch_offset,
        batch_limit: batch_limit,
        llm_model: llm_model,
        prompt_template_version: prompt_template_version,
        prompt_payload: prompt_payload,
        response_status: response_status,
        error_message: error_message,
        duration_ms: duration_ms,
        screenshot_dimensions: screenshot_dimensions,
        file_size_bytes: file_size_bytes,
        sub_images_count: sub_images_count,
        composite_checksum: composite_checksum,
        screenshot_url: screenshot_url
      )
      info_source
    end

    # Processes composite photo data
    def self.process_composite_photo_data(photo_info_source, response_json, _target_dossier_asset)
      sub_images = response_json[SUB_IMAGE_DESCRIPTIONS_KEY] || []
      section_details = process_sub_images(sub_images)
      update_photo_info_source(photo_info_source, sub_images, section_details)
    end

    # Processes sub-images and updates RealtyAssetPhoto records
    def self.process_sub_images(sub_images)
      main_details = []
      other_details = []

      sub_images.each do |sub_image|
        photo = RealtyAssetPhoto.find_by(id: sub_image['image_id'])
        next unless photo

        update_vals = {
          flag_is_parsed_by_llm: true,
          photo_title: sub_image['catchy_title'],
          photo_description: sub_image['general_description'],
          photo_ai_desc: sub_image['general_description'],
          raw_ai_analysis: sub_image
          # updated_at: Time.current
        }

        begin
          photo.update!(update_vals)
          # Optional: categorize as main/other if needed
          main_details << photo if photo.photo_title.present? # example condition
        rescue StandardError => e
          puts "Error updating photo ID #{sub_image['image_id']}: #{e.message}"
          other_details << { id: sub_image['image_id'], error: e.message }
        end
      end

      { main: main_details, other: other_details }
    end

    # def self.process_sub_images(sub_images)
    #   main_details = []
    #   other_details = []

    #   photo_updates = sub_images.map do |sub_image|
    #     {
    #       id: sub_image[IMAGE_ID_KEY],
    #       # below is from flag_tzu - not important
    #       # flag_is_parsed_by_llm: true,
    #       photo_title: sub_image['catchy_title'],
    #       photo_description: sub_image['general_description'],
    #       # photo_ai_desc: sub_image['general_description'],
    #       # raw_ai_analysis: sub_image,
    #       updated_at: Time.current
    #     }
    #   end

    #   upsert_result = RealtyAssetPhoto.upsert_all(photo_updates, unique_by: :id) if photo_updates.present?
    #   puts upsert_result
    #   { main: main_details, other: other_details }
    # end

    # Updates the photo info source with analysis data
    def self.update_photo_info_source(photo_info_source, sub_images, section_details)
      photo_info_source.update!(
        sub_images: sub_images,
        main_section_or_room_details: section_details[:main],
        other_section_or_room_details: section_details[:other]
      )
    end

    # Checks if the LLM interaction has a valid chosen response
    def self.valid_llm_interaction?(llm_interaction)
      llm_interaction.chosen_response.present?
    end
  end
end
