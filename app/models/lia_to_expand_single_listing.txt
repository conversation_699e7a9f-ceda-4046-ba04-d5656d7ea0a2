# == Schema Information
#
# Table name: llm_interaction_associations
#
#  id                               :bigint           not null, primary key
#  agency_tenant_uuid               :uuid             not null
#  associable_type                  :string           not null
#  association_metadata             :jsonb
#  association_type                 :string           not null
#  discarded_at                     :datetime
#  llm_interaction_assoc_aasm_state :string
#  llm_interaction_assoc_flags      :integer          default(0), not null
#  llm_interaction_uuid             :uuid             not null
#  translations                     :jsonb
#  uuid                             :uuid
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  associable_id                    :uuid             not null
#
# Indexes
#
#  idx_on_llm_interaction_assoc_flags_d5d80b59f6               (llm_interaction_assoc_flags)
#  index_llm_interaction_associations_on_agency_tenant_uuid    (agency_tenant_uuid)
#  index_llm_interaction_associations_on_association_type      (association_type)
#  index_llm_interaction_associations_on_discarded_at          (discarded_at)
#  index_llm_interaction_associations_on_llm_interaction_uuid  (llm_interaction_uuid)
#  index_llm_interaction_associations_on_uuid                  (uuid)
#
class LiaToExpandSingleListing < LlmInteractionAssociation
  #  this class may largely be redundant ...
  # belongs_to :sale_listing, foreign_key: :associable_id, primary_key: :uuid, optional: true
  belongs_to :realty_dossier, foreign_key: :associable_id, primary_key: :uuid, optional: true

  default_scope { where(association_type: 'to_expand_single_listing') }

  store_attribute :association_metadata, :walk_through_description, :string, default: ''
  # store_attribute :association_metadata, :uuids_of_other_similar_st_epcs, :json, default: []
  store_attribute :association_metadata, :estimated_fair_value_price, :decimal, default: 0.0
  store_attribute :association_metadata, :is_asking_price_competitive_or_overpriced, :string, default: ''
  store_attribute :association_metadata, :reasoning_content, :json, default: {}
  store_attribute :association_metadata, :property_inventory, :json, default: {}
  store_attribute :association_metadata, :sections_or_rooms_details, :json, default: {}

  def summary_console_print
    attributes.except('full_prompt', 'full_response', 'chosen_response')
  end

  # below ends up getting called from realty_dossier_instance.evaluate_with_photos_content
  def self.find_or_create_from_llm_interaction(
    llm_interaction, dossier_sale_listing, dossier_item
  )
    return unless llm_interaction.chosen_response.present?

    chosen_response_json = JSON.parse(llm_interaction.chosen_response)
    association_type = 'to_expand_single_listing'

    #     ["catchy_title",
    #  "description_short",
    #  "description_medium",
    #  "description_long",
    #  "walk_through_description",
    #  "description_bullet_points",
    #  "estimated_fair_value_price",
    #  "is_asking_price_competitive_or_overpriced",
    #  "property_inventory",
    #  "sections_or_rooms_details",
    #  "reasoning_content"]

    chosen_response_json['sections_or_rooms_details'].each do |sections_or_rooms_detail|
      sections_or_rooms_title = sections_or_rooms_detail[0]
      # relevant_dossier_asset_part = dossier_item.dossier_asset_parts.find_or_create_by(
      #   asset_part_slug: sections_or_rooms_title.parameterize
      # )

      relevant_dossier_asset_part = dossier_item.primary_dossier_asset.dossier_asset_parts.find_or_create_by(
        asset_part_slug: sections_or_rooms_title.parameterize
      )
      realty_asset_photo_uuids = sections_or_rooms_detail[1]['realty_asset_photo_uuids'] || []
      unique_features = sections_or_rooms_detail[1]['unique_features'] || {}
      asset_part_size = sections_or_rooms_detail[1]['size'] || ''
      asset_part_type = sections_or_rooms_detail[1]['type'].downcase || 'unknown'
      # valid_values = DossierAssetPart.asset_part_types.keys
      # asset_part_type = 'unknown' unless valid_values.include?(asset_part_type)
      relevant_dossier_asset_part.update!(
        realty_asset_photo_uuids: realty_asset_photo_uuids,
        asset_part_unique_features: unique_features,
        raw_llm_json: sections_or_rooms_detail,
        asset_part_size: asset_part_size,
        asset_part_title: sections_or_rooms_title.titleize,
        asset_part_type: asset_part_type
      )
    end

    # TODO: - once info_sources on dossier_asset is set up
    # will want to connect the primary_dossier_asset to
    #  an instance of info_source

    if dossier_item.primary_dossier_asset.present?
      # instance of DossierAsset
      dossier_item.primary_dossier_asset.update!(
        # llm_interaction_uuid: llm_interaction.uuid,
        walk_through_description: chosen_response_json['walk_through_description'],
        estimated_fair_value_price: chosen_response_json['estimated_fair_value_price'],
        is_asking_price_competitive_or_overpriced: chosen_response_json['is_asking_price_competitive_or_overpriced'],
        reasoning_content: chosen_response_json['reasoning_content'],
        summary_sections_or_rooms: chosen_response_json['sections_or_rooms_details'],
        property_inventory: chosen_response_json['property_inventory']
        # catchy_title: chosen_response_json['catchy_title']
      )
    else
      puts 'No primary dossier asset'
      raise 'No primary dossier asset'
    end
    # if dossier_item.main_eval_for_primary_property.present?
    #   # instance of DossierInfoSourceForPrimaryProperty
    #   dossier_item.main_eval_for_primary_property.update!(
    #     llm_interaction_uuid: llm_interaction.uuid,
    #     walk_through_description: chosen_response_json['walk_through_description'],
    #     estimated_fair_value_price: chosen_response_json['estimated_fair_value_price'],
    #     is_asking_price_competitive_or_overpriced: chosen_response_json['is_asking_price_competitive_or_overpriced'],
    #     reasoning_content: chosen_response_json['reasoning_content'],
    #     sections_or_rooms_details: chosen_response_json['sections_or_rooms_details'],
    #     property_inventory: chosen_response_json['property_inventory']
    #     # catchy_title: chosen_response_json['catchy_title']
    #   )
    # else
    #   puts 'No main source for primary property'
    #   raise 'No main source for primary property'
    # end

    # 24 mar 2025 - below is the old way of doing things
    # Perhaps I'll keep
    responsible_association = LiaToExpandSingleListing.find_or_create_by(
      llm_interaction: llm_interaction,
      associable: dossier_item,
      association_type: association_type,
      # association_metadata: metadata,
      agency_tenant_uuid: llm_interaction.agency_tenant_uuid
    )
    responsible_association.update!(
      walk_through_description: chosen_response_json['walk_through_description'],
      estimated_fair_value_price: chosen_response_json['estimated_fair_value_price'],
      is_asking_price_competitive_or_overpriced: chosen_response_json['is_asking_price_competitive_or_overpriced'],
      reasoning_content: chosen_response_json['reasoning_content'],
      sections_or_rooms_details: chosen_response_json['sections_or_rooms_details'],
      property_inventory: chosen_response_json['property_inventory']
    )

    dossier_sale_listing.update!(
      catchy_title: chosen_response_json['catchy_title'],
      description_short: chosen_response_json['description_short'],
      description_medium: chosen_response_json['description_medium'],
      description_bullet_points: chosen_response_json['description_bullet_points'],
      description_long: chosen_response_json['description_long']
    )
    responsible_association
  end

  # def associable_type_valid?
  #   associable_type == 'SaleListing'
  # end

  # validate :associable_type_valid?, if: :new_record?
end
