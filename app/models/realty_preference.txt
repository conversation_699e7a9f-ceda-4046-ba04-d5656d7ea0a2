# == Schema Information
#
# Table name: realty_preferences
#
#  id                       :bigint           not null, primary key
#  aasm_state               :string
#  agency_tenant_uuid       :uuid
#  discarded_at             :datetime
#  extra_pref_details       :jsonb
#  generic_properties_count :integer          default(0)
#  is_primary_pref          :boolean          default(FALSE)
#  market_alignment         :integer          default("totally_unrealistic")
#  pref_area_unit           :integer          default("sqmt")
#  pref_bathrooms_max       :integer          default(0), not null
#  pref_bathrooms_min       :integer          default(0), not null
#  pref_bedrooms_max        :integer          default(0), not null
#  pref_bedrooms_min        :integer          default(0), not null
#  pref_cities              :text             default([]), is an Array
#  pref_city                :string
#  pref_currency            :string           default("EUR")
#  pref_flags               :integer          default(0), not null
#  pref_indoor_area_max     :integer          default(0)
#  pref_indoor_area_min     :integer          default(0)
#  pref_lat_lng_bounds      :jsonb
#  pref_latitude_center     :float
#  pref_locale              :string
#  pref_longitude_center    :float
#  pref_plot_area_max       :integer          default(0)
#  pref_plot_area_min       :integer          default(0)
#  pref_price_max_cents     :bigint           default(0), not null
#  pref_price_max_currency  :string           default("EUR"), not null
#  pref_price_min_cents     :bigint           default(0), not null
#  pref_price_min_currency  :string           default("EUR"), not null
#  pref_property_type       :integer          default("detached_house"), not null
#  pref_property_type_eu    :integer          default(0), not null
#  pref_property_type_uk    :integer          default(0), not null
#  pref_property_type_us    :integer          default(0), not null
#  pref_sections            :jsonb
#  pref_slug                :string
#  pref_traits              :string
#  pref_transaction_type    :integer          default("for_sale"), not null
#  preferences_description  :string
#  preferences_title        :string
#  property_type_string     :string
#  pwb_user_uuid            :uuid
#  sale_listings_count      :integer          default(0)
#  sold_listings_count      :integer          default(0)
#  translations             :jsonb
#  uuid                     :uuid
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
# Indexes
#
#  index_realty_preferences_on_agency_tenant_uuid     (agency_tenant_uuid)
#  index_realty_preferences_on_discarded_at           (discarded_at)
#  index_realty_preferences_on_pref_flags             (pref_flags)
#  index_realty_preferences_on_pref_property_type     (pref_property_type)
#  index_realty_preferences_on_pref_property_type_eu  (pref_property_type_eu)
#  index_realty_preferences_on_pref_property_type_uk  (pref_property_type_uk)
#  index_realty_preferences_on_pref_property_type_us  (pref_property_type_us)
#  index_realty_preferences_on_pref_transaction_type  (pref_transaction_type)
#  index_realty_preferences_on_pwb_user_uuid          (pwb_user_uuid)
#  index_realty_preferences_on_uuid                   (uuid)
#
class RealtyPreference < ApplicationRecord
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid')
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  has_paper_trail
  include Discard::Model
  extend Mobility
  # translates :slug, :title, :title_meta, :description, :description_meta

  # belongs_to :primary_pwb_user, optional: true, class_name: 'Pwb::User',
  #                               primary_key: 'uuid',
  #                               foreign_key: 'pwb_user_uuid'

  has_many :realty_preference_generic_properties, # -> { order 'gp_section_position_in_list asc' },
           class_name: 'RealtyPreferenceGenericProperty', primary_key: 'uuid',
           foreign_key: 'realty_preference_uuid', dependent: :destroy
  has_many :generic_properties, through: :realty_preference_generic_properties

  enum pref_transaction_type: { for_sale: 0, for_rent: 1 }
  enum pref_area_unit: { sqmt: 0, sqft: 1 }

  enum market_alignment: {
    totally_unrealistic: 0, # User preferences are far from market reality
    somewhat_unrealistic: 1, # Preferences are unrealistic but could be achievable with compromises
    borderline_realistic: 2, # Close to market conditions but slightly off
    realistic: 3,            # Matches current market conditions well
    very_realistic: 4        # Perfectly aligns with market conditions
  }

  # pref_property_type_uk
  enum pref_property_type: {
    detached_house: 0,
    semi_detached_house: 1,
    terraced_house: 2,
    bungalow: 3,
    flat_apartment: 4,
    maisonette: 5,
    studio: 6,
    cottage: 7,
    townhouse: 8,
    park_home: 9,
    new_build: 10,
    land: 11,
    commercial_property: 12,
    office_space: 13,
    retail_space: 14,
    industrial_unit: 15,
    mixed_use: 16,
    investment_property: 17
  }

  monetize :pref_price_min_cents, with_model_currency: :pref_currency
  monetize :pref_price_max_cents, with_model_currency: :pref_currency

  def pref_summary
    "#{pref_bedrooms_min}-#{pref_bedrooms_max} bedrooms, #{pref_price_min_cents / 100}-#{pref_price_max_cents / 100} #{pref_currency}, in #{pref_city || 'unspecified location'}"
  end
end
