# == Schema Information
#
# Table name: llm_interaction_associations
#
#  id                               :bigint           not null, primary key
#  agency_tenant_uuid               :uuid             not null
#  associable_type                  :string           not null
#  association_metadata             :jsonb
#  association_type                 :string           not null
#  discarded_at                     :datetime
#  llm_interaction_assoc_aasm_state :string
#  llm_interaction_assoc_flags      :integer          default(0), not null
#  llm_interaction_uuid             :uuid             not null
#  translations                     :jsonb
#  uuid                             :uuid
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  associable_id                    :uuid             not null
#
# Indexes
#
#  idx_on_llm_interaction_assoc_flags_d5d80b59f6               (llm_interaction_assoc_flags)
#  index_llm_interaction_associations_on_agency_tenant_uuid    (agency_tenant_uuid)
#  index_llm_interaction_associations_on_association_type      (association_type)
#  index_llm_interaction_associations_on_discarded_at          (discarded_at)
#  index_llm_interaction_associations_on_llm_interaction_uuid  (llm_interaction_uuid)
#  index_llm_interaction_associations_on_uuid                  (uuid)
#
class LiaToAnalysePhotos < LlmInteractionAssociation
  # 9 apr 2025 - now that I use composit_photos this class may well get deprecated
  belongs_to :realty_dossier, foreign_key: :associable_id, primary_key: :uuid, optional: true

  default_scope { where(association_type: 'to_eval_listing_photos') }

  # store_attribute :association_metadata, :uuid_of_target_photo, :string, default: ''
  store_attribute :association_metadata, :main_section_or_room_details, :json, default: {}
  store_attribute :association_metadata, :other_section_or_room_details, :json, default: []
  # store_attribute :association_metadata, :condition_or_style, :string, default: ''
  # store_attribute :association_metadata, :significant_items, :json, default: []
  # store_attribute :association_metadata, :unique_features, :json, default: []

  belongs_to :target_photo, primary_key: 'uuid', foreign_key: 'target_photo_uuid',
                            class_name: 'RealtyAssetPhoto', optional: true

  # def realty_asset_photo
  #   RealtyAssetPhoto.find_by_uuid(
  #     uuid_of_target_photo
  #   )
  # end

  def analysed_photo_summary
    {
      realty_asset_photo_uuid: target_photo&.uuid,
      main_section_or_room_details: main_section_or_room_details,
      other_section_or_room_details: other_section_or_room_details
    }
  end

  def summary_console_print
    attributes.except('full_prompt', 'full_response', 'chosen_response')
  end

  def self.find_or_create_from_llm_interaction(
    llm_interaction, photo_analysed, dossier_item
  )
    puts photo_analysed
    return unless llm_interaction.chosen_response.present?

    chosen_response_json = JSON.parse(llm_interaction.chosen_response)
    association_type = 'to_eval_listing_photos'

    responsible_association = LiaToAnalysePhotos.find_or_create_by(
      llm_interaction: llm_interaction,
      associable: dossier_item,
      association_type: association_type,
      # association_metadata: metadata,
      agency_tenant_uuid: llm_interaction.agency_tenant_uuid
    )

    other_section_or_room_details = []
    main_section_or_room_details = {}

    chosen_response_json['sections_or_rooms_in_photo'].each do |cbl_analysis|
      if cbl_analysis['is_main_section_or_room']
        main_section_or_room_details = cbl_analysis
      else
        other_section_or_room_details << cbl_analysis
      end
    end

    gpa = chosen_response_json['general_photo_analysis']
    photo_slug = "#{photo_analysed.uuid[0...6]}-#{gpa['photo_catchy_title'].parameterize}"
    photo_analysed.update!(
      flag_is_parsed_by_llm: true,
      photo_title: gpa['photo_catchy_title'],
      photo_description: gpa['photo_general_description'],
      photo_ai_desc: gpa['photo_general_description'],
      realty_asset_photo_tags: gpa['photo_tags'],
      photo_slug: photo_slug,
      raw_ai_analysis: chosen_response_json
    )
    # raw_ai_analysis
    #     ["photo_url",
    #  "photo_catchy_title",
    #  "photo_general_description",
    #  "photo_orientation",
    #  "photo_quality",
    #  "photo_style",
    #  "photo_tags",
    #  "photo_atmosphere",
    #  "photo_emotion"]
    responsible_association.update!(
      target_photo_uuid: photo_analysed.uuid,
      main_section_or_room_details: main_section_or_room_details,
      other_section_or_room_details: other_section_or_room_details
    )

    responsible_association
  end

  # def associable_type_valid?
  #   associable_type == 'SaleListing'
  # end

  # validate :associable_type_valid?, if: :new_record?
end
