# == Schema Information
#
# Table name: realty_preference_generic_properties
#
#  id                          :bigint           not null, primary key
#  agency_tenant_uuid          :uuid
#  discarded_at                :datetime
#  generic_property_uuid       :uuid
#  gp_section_position_in_list :integer
#  is_primary                  :boolean          default(TRUE)
#  realty_preference_uuid      :uuid
#  rpgp_details                :jsonb
#  rpgp_flags                  :integer          default(0), not null
#  rpgp_slug                   :string
#  rpgp_tags                   :string           default([]), is an Array
#  translations                :jsonb
#  uuid                        :uuid
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#
# Indexes
#
#  idx_on_agency_tenant_uuid_78844225c8                        (agency_tenant_uuid)
#  idx_on_generic_property_uuid_41db8cefd5                     (generic_property_uuid)
#  idx_on_realty_preference_uuid_32accb5045                    (realty_preference_uuid)
#  index_realty_preference_generic_properties_on_discarded_at  (discarded_at)
#  index_realty_preference_generic_properties_on_is_primary    (is_primary)
#  index_realty_preference_generic_properties_on_rpgp_flags    (rpgp_flags)
#  index_realty_preference_generic_properties_on_uuid          (uuid)
#
class RealtyPreferenceGenericProperty < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  belongs_to :realty_preference, optional: true, primary_key: 'uuid', foreign_key: 'realty_preference_uuid'
  belongs_to :generic_property, optional: true, primary_key: 'uuid', foreign_key: 'generic_property_uuid'
  # counter_culture :generic_property, column_name: 'generic_property_sections_count'
end
