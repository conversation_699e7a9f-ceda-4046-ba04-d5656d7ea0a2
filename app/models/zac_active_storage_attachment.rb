# == Schema Information
#
# Table name: active_storage_attachments
#
#  id          :bigint           not null, primary key
#  name        :string           not null
#  record_type :string           not null
#  created_at  :datetime         not null
#  blob_id     :bigint           not null
#  record_id   :bigint           not null
#
# Indexes
#
#  index_active_storage_attachments_on_blob_id  (blob_id)
#  index_active_storage_attachments_uniqueness  (record_type,record_id,name,blob_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (blob_id => active_storage_blobs.id)
#
class ZacActiveStorageAttachment < ActiveStorage::Attachment
  # created to get around issues with namespacing in administrate gem
  belongs_to :zac_active_storage_blob, primary_key: 'id', foreign_key: 'blob_id', optional: true
  belongs_to :zac_active_storage_variant_record, primary_key: 'id', foreign_key: 'record_id', optional: true
  # has_one_attached :image
  # has_one :zac_active_storage_attachment, foreign_key: 'record_id', primary_key: 'id'
end
