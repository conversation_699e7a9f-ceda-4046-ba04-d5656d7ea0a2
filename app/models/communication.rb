# == Schema Information
#
# Table name: communications
#
#  id                   :bigint           not null, primary key
#  aasm_state           :string
#  agency_tenant_uuid   :uuid
#  comm_flags           :integer          default(0), not null
#  comm_form_name       :string
#  comm_form_params     :jsonb
#  comm_text            :text
#  comm_type            :integer          default(0), not null
#  discarded_at         :datetime
#  extra_comm_details   :jsonb
#  origin_user_uuid     :uuid
#  package_code         :string
#  primary_assoc_type   :string
#  primary_assoc_uuid   :uuid
#  request_referrer     :string
#  secondary_assoc_type :string
#  subdomain_uuid       :uuid
#  target_user_uuid     :uuid
#  translations         :jsonb
#  uuid                 :uuid
#  viewed_at            :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  secondary_assoc_id   :bigint
#
# Indexes
#
#  index_communications_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_communications_on_comm_flags          (comm_flags)
#  index_communications_on_comm_type           (comm_type)
#  index_communications_on_discarded_at        (discarded_at)
#  index_communications_on_uuid                (uuid)
#
class Communication < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # enum comm_type: {
  #   subdomain_access_request: 0,
  #   message_for_guest: 1,
  #   general_enquiry: 2,
  #   attempted_comm: 3,
  #   gpt_enquiry: 4,
  #   persona: 5
  # }

  # has_one :gipety_submitter, foreign_key: 'communication_uuid', primary_key: 'uuid'

  # belongs_to :subdomain, foreign_key: 'subdomain_uuid', primary_key: 'uuid', optional: true
  # belongs_to :target_guest, foreign_key: 'target_guest_uuid', primary_key: 'uuid', optional: true, counter_cache: false, class_name: 'SiteGuest'
  # belongs_to :origin_guest, foreign_key: 'origin_guest_uuid', primary_key: 'uuid', optional: true, counter_cache: false, class_name: 'SiteGuest'

  # belongs_to :origin_visitor, foreign_key: 'origin_svt', primary_key: 'ahoy_visitor_token', optional: true, counter_cache: false, class_name: 'SiteVisitor'
end
