# == Schema Information
#
# Table name: llm_interactions
#
#  id                            :bigint           not null, primary key
#  aasm_state                    :string
#  agency_tenant_uuid            :uuid
#  ai_model                      :string
#  chosen_response               :jsonb
#  chosen_response_finish_reason :string
#  cloned_from_uuid              :string
#  discarded_at                  :datetime
#  endpoint                      :string
#  extra_llm_interaction_details :jsonb
#  extra_params                  :jsonb
#  flags                         :integer          default(0), not null
#  full_prompt                   :text
#  full_response                 :jsonb
#  has_errored                   :boolean          default(TRUE)
#  llm_error_message             :string
#  llm_interaction_slug          :string
#  max_tokens                    :integer
#  related_llm_interactions      :jsonb
#  response_choices              :jsonb
#  response_completion_tokens    :integer
#  response_model                :string
#  response_object               :string
#  response_prompt_tokens        :integer
#  response_total_tokens         :integer
#  result_confidence             :integer
#  temperature                   :float
#  translations                  :jsonb
#  user_uuid                     :uuid
#  uuid                          :uuid
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  response_id                   :string
#
# Indexes
#
#  index_llm_interactions_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_llm_interactions_on_discarded_at        (discarded_at)
#  index_llm_interactions_on_flags               (flags)
#  index_llm_interactions_on_uuid                (uuid)
#
class LlmInteractionWithSaleListing < LlmInteraction
  # Add any additional methods or overrides specific to this subclass here
  # default_scope { flag_is_sale_listing_interaction }
  default_scope { where(is_sale_listing_interaction: true) }

  store_attribute :extra_llm_interaction_details, :uuid_of_most_similar_st_epc, :string, default: ''
  # store_attribute :extra_llm_interaction_details, :id_of_most_similar_sold_transaction, :integer, default: 0
  # store_attribute :extra_llm_interaction_details, :ids_of_other_similar_sold_transactions, :array, default: []
  store_attribute :extra_llm_interaction_details, :uuids_of_other_similar_st_epcs, :json, default: []
  store_attribute :extra_llm_interaction_details, :estimated_fair_value_price, :decimal, default: 0.0
  store_attribute :extra_llm_interaction_details, :is_asking_price_competitive_or_overpriced, :string, default: ''
  store_attribute :extra_llm_interaction_details, :reasoning_content, :json, default: {}

  # belongs_to :sold_transaction_epc, primary_key: 'uuid', foreign_key: 'uuid_of_most_similar_st_epc', optional: true
  # Above returns nil but does not throw an error either

  def summary_console_print
    attributes.except('full_prompt', 'full_response', 'chosen_response')
  end

  def most_similar_sold_transaction_epc
    SoldTransactionEpc.find_by_uuid(uuid_of_most_similar_st_epc)
  end

  def other_similar_sold_transactions_epcs
    SoldTransactionEpc.where(uuid: uuids_of_other_similar_st_epcs)
    # uuids_of_other_similar_st_epcs.map do |uuid|
    #   SoldTransactionEpc.find_by_uuid(uuid)
    # end
  end

  def store_sale_listing_data
    # 12 mar 2025 - this does too much heavy lifting
    return unless chosen_response.present?

    chosen_response_json = JSON.parse(chosen_response)
    # self.id_of_most_similar_sold_transaction = chosen_response_json["id_of_most_similar_sold_transaction"]
    self.uuid_of_most_similar_st_epc = chosen_response_json['uuid_of_most_similar_sold_transaction']
    # self.ids_of_other_similar_sold_transactions = chosen_response_json["ids_of_other_similar_sold_transactions"]
    self.uuids_of_other_similar_st_epcs = chosen_response_json['uuids_of_other_similar_sold_transactions']
    self.estimated_fair_value_price = chosen_response_json['estimated_fair_value_price']
    self.is_asking_price_competitive_or_overpriced = chosen_response_json['is_asking_price_competitive_or_overpriced']
    self.reasoning_content = chosen_response_json['reasoning_content']

    save

    sale_listing.update(
      catchy_title: chosen_response_json['catchy_title'],
      description_short: chosen_response_json['description_short'],
      description_medium: chosen_response_json['description_medium'],
      description_bullet_points: chosen_response_json['description_bullet_points'],
      description_long: chosen_response_json['description_long']
    )
  end
end
