# == Schema Information
#
# Table name: agency_tenants
#
#  id                              :bigint           not null, primary key
#  aasm_state                      :string
#  agency_tenant_details           :jsonb
#  agency_tenant_flags             :integer          default(0), not null
#  agency_tenant_permission_flags  :integer          default(0), not null
#  agency_tenant_url               :string
#  allow_public_view               :boolean          default(FALSE), not null
#  analytics_id_type               :integer
#  available_currencies            :text             default([]), is an Array
#  available_locales               :text             default([]), is an Array
#  client_forms_count              :integer          default(0), not null
#  company_name                    :string
#  contacts_count                  :integer          default(0), not null
#  default_admin_locale            :string
#  default_client_locale           :string
#  default_currency                :string
#  discarded_at                    :datetime
#  display_name                    :string
#  domain                          :string
#  email_for_general_contact_form  :string
#  email_for_property_contact_form :string
#  email_primary                   :string
#  event_store_events_count        :integer          default(0), not null
#  guests_count                    :integer          default(0), not null
#  is_claimed                      :boolean          default(FALSE), not null
#  is_provisioned                  :boolean          default(FALSE), not null
#  messages_count                  :integer          default(0), not null
#  pages_count                     :integer          default(0), not null
#  phone_number_mobile             :string
#  phone_number_other              :string
#  phone_number_primary            :string
#  primary_api_key                 :string
#  primary_pwb_pro_user_uuid       :uuid
#  primary_pwb_user_uuid           :uuid
#  raw_css                         :text
#  realty_asset_photos_count       :integer          default(0), not null
#  realty_assets_count             :integer          default(0), not null
#  rental_listings_count           :integer          default(0), not null
#  sale_listings_count             :integer          default(0), not null
#  site_configuration              :jsonb
#  site_visitors_count             :integer          default(0), not null
#  social_media                    :jsonb
#  subdomain                       :string
#  supported_currencies            :text             default([]), is an Array
#  supported_locales               :text             default([]), is an Array
#  theme_name                      :string
#  total_photos_count              :integer          default(0), not null
#  translations                    :jsonb
#  users_count                     :integer          default(0), not null
#  uuid                            :uuid
#  versions_count                  :integer          default(0), not null
#  websites_count                  :integer          default(0), not null
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  analytics_id                    :string
#  payment_plan_id                 :integer
#  primary_address_id              :integer
#  secondary_address_id            :integer
#  site_template_id                :integer
#
# Indexes
#
#  index_agency_tenants_on_discarded_at  (discarded_at)
#  index_agency_tenants_on_uuid          (uuid)
#
class AgencyTenant < ApplicationRecord
  validates :subdomain, uniqueness: { scope: :domain, message: 'and domain combination must be unique' }
  belongs_to :primary_pwb_user, optional: true, class_name: 'Pwb::User',
                                primary_key: 'uuid',
                                foreign_key: 'primary_pwb_user_uuid'

  has_many :props,
           foreign_key: 'agency_tenant_uuid',
           primary_key: 'uuid',
           class_name: 'Pwb::Prop'

  def pu_email
    primary_pwb_user&.email
  end

  def self.unique_tenant
    # there will be only one row, and its ID must be '1'

    # TODO: - memoize
    find(1)
  rescue ActiveRecord::RecordNotFound
    # slight race condition here, but it will only happen once
    row = Agency.new
    row.id = 1
    row.save!
    row
  end
  # def self.provision(subdomain: nil)
  #   new_subdomain = subdomain || Haikunator.haikunate(0)

  #   new_subdomain = "#{Haikunator.haikunate(0)}-#{AgencyTenant.count}" if AgencyTenant.exists?(subdomain: new_subdomain)

  #   create!(
  #     subdomain: new_subdomain,
  #     domain: 'propertywebbuilder.com',
  #     is_provisioned: true,
  #     is_claimed: false
  #   )
  # end

  def self.find_or_create_for_pwb_user(pwb_user_uuid:)
    a_tenant = AgencyTenant.where(primary_pwb_user_uuid: pwb_user_uuid).first
    unless a_tenant.present?
      a_tenant = AgencyTenant.where(
        primary_pwb_user_uuid: nil,
        is_provisioned: true, is_claimed: false
      ).first
      if a_tenant.present?
        a_tenant.update!(primary_pwb_user_uuid: pwb_user_uuid,
                         is_claimed: true)
      else
        new_subdomain = Haikunator.haikunate(0)
        if AgencyTenant.exists?(subdomain: new_subdomain)
          new_subdomain = "#{Haikunator.haikunate(0)}-#{AgencyTenant.count}"
          # Haikunator.haikunate(0) + '-' +
        end
        a_tenant = AgencyTenant.create!(
          primary_pwb_user_uuid: pwb_user_uuid,
          subdomain: new_subdomain,
          domain: 'propertywebbuilder.com',
          is_claimed: true
        )
      end

    end
    a_tenant
  end
  # store_attribute :details, :domain_comparisons, :json, default: { "hhh" => {} }

  # store_attribute :details, :first_gpt_question, :json, default: { "hhh" => {} }
  # store_attribute :details, :first_gpt_answer, :json, default: { "hhh" => {} }
end
