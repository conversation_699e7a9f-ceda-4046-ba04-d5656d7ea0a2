# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItem < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true

  # store_attribute :json_representation, :scrape_failure_message, :string, default: ''
  # store_attribute :json_representation, :request_object, :json, default: {}
  # store_attribute :json_representation, :is_valid_scrape, :boolean, default: false

  # store_attribute :json_representation, :all_page_images, :json, default: {}
  # store_attribute :json_representation, :all_page_images_length, :integer, default: 0

  # store_attribute :json_representation, :title, :string, default: ''
  # store_attribute :json_representation, :description, :string, default: ''
  # store_attribute :json_representation, :page_locale_code, :string, default: ''
  # # store_attribute :json_representation, :scraper_host, :string, default: ''
  # store_attribute :json_representation, :scraper_mapping_name, :string, default: ''
  # store_attribute :json_representation, :last_items_history, :json, default: {}
  # store_attribute :json_representation, :client_provided_html, :text, default: ''
  # store_attribute :json_representation, :full_html_before_js, :text, default: ''
  # store_attribute :json_representation, :full_html_before_js_length, :integer, default: 0
  # include RealtyScrapers::SharedScraperHelpers
  # For methods like get_scraper_mapping

  # visitable :psq_visit
  # belongs_to :visit, foreign_key: "psq_visit_id", optional: true, class_name: "Ahoy::Visit", counter_cache: false
  has_paper_trail
  include Discard::Model
  # extend Mobility
  # translates :locale_title

  include FlagShihTzu
  has_flags 1 => :scrape_is_zoopla,
            2 => :scrape_is_rightmove,
            3 => :scrape_is_onthemarket,
            4 => :scrape_is_jitty,
            5 => :scrape_is_purplebricks,
            6 => :scrape_is_gumtree,
            7 => :scrape_is_nestoria,
            8 => :scrape_is_bbb,
            9 => :scrape_is_craigslist,
            10 => :scrape_is_idealista,
            11 => :scrape_is_getthedata,
            12 => :scrape_is_buenavista,
            13 => :scrape_is_zillow,
            14 => :scrape_is_capeverdeproperty,
            15 => :scrape_is_redfin,
            :column => 'scrape_item_flags'

  # Feb 2025 - TODO - implement scraper_host association
  def scraper_host
    AdHocData::ScraperHost.find_or_create_by(
      {
        ad_hoc_data_item_slug: 'default'
      }
    )
  end

  # belongs_to :scraped_page, primary_key: 'uuid', foreign_key: 'scraped_page_uuid', optional: true
  # counter_culture :scraped_page, column_name: 'scrapes_count'
  # belongs_to :scraper_host, primary_key: 'uuid', foreign_key: 'scraper_host_uuid', optional: true
  # counter_culture :scraper_host, column_name: 'scrapes_count'

  # where is_realty_search_scrape below will apply
  has_many :summary_listings, primary_key: 'uuid',
                              foreign_key: 'scrape_item_uuid'
  belongs_to :realty_search_query, primary_key: 'uuid', foreign_key: 'realty_search_query_uuid',
                                   optional: true, class_name: 'RealtySearchQuery'

  # otherwise will belong to a sale_listing
  belongs_to :sale_listing, primary_key: 'uuid', foreign_key: 'sale_listing_uuid',
                            optional: true, class_name: 'SaleListing'
  # and potentialy a realty_asset
  belongs_to :last_realty_asset,
             primary_key: 'uuid', foreign_key: 'realty_asset_uuid',
             optional: true, class_name: 'RealtyAsset'

  # validates_presence_of :scraped_page_uuid
  # validates_presence_of :scraper_host_uuid

  # def is_search_scrape
  #   added is_realty_search_scrape in a migration to replace this
  #   is_paid_scrape
  # end

  def self.find_or_create_for_h2c(retrieval_end_point, is_search_url: false)
    retrieval_uri = URI(retrieval_end_point)
    # The assumption below is that any query strings, et cetera
    # are not relevant.
    # Will have to add exceptions here as I come across them
    clean_url = "#{retrieval_uri.scheme}://#{retrieval_uri.host}#{retrieval_uri.port && retrieval_uri.port != retrieval_uri.default_port ? ":#{retrieval_uri.port}" : ''}#{retrieval_uri.path}"
    # base_url = "#{retrieval_uri.scheme}://#{retrieval_uri.host}"
    scrape_unique_url = "#{clean_url}#h2c"
    scrape_unique_url = retrieval_end_point if is_search_url
    sci = ScrapeItem.find_or_create_by!(scrape_unique_url: scrape_unique_url)
    sci.update!(
      is_realty_search_scrape: is_search_url,
      scrapable_url: retrieval_end_point,
      scrape_uri_scheme: retrieval_uri.scheme,
      scrape_uri_host: retrieval_uri.host
    )
    sci
  end

  def top_level_url
    "#{scrape_uri_scheme}://#{scrape_uri_host}"
  end

  # 03 july 2025: this method seems currently only used by ContextualRecordFromGetthedata
  # Might be worth using it more
  def retrieve_and_set_content_with_playwright(
    playwright_connector, force_retrieval: false
  )
    content_item_to_check_for = 'full_content_after_js'
    if !force_retrieval && send(content_item_to_check_for).present?
      puts '[INFO] Content already present, skipping retrieval.'
      return send(content_item_to_check_for)
    end

    raise ArgumentError, 'playwright_connector must be an instance of ScraperConnectors::LocalPlaywright' unless playwright_connector.is_a?(ScraperConnectors::LocalPlaywright)

    import_uri = URI(scrapable_url)
    puts "[INFO] Retrieving content for URI: #{import_uri}"

    content_from_connector = playwright_connector.retrieve_data_from_connector(
      import_uri,
      # include_trailing_slash: include_trailing_slash,
      is_search_scrape: is_realty_search_scrape
    )

    content_key = content_from_connector[:scrape_item_target]
    content_value = content_from_connector[:returned_content]

    if content_from_connector[:error]
      update!(is_valid_scrape: false, scrape_failure_message: content_from_connector[:error])
      raise "Scraping failed: #{content_from_connector[:error]}"
    end

    if content_is_json
      update_column(content_key, content_value.to_json)
    else
      update_column(content_key, content_value.to_s)
    end

    if !scrape_is_rightmove && !(content_value.present? && content_value.length > 1000)
      update!(is_valid_scrape: false)
      raise "#{content_key} is unavailable or too short"
    end

    update!(is_valid_scrape: true)
    save!
    send(content_key)
  rescue StandardError => e
    update!(is_valid_scrape: false, scrape_failure_message: e.message)
    puts "[ERROR] Failed to retrieve content: #{e.class} - #{e.message}"
    raise
  end

  def retrieve_and_set_content_object(
    scraper_connector_name, include_trailing_slash: false,
    force_retrieval: false # , is_search_scrape: false
  )
    self.update!(scraper_connector_name: scraper_connector_name)
    # I should have named the incoming parameter something else
    # to avoid confusion with the self.scraper_connector_name
    # but above works for now so will leave it
    puts "[INFO] Starting retrieve_and_set_content_object for ScrapeItem ID: \\#{id}"
    content_item_to_check_for = 'full_content_after_js'
    if !force_retrieval && send(content_item_to_check_for).present?
      puts "[INFO] Content already present for \\#{content_item_to_check_for}, skipping retrieval."
      return send(content_item_to_check_for)
    end

    import_uri = URI(scrapable_url)
    puts "[INFO] Import URI: \\#{import_uri}"
    # TODO: add and set import_url_cannonical for scrape here....
    # canonical_import_url = RealtyScrapers::SharedScraperHelpers.get_canonical_url_from_url(import_url)

    scraper_connector = if scraper_connector_name.present?
                          puts "[INFO] Initializing scraper connector: \\#{scraper_connector_name}"
                          scraper_connector_name.constantize.new(self)
                        else
                          puts '[WARN] Scraper connector name not present'
                          # Nov 2024 - will now expect scraper_connector_name
                          # to be set previously.

                          # TODO: - refactor so scraper_connector_name needs to be set

                          # set_scraper_mapping_and_connector import_uri # , log_object
                        end
    # Was previously called full_html_before_js - but in the case of via_bvh_json
    # I will be retrieving and setting json not html
    puts "[INFO] Calling retrieve_data_from_connector on: \\#{scraper_connector.class.name if scraper_connector}"
    content_from_connector = scraper_connector.retrieve_data_from_connector(
      import_uri,
      include_trailing_slash: include_trailing_slash
      # is_search_scrape: is_search_scrape
    )

    # puts "[INFO] Content from connector: \\#{content_from_connector.inspect}"
    # retrieve_data_from_connector above was previously called retrieve_noko_doc_string
    # which didn't make a load of sense
    content_key = content_from_connector[:scrape_item_target]
    # might want to do a check to ensure content_item_to_check_for
    # is the same as content_key..
    content_value = content_from_connector[:returned_content]
    # If you are using ActiveRecord:
    # self.send(\"\\#{content_key}=\", content_value)
    # self.save # or

    puts "[INFO] Saving content to column: \\#{content_key} (length: \\#{content_value&.length})"
    if content_is_json
      # update_column(content_key, content_value.to_json)
      # update_column(content_key, JSON.parse(content_value))
      # 27 may 2025 - with bvh content it seems just saving as string worksu
      update_column(content_key, content_value.to_s)
    else
      update_column(content_key, content_value.to_s)
    end

    if !scrape_is_rightmove && !(content_value.present? && content_value.length > 1000)
      puts "[ERROR] Content for \\#{content_key} is unavailable or too short (length: \\#{content_value&.length})"
      # 18 may 2025
      # For rightmove, we don't want to raise an error if the content is too short
      # because I save json there and the length will return how many keys there are
      update!(is_valid_scrape: false)
      raise "\\#{content_key} is unavailable or too short"
    end

    # update_column(content_key, content_value.to_s)
    puts "[INFO] Content saved successfully."
    save!
    full_content_before_js
  rescue StandardError => e
    puts "[ERROR] Exception in retrieve_and_set_content_object: \\#{e.class} - \\#{e.message}"
    puts e.backtrace.join("\n")
    raise
  end

  def get_property_hash_from_html
    doc = Nokogiri::HTML(full_content_before_js)

    # Extract basic metadata that doesn't require LLM
    property_data = {
      'import_url' => doc.at('link[rel="canonical"]')&.[]('href'),
      'title' => doc.at('title')&.text, # Page title
      'description' => doc.at('meta[name="description"]')&.[]('content'), # Meta description
      'og_image' => doc.at('meta[property="og:image"]')&.[]('content'), # Open Graph image
      'og_url' => doc.at('meta[property="og:url"]')&.[]('content'), # Open Graph URL
      'og_type' => doc.at('meta[property="og:type"]')&.[]('content'), # Open Graph type
      'price' => doc.at('[itemprop="price"]')&.[]('content') || doc.at('.price')&.text&.strip, # Try structured data first
      'address' => doc.at('[itemprop="streetAddress"]')&.text&.strip, # Structured address
      'property_type' => doc.at('[itemprop="propertyType"]')&.text&.strip || doc.at('.property-type')&.text&.strip
    }

    # Extract image URLs
    property_data[:listing_image_urls] = extract_image_urls(doc)

    property_data.stringify_keys!
  end

  def retrieve_scraped_content_for_llm
    extracted_content = get_property_hash_from_html
    if scrape_is_zoopla
      {
        scraped_html: '',
        extracted_content: extracted_content,
        other_texts: extract_nextjs_scripts
      }
    else
      {
        scraped_html: full_content_before_js,
        extracted_content: extracted_content,
        other_texts: ''
      }
    end
  end

  def extract_nextjs_scripts
    # inspired by
    # https://github.com/savedra1/rental-alerts/blob/main/rental-alerts/utils/constants.py#L14
    doc = Nokogiri::HTML(full_content_before_js)
    bad_strings = ['"])</script>', '<script>self.__next_f.push([1,"']

    puts bad_strings
    nextjs_data = ''
    doc.xpath('//script').each do |script|
      # next unless script.text.include?('self.__next_f.push')
      next unless script.text.include?('self.__next_f.push([1,"')

      nextjs_data += script.to_s
    end

    bad_strings.each do |bad_string|
      nextjs_data = nextjs_data.gsub(bad_string, '')
    end

    nextjs_data_as_lines = nextjs_data.gsub('\\"', '"').gsub('\\n', "\n")

    # listing_file_path = File.join(
    #   Rails.root, 'db', "external_sites/zoopla-com/#{scrapable_url.parameterize}.script.json"
    # )
    # # Ensure the directory exists
    # FileUtils.mkdir_p(File.dirname(listing_file_path))
    # # Write the file
    # File.write(listing_file_path, nextjs_data_as_lines)

    # nextjs_data_as_lines.lines.each do |line|
    #   puts line if line.include?('latitude')
    # end

    nextjs_data_as_lines
  end

  def sale_listing_from_scrape_item
    standardised_listing_hash = property_hash_from_scrape_item
    # march 2025 - property_hash_from_scrape_item is particular
    # to a given subclass of ScrapeItem and is not implemented here
    standardised_listing_hash['import_url'] = scrapable_url
    standardised_listing_hash['listing_data']['import_url'] = scrapable_url
    standardised_listing_hash['asset_data']['import_url'] = scrapable_url
    # request_host = 'guise_listing_server'
    creator = Creators::Full::FullListingAndAssetCreator.new
    sale_listing = creator.create_from_standardised_hash(
      standardised_listing_hash
      # request_host
    )
    if standardised_listing_hash['raw_otm_data'].present?
      # 18 mar 2025 - below is a temporary thing to enable
      # me to dig deeper into otm data
      sale_listing.update!(
        raw_otm_data: standardised_listing_hash['raw_otm_data']
      )
    end
    sale_listing
  end

  private

  def extract_image_urls(doc)
    # Try different selectors commonly used for property images
    selectors = [
      'div.swiper-slide picture img',
      'div.property-images img',
      '.gallery img',
      '.carousel img',
      '.slider img',
      'img[data-src]',
      'img.property-image'
    ]

    selectors.each do |selector|
      images = doc.css(selector).map { |img| img['src'] || img['data-src'] }.compact
      return images unless images.empty?
    end

    # Fallback: look for any image that might be a property image
    doc.css('img').map { |img| img['src'] }.compact.select { |src| src.match(/\.(jpg|jpeg|png|webp)/i) }
  end
end
