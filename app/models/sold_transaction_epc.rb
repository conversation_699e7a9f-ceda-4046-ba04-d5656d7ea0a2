# == Schema Information
#
# Table name: sold_transaction_epcs
#
#  id                           :bigint           not null, primary key
#  agency_tenant_uuid           :uuid
#  association_certainty        :integer          default(50), not null
#  best_estimated_price         :integer          default(0)
#  best_estimated_price_off_by  :integer          default(0)
#  days_difference_epc_and_sale :integer          default(0)
#  discarded_at                 :datetime
#  epc_address                  :string
#  epc_detail_uuid              :uuid
#  epc_inspection_date          :date
#  is_best_combo                :boolean          default(FALSE)
#  is_outlier                   :boolean          default(FALSE)
#  outlier_reason               :string           default("")
#  outlier_score                :integer          default(0)
#  postcode_area_uuid           :uuid
#  price_per_floor_area_meter   :integer          default(0)
#  price_per_habitable_room     :integer          default(0)
#  realty_asset_uuid            :uuid
#  sold_transaction_date        :date
#  sold_transaction_uuid        :uuid
#  st_address                   :string
#  st_epc_extra_details         :jsonb
#  st_epc_flags                 :integer          default(0), not null
#  st_epc_latitude              :float
#  st_epc_longitude             :float
#  st_epc_outcode               :string
#  st_epc_postcode              :string
#  st_epc_price_predictions     :jsonb
#  st_epc_uprn                  :string
#  uuid                         :uuid
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#
# Indexes
#
#  index_sold_transaction_epcs_on_agency_tenant_uuid     (agency_tenant_uuid)
#  index_sold_transaction_epcs_on_discarded_at           (discarded_at)
#  index_sold_transaction_epcs_on_epc_detail_uuid        (epc_detail_uuid)
#  index_sold_transaction_epcs_on_realty_asset_uuid      (realty_asset_uuid)
#  index_sold_transaction_epcs_on_sold_transaction_uuid  (sold_transaction_uuid)
#  index_sold_transaction_epcs_on_st_epc_flags           (st_epc_flags)
#  index_sold_transaction_epcs_on_uuid                   (uuid)
#
class SoldTransactionEpc < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # include FlagShihTzu
  # has_flags 1 => :fully_processed_flg,
  #           :column => 'postcode_cluster_flags'
  scope :within_reasonable_days_difference, -> { where(days_difference_epc_and_sale: -1000..1000) }
  scope :within_days_difference, ->(min, max) { where(days_difference_epc_and_sale: min..max) }
  scope :with_habitable_rooms, lambda {
    joins(:epc_detail)
      .where('epc_details.number_habitable_rooms IS NOT NULL AND epc_details.number_habitable_rooms > 0')
    # .where.not(epc_details: { number_habitable_rooms: nil })
  }

  scope :with_heated_rooms, lambda {
    joins(:epc_detail)
      .where('epc_details.number_heated_rooms IS NOT NULL AND epc_details.number_heated_rooms > 0')
  }

  scope :with_non_zero_total_floor_area, lambda {
    joins(:epc_detail)
      .where('epc_details.total_floor_area IS NOT NULL AND epc_details.total_floor_area > 0')
  }

  reverse_geocoded_by :st_epc_latitude, :st_epc_longitude # , address: :street_address
  # adds methods like:
  # PostcodeArea.last.nearbys(0.3).length
  # PostcodeArea.last.distance_from([40.714,-100.234])
  # PostcodeArea.near([40.71, -100.23], 50)              # venues within 50 miles of a point

  store_attribute :st_epc_extra_details, :outlier_details, :json, default: {}

  store_attribute :st_epc_price_predictions, :st_sold_price, :string, default: ''
  store_attribute :st_epc_price_predictions, :st_predicted_price_a, :string, default: ''
  store_attribute :st_epc_price_predictions, :st_predicted_price_a_explanation, :string, default: ''
  store_attribute :st_epc_price_predictions, :st_predicted_price_off_by_a, :integer, default: 0
  store_attribute :st_epc_price_predictions, :st_predicted_price_b, :string, default: ''
  store_attribute :st_epc_price_predictions, :st_predicted_price_b_explanation, :string, default: ''
  store_attribute :st_epc_price_predictions, :st_predicted_price_off_by_b, :integer, default: 0

  delegate :st_uprn, :st_tenure, :st_property_type, :formatted_sold_price,
           :sold_date, :sold_price_cents, :sold_price_currency,
           :st_postal_code, :st_age_of_property, to: :sold_transaction

  delegate :epc_uprn, :total_floor_area, :number_heated_rooms,
           :number_habitable_rooms, :construction_age_band, :current_energy_rating,
           :potential_energy_rating, :current_energy_efficiency, :potential_energy_efficiency,
           :tenure,
           to: :epc_detail, prefix: false, allow_nil: false

  belongs_to :sold_transaction, class_name: 'SoldTransaction', foreign_key: 'sold_transaction_uuid', primary_key: :uuid, optional: false
  has_one :postcode_area, through: :sold_transaction # , source: :postcode_area

  belongs_to :epc_detail, class_name: 'EpcDetail', foreign_key: 'epc_detail_uuid', primary_key: :uuid, optional: true
  belongs_to :realty_asset, primary_key: 'uuid', foreign_key: 'realty_asset_uuid', optional: true

  # could run below to discard records which are not actually similar
  # SoldTransactionEpc.where("similarity(LOWER(st_address), LOWER(epc_address)) < 0.9").discard_all
  # pretty much all of below are correct:
  def self.confirmed_st_epcs
    SoldTransactionEpc.where('similarity(LOWER(st_address), LOWER(epc_address)) > 0.9')
  end
  #
  # SoldTransactionEpc.confirmed_st_epcs.pluck(:st_address, :epc_address)

  def self.find_or_create_and_update(epc_detail:, transaction:, uprn_detail:)
    if epc_detail.uprn.present?
      #  uprn_detail&.pcds != epc_detail.epc_postal_code
      target_realty_asset = RealtyAsset.find_or_create_by(reference: epc_detail.uprn)
      target_realty_asset.update!(
        details: epc_detail,
        related_urls: {
          # rightmove_tracking_history: epc_detail['detailUrl'],
          # rightmove_import_url:
        },
        has_sold_transactions: true,
        postal_code: epc_detail.epc_postal_code,
        # st_epc_outcode: epc_detail.epc_outcode,
        latitude: uprn_detail&.geog&.latitude,
        longitude: uprn_detail&.geog&.longitude,
        street_address: epc_detail.address.upcase
        # count_bedrooms: epc_detail['bedrooms'],
        # prop_type_key: epc_detail['propertyType']&.downcase
      )
      epc_detail.update!(
        realty_asset_uuid: target_realty_asset&.uuid
      )
      transaction.update!(
        st_latitude: uprn_detail&.geog&.latitude,
        st_longitude: uprn_detail&.geog&.longitude,
        realty_asset_uuid: target_realty_asset&.uuid,
        st_uprn: epc_detail.uprn
      )
      # target_realty_asset.process_rightmove_address(target_realty_asset.street_address)
    end
    st_epc = find_or_create_by(
      epc_detail:, sold_transaction: transaction
    )
    days_difference_epc_and_sale = (epc_detail.inspection_date - transaction.sold_date).to_i
    # price_per_habitable_room price_per_floor_area_meter
    price_per_floor_area_meter = if epc_detail.total_floor_area&.positive?
                                   transaction.sold_price_cents / epc_detail.total_floor_area
                                 else
                                   0
                                 end
    price_per_habitable_room = if epc_detail.number_habitable_rooms&.positive?
                                 transaction.sold_price_cents / epc_detail.number_habitable_rooms
                               else
                                 0
                               end
    st_epc.update!(
      days_difference_epc_and_sale: days_difference_epc_and_sale,
      price_per_floor_area_meter: price_per_floor_area_meter / 100,
      price_per_habitable_room: price_per_habitable_room / 100,
      realty_asset_uuid: target_realty_asset&.uuid,
      # TODO- add below when I migrate it
      # postcode_area_uuid: epc_detail.postcode_area_uuid,
      st_epc_postcode: epc_detail.epc_postal_code,
      st_epc_outcode: epc_detail.epc_outcode,
      association_certainty: 65,
      st_epc_latitude: uprn_detail&.geog&.latitude,
      st_epc_longitude: uprn_detail&.geog&.longitude,
      st_epc_uprn: epc_detail.uprn,
      epc_address: epc_detail.address.upcase,
      st_address: transaction.st_long_address,
      sold_transaction_date: transaction.sold_date,
      epc_inspection_date: epc_detail.inspection_date
    )

    st_epc
  end

  def self.find_price_per_habitable_room_outliers(threshold = 2)
    find_outliers(field_name: :price_per_habitable_room, threshold: threshold)
  end

  def self.find_price_per_floor_area_meter_outliers(threshold = 2)
    find_outliers(field_name: :price_per_floor_area_meter, threshold: threshold)
  end

  def self.find_outliers(field_name: :price_per_floor_area_meter, threshold: 2)
    # Step 1: Calculate mean and standard deviation using PostgreSQL
    stats_query = SoldTransactionEpc.select(
      "AVG(#{field_name}) AS mean, STDDEV(#{field_name}) AS stddev"
    ).take # Use .take instead of [0] or .first to avoid potential ordering issues

    mean = stats_query.mean
    stddev = stats_query.stddev

    # Step 2: Define the outlier range
    lower_bound = mean - (threshold * stddev)
    upper_bound = mean + (threshold * stddev)

    # Step 3: Query for outliers using the dynamic field name
    outliers = SoldTransactionEpc.where(
      "#{field_name} < :lower_bound OR #{field_name} > :upper_bound",
      lower_bound: lower_bound,
      upper_bound: upper_bound
    )

    puts "Analyzing field: #{field_name}"
    puts "Mean: #{mean.round(2)}, Stddev: #{stddev.round(2)}, Lower bound: #{lower_bound.round(2)}, Upper bound: #{upper_bound.round(2)}"
    puts "#{outliers.count} outliers found for #{field_name} with a standard deviation threshold of #{threshold}"

    outliers.each do |outlier|
      field_value = outlier.send(field_name)

      outlier_info = {
        field_name: field_name.to_s, # Store field name as string in JSON
        field_value: field_value,
        mean: mean.round(2),
        stddev: stddev.round(2),
        threshold: threshold,
        lower_bound: lower_bound.round(2),
        upper_bound: upper_bound.round(2),
        deviation: ((field_value - mean) / stddev).abs.round(2)
      }

      updated_outlier_details = outlier.outlier_details || {}
      outlier_score = outlier.outlier_score + 1
      updated_outlier_details[field_name] = outlier_info
      outlier.update(
        outlier_score: outlier_score,
        is_outlier: true, outlier_details: updated_outlier_details
      )
    end
    # # Step 4: Return outliers with additional info, now using the dynamic field
    # outliers.map do |outlier|
    #   field_value = outlier.send(field_name) # Dynamically get the field value
    #   {
    #     id: outlier.id,
    #     field_value: field_value, # Use a generic name like field_value
    #     deviation: ((field_value - mean) / stddev).abs.round(2) # Calculate deviation based on the dynamic field
    #   }
    # end
  rescue StandardError => e
    Rails.logger.error "Error in find_outliers for field #{field_name}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n") if Rails.env.development?
    [] # Return empty array in case of error, or handle error as appropriate
  end

  # Need to make a decision on below:
  # def avm_features
  #   dd = {
  #     st_tenure: st_tenure || 'Unknown',
  #     st_property_type: st_property_type || 'Unknown',
  #     st_postal_code: st_postal_code || 'Unknown',
  #     st_age_of_property: st_age_of_property || 'Unknown',

  #     total_floor_area: total_floor_area || 'Unknown',
  #     number_heated_rooms: number_heated_rooms || 'Unknown',
  #     number_habitable_rooms: number_habitable_rooms || 'Unknown',
  #     construction_age_band: construction_age_band || 'Unknown',
  #     current_energy_rating: current_energy_rating || 'Unknown',
  #     month: sold_date&.strftime('%b') || 'Unknown',
  #     sold_date:,
  #     sold_price_cents:
  #   }
  #   puts dd
  #   dd
  # end
end
