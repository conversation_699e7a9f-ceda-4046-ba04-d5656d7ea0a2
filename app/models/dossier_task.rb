# == Schema Information
#
# Table name: dossier_tasks
#
#  id                      :bigint           not null, primary key
#  agency_tenant_uuid      :uuid
#  completed               :boolean          default(FALSE), not null
#  description             :text
#  discarded_at            :datetime
#  dossier_task_aasm_state :string
#  dossier_task_flags      :integer          default(0), not null
#  due_date                :date
#  realty_dossier_uuid     :uuid             not null
#  task_details            :jsonb
#  title                   :string           not null
#  uuid                    :uuid             not null
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#
# Indexes
#
#  index_dossier_tasks_on_agency_tenant_uuid   (agency_tenant_uuid)
#  index_dossier_tasks_on_discarded_at         (discarded_at)
#  index_dossier_tasks_on_dossier_task_flags   (dossier_task_flags)
#  index_dossier_tasks_on_realty_dossier_uuid  (realty_dossier_uuid)
#  index_dossier_tasks_on_uuid                 (uuid)
#

class DossierTask < ApplicationRecord
  include Discard::Model

  acts_as_tenant :agency_tenant,
                 foreign_key: 'agency_tenant_uuid',
                 primary_key: 'uuid',
                 counter_cache: false

  belongs_to :realty_dossier,
             class_name: 'Real<PERSON><PERSON><PERSON><PERSON>',
             foreign_key: 'realty_dossier_uuid',
             primary_key: 'uuid',
             optional: false

  store_attribute :task_details, :comparison_id, :string, default: ''
  store_attribute :task_details, :is_primary, :boolean, default: true
  store_attribute :task_details, :pictures, :json, default: []

  #  {
  #   "id": 1745510237310,
  #   "text": "viewing",
  #   "completed": false,
  #   "comparisonId": "e3fdc793-52c0-4a71-bc2b-a6780652e1f2",
  #   "isPrimary": false,
  #   "dueDate": "2025-04-24",
  #   "pictures": [],
  #   "createdAt": "2025-04-24T15:57:17.310Z"
  # }

  # validates :uuid, presence: true, uniqueness: true
  validates :realty_dossier_uuid, presence: true
  validates :title, presence: true
end
