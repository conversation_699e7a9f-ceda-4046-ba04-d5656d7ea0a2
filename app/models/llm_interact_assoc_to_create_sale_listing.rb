# == Schema Information
#
# Table name: llm_interaction_associations
#
#  id                               :bigint           not null, primary key
#  agency_tenant_uuid               :uuid             not null
#  associable_type                  :string           not null
#  association_metadata             :jsonb
#  association_type                 :string           not null
#  discarded_at                     :datetime
#  llm_interaction_assoc_aasm_state :string
#  llm_interaction_assoc_flags      :integer          default(0), not null
#  llm_interaction_uuid             :uuid             not null
#  translations                     :jsonb
#  uuid                             :uuid
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  associable_id                    :uuid             not null
#
# Indexes
#
#  idx_on_llm_interaction_assoc_flags_d5d80b59f6               (llm_interaction_assoc_flags)
#  index_llm_interaction_associations_on_agency_tenant_uuid    (agency_tenant_uuid)
#  index_llm_interaction_associations_on_association_type      (association_type)
#  index_llm_interaction_associations_on_discarded_at          (discarded_at)
#  index_llm_interaction_associations_on_llm_interaction_uuid  (llm_interaction_uuid)
#  index_llm_interaction_associations_on_uuid                  (uuid)
#
class LlmInteractAssocToCreateSaleListing < LlmInteractionAssociation
  # Mar 11 2025
  # This class is used to create a SaleListing from a LlmInteraction
  # Might rename to LiaToCreateSaleListing
  belongs_to :sale_listing, foreign_key: :associable_id, primary_key: :uuid, optional: true

  # before_validation :set_sale_listing, if: :new_record?

  def self.find_or_create_from_llm_interaction(llm_interaction, standardised_listing_hash)
    standardised_listing_hash.stringify_keys!
    unique_url = standardised_listing_hash['import_url'] || standardised_listing_hash['listing_data']['import_url']

    unique_uri = URI(unique_url)
    listing_unique_url = "#{unique_uri.scheme}://#{unique_uri.host}#{unique_uri.port && unique_uri.port != unique_uri.default_port ? ":#{unique_uri.port}" : ''}#{unique_uri.path}"
    listing_unique_url = "#{listing_unique_url}#h2c"
    # sci = ScrapeItem.find_or_create_by!(scrape_unique_url: scrape_unique_url)

    target_listing = SaleListing.find_or_create_by(
      {
        unique_url: listing_unique_url
      }
    )

    if target_listing.realty_asset.blank?
      # If we have a uprn we need to use that to create the asset
      new_realty_asset = RealtyAsset.create!
      new_realty_asset.reload
      target_listing.realty_asset_uuid = new_realty_asset.uuid
      # target_listing.save!
    end
    target_listing.save!

    association_type = 'created_from_scrape'
    metadata = {}
    create!(
      llm_interaction: llm_interaction,
      associable: target_listing,
      association_type: association_type,
      association_metadata: metadata,
      agency_tenant_uuid: llm_interaction.agency_tenant_uuid
    )
  end

  # def self.create_from_llm_interaction_uuid(llm_interaction_uuid, sale_listing_uuid, association_type, metadata = {})
  #   llm_interaction = LlmInteraction.find_by(uuid: llm_interaction_uuid)
  #   sale_listing = SaleListing.find_by(uuid: sale_listing_uuid)

  #   create_from_llm_interaction(llm_interaction, sale_listing, association_type, metadata)
  # end

  private

  # def set_sale_listing
  #   return unless associable_type == 'SaleListing' && associable.present?

  #   self.sale_listing = SaleListing.find_by(uuid: associable_uuid)
  # end

  def associable_type_valid?
    associable_type == 'SaleListing'
  end

  validate :associable_type_valid?, if: :new_record?
end
