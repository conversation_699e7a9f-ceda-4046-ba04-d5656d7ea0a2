# == Schema Information
#
# Table name: realty_search_queries
#
#  id                        :bigint           not null, primary key
#  aasm_state                :string
#  agency_tenant_uuid        :uuid             not null
#  agency_uuid               :uuid
#  average_results_count     :integer
#  default_sort_criteria     :integer          default(0), not null
#  discarded_at              :datetime
#  indoor_area_max           :integer          default(0)
#  indoor_area_min           :integer          default(0)
#  latest_scrape_item_uuid   :uuid
#  locale                    :string
#  parent_query_uuid         :uuid
#  plot_area_max             :integer          default(0)
#  plot_area_min             :integer          default(0)
#  property_type             :integer          default(0), not null
#  property_type_string      :string
#  query_rating              :integer          default(50), not null
#  query_source_portal       :integer          default("unknown"), not null
#  query_traits              :string
#  realty_search_details     :jsonb
#  realty_search_flags       :integer          default(0), not null
#  search_area_unit          :integer          default(0)
#  search_bathrooms_max      :integer          default(0), not null
#  search_bathrooms_min      :integer          default(0), not null
#  search_bedrooms_max       :integer          default(0), not null
#  search_bedrooms_min       :integer          default(0), not null
#  search_cities             :text             default([]), is an Array
#  search_city               :string
#  search_currency           :string           default("GBP")
#  search_lat_lng_bounds     :jsonb
#  search_latitude_center    :float
#  search_longitude_center   :float
#  search_postcode           :string
#  search_price_max          :bigint           default(0), not null
#  search_price_min          :bigint           default(0), not null
#  search_query_slug         :string
#  search_query_url          :string
#  search_query_url_template :string
#  search_source             :integer          default(0), not null
#  summary_listings_count    :integer
#  transaction_type          :integer          default(0), not null
#  translations              :jsonb
#  uuid                      :uuid
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#
# Indexes
#
#  index_realty_search_queries_on_agency_uuid            (agency_uuid)
#  index_realty_search_queries_on_default_sort_criteria  (default_sort_criteria)
#  index_realty_search_queries_on_discarded_at           (discarded_at)
#  index_realty_search_queries_on_property_type          (property_type)
#  index_realty_search_queries_on_query_rating           (query_rating)
#  index_realty_search_queries_on_realty_search_flags    (realty_search_flags)
#  index_realty_search_queries_on_search_source          (search_source)
#  index_realty_search_queries_on_transaction_type       (transaction_type)
#  index_realty_search_queries_on_uuid                   (uuid)
#
class RealtySearchQuery < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  has_many :dossier_searches, primary_key: 'uuid', foreign_key: 'realty_search_query_uuid'
  has_many :realty_dossiers, through: :dossier_searches # , source: :page

  has_many :scrape_items, primary_key: 'uuid', foreign_key: 'realty_search_query_uuid'
  has_many :summary_listings, primary_key: 'uuid', foreign_key: 'realty_search_query_uuid'
  # belongs_to :postcode_area, optional: true, primary_key: 'uuid', foreign_key: 'postcode_area_uuid'
  # counter_culture :postcode_area, column_name: 'ra_photos_count'

  # extend Mobility
  # translates :photo_title, :photo_ai_desc

  # # acts_as_list column: :sort_order

  # include FlagShihTzu
  # has_flags 1 => :query_is_zoopla,
  #           2 => :query_is_rightmove,
  #           3 => :query_is_onthemarket,
  #           4 => :query_is_jitty,
  #           5 => :query_is_purplebricks,
  #           6 => :query_is_gumtree,
  #           7 => :query_is_nestoria,
  #           8 => :query_is_bbb,
  #           9 => :query_is_craigslist,
  #           10 => :query_is_idealista,
  #           :column => 'realty_search_flags'

  enum query_source_portal: {
    unknown: 0,
    zoopla: 1,
    rightmove: 2,
    onthemarket: 3,
    jitty: 4,
    purplebricks: 5,
    gumtree: 6,
    nestoria: 7,
    bbb: 8,
    craigslist: 9,
    idealista: 10
  }

  # ZOOPLA_BASE_URL = 'https://www.zoopla.co.uk/for-sale/'.freeze

  MIN_ROOMS_VALUE = 0
  MAX_ROOMS_VALUE = 90
  DEFAULT_ROOMS_VALUE = 1
  MIN_SALE_PRICE_VALUE = 5000
  MAX_SALE_PRICE_VALUE = 500_000_000
  DEFAULT_SALE_PRICE_VALUE = 300_000

  # Might need a find_or_create_unique_query for each subclass
  # when I support more than just bvh queries
  def self.find_or_create_unique_query(new_query_params)
    new_query_params = new_query_params.symbolize_keys

    # Need to figure out a plan for below and for property type too
    new_query_params[:search_postcode] = new_query_params[:search_postcode] || 'cv11'
    # # I could avoid below by using splat operator on new_query_params
    # # incoming params:
    # required_keys = %i[
    #   search_bedrooms_min
    #   search_bedrooms_max
    #   search_bathrooms_min
    #   search_bathrooms_max
    #   search_price_min
    #   search_price_max
    # ]

    # raise ArgumentError, "new_query_params must contain exactly the keys: #{required_keys.join(', ')}" if new_query_params.keys.sort != required_keys.sort

    # Eventually I might replace below with SearchQueryRange class instances
    # But if I end up not doing that then I should test below well.
    # An advantage of using SearchQueryRanges is that I can more easily
    # notch them up of down to broaden or narrow a search
    new_query_params[:search_bedrooms_min] = sanity_check_param(
      new_query_params[:search_bedrooms_min],
      RealtySearchQuery::MIN_ROOMS_VALUE, new_query_params[:search_bedrooms_max],
      RealtySearchQuery::DEFAULT_ROOMS_VALUE
    )
    # There is the chance the new_query_params[:search_bedrooms_max] I used above
    # will be overwritten below - but I'm not too worried about that
    # Should check with chatGPT though if it can identify more concerning issues
    new_query_params[:search_bedrooms_max] = sanity_check_param(
      new_query_params[:search_bedrooms_max],
      new_query_params[:search_bedrooms_min], RealtySearchQuery::MAX_ROOMS_VALUE,
      RealtySearchQuery::DEFAULT_ROOMS_VALUE + 1
    )
    new_query_params[:search_bathrooms_min] = sanity_check_param(
      new_query_params[:search_bathrooms_min],
      RealtySearchQuery::MIN_ROOMS_VALUE, RealtySearchQuery::MAX_ROOMS_VALUE,
      RealtySearchQuery::DEFAULT_ROOMS_VALUE
    )
    new_query_params[:search_bathrooms_max] = sanity_check_param(
      new_query_params[:search_bathrooms_max],
      RealtySearchQuery::MIN_ROOMS_VALUE, RealtySearchQuery::MAX_ROOMS_VALUE,
      RealtySearchQuery::DEFAULT_ROOMS_VALUE + 1
    )

    new_query_params[:search_price_min] = sanity_check_param(
      new_query_params[:search_price_min],
      RealtySearchQuery::MIN_SALE_PRICE_VALUE, RealtySearchQuery::MAX_SALE_PRICE_VALUE,
      RealtySearchQuery::DEFAULT_SALE_PRICE_VALUE - 50_000
    )
    new_query_params[:search_price_max] = sanity_check_param(
      new_query_params[:search_price_max],
      RealtySearchQuery::MIN_SALE_PRICE_VALUE, RealtySearchQuery::MAX_SALE_PRICE_VALUE,
      RealtySearchQuery::DEFAULT_SALE_PRICE_VALUE + 50_000
    )

    new_query = find_or_create_by(new_query_params)
    new_query
  end

  def self.sanity_check_param(incoming_param, min_value, max_value, default_value)
    return incoming_param if incoming_param.present? && incoming_param >= min_value && incoming_param <= max_value

    default_value
  end

  # def construct_zoopla_url(params)
  #   query_params = {
  #     q: params[:location] || search_city || search_postcode,
  #     min_price: params[:min_price] || search_price_min,
  #     max_price: params[:max_price] || search_price_max,
  #     min_beds: params[:min_beds] || search_bedrooms_min,
  #     max_beds: params[:max_beds] || search_bedrooms_max,
  #     property_type: params[:property_type] || property_type_string,
  #     include_sold: false
  #   }.compact

  #   uri = URI(ZOOPLA_BASE_URL)
  #   uri.query = URI.encode_www_form(query_params)
  #   uri.to_s
  # end
end
