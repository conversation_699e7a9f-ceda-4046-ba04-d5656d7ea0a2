# == Schema Information
#
# Table name: generic_properties
#
#  id                                    :bigint           not null, primary key
#  agency_tenant_uuid                    :uuid
#  archived                              :boolean          default(FALSE)
#  area_unit                             :integer          default(0)
#  buyers_guide                          :jsonb
#  city                                  :string
#  cloned_from_uuid                      :string
#  constructed_area                      :float            default(0.0), not null
#  constructed_area_sq_ft                :decimal(10, 2)   default(0.0), not null
#  constructed_area_sq_mt                :decimal(10, 2)   default(0.0), not null
#  country                               :string
#  design_style                          :string
#  details_of_rooms                      :jsonb
#  discarded_at                          :datetime
#  energy_performance                    :float
#  energy_rating                         :integer
#  extra_generic_property_details        :jsonb
#  floor_of_flat                         :string
#  floors_in_building                    :integer
#  furnished                             :boolean          default(FALSE)
#  generic_property_currency             :string
#  generic_property_description          :string
#  generic_property_features             :jsonb
#  generic_property_flags                :integer          default(0), not null
#  generic_property_gen_prompt           :text
#  generic_property_photos_count         :integer          default(0), not null
#  generic_property_reference            :string
#  generic_property_sections_count       :integer          default(0), not null
#  generic_property_slug                 :string
#  generic_property_tags                 :string           default([]), is an Array
#  generic_property_title                :string
#  generic_property_type                 :integer          default(0), not null
#  generic_property_type_europe          :integer          default(0), not null
#  generic_property_type_uk              :integer          default("detached_house"), not null
#  generic_property_type_us              :integer          default(0), not null
#  gp_position_in_list                   :integer
#  high_sale_price_cents                 :bigint           default(0), not null
#  high_sale_price_currency              :string           default("EUR"), not null
#  highest_sale_price_cents              :bigint           default(0), not null
#  highest_sale_price_currency           :string           default("EUR"), not null
#  is_ai_generated_gp                    :boolean          default(TRUE)
#  is_median_for_outcode_area            :boolean          default(FALSE)
#  is_median_for_postcode_area           :boolean          default(FALSE)
#  latitude                              :float
#  leasehold_or_freehold                 :integer          default("leasehold")
#  leasehold_years_remaining             :integer          default(0)
#  likely_service_charge_yearly_cents    :bigint           default(0), not null
#  likely_service_charge_yearly_currency :string           default("EUR"), not null
#  llm_interaction_uuid                  :uuid
#  longitude                             :float
#  low_sale_price_cents                  :bigint           default(0), not null
#  low_sale_price_currency               :string           default("EUR"), not null
#  lowest_sale_price_cents               :bigint           default(0), not null
#  lowest_sale_price_currency            :string           default("EUR"), not null
#  map_url                               :string
#  no_of_bathrooms                       :float            default(0.0), not null
#  no_of_bedrooms                        :integer          default(0), not null
#  no_of_garages                         :integer          default(0), not null
#  no_of_toilets                         :integer          default(0), not null
#  outcode_area_uuid                     :uuid
#  plot_area                             :float            default(0.0), not null
#  plot_area_sq_ft                       :decimal(10, 2)   default(0.0), not null
#  plot_area_sq_mt                       :decimal(10, 2)   default(0.0), not null
#  postal_code                           :string
#  postcode_area_uuid                    :uuid
#  potential_rental_daily_cents          :bigint           default(0), not null
#  potential_rental_daily_currency       :string           default("EUR"), not null
#  potential_rental_monthly_cents        :bigint           default(0), not null
#  potential_rental_monthly_currency     :string           default("EUR"), not null
#  primary_neighborhood                  :string
#  prop_origin_key                       :string           default(""), not null
#  prop_state_key                        :string           default(""), not null
#  prop_type_key                         :string           default(""), not null
#  province                              :string
#  published                             :boolean          default(FALSE)
#  realty_preferences_count              :integer          default(0)
#  reasonable_sale_price_cents           :bigint           default(0), not null
#  reasonable_sale_price_currency        :string           default("EUR"), not null
#  region                                :string
#  related_urls                          :jsonb
#  representativeness_score              :integer          default(0)
#  sellers_guide                         :jsonb
#  similar_neighborhoods                 :string           default([]), is an Array
#  site_visitor_token                    :string
#  street_name                           :string
#  street_number                         :string
#  translations                          :jsonb
#  usefulness_rating                     :integer
#  user_uuid                             :uuid
#  uuid                                  :uuid
#  versions_count                        :integer          default(0), not null
#  visible                               :boolean          default(FALSE)
#  year_construction                     :integer          default(0), not null
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#
# Indexes
#
#  index_generic_properties_on_agency_tenant_uuid          (agency_tenant_uuid)
#  index_generic_properties_on_constructed_area            (constructed_area)
#  index_generic_properties_on_discarded_at                (discarded_at)
#  index_generic_properties_on_generic_property_flags      (generic_property_flags)
#  index_generic_properties_on_generic_property_reference  (generic_property_reference)
#  index_generic_properties_on_generic_property_slug       (generic_property_slug)
#  index_generic_properties_on_no_of_bathrooms             (no_of_bathrooms)
#  index_generic_properties_on_no_of_bedrooms              (no_of_bedrooms)
#  index_generic_properties_on_no_of_garages               (no_of_garages)
#  index_generic_properties_on_no_of_toilets               (no_of_toilets)
#  index_generic_properties_on_outcode_area_uuid           (outcode_area_uuid)
#  index_generic_properties_on_plot_area                   (plot_area)
#  index_generic_properties_on_postcode_area_uuid          (postcode_area_uuid)
#  index_generic_properties_on_published                   (published)
#  index_generic_properties_on_uuid                        (uuid)
#  index_generic_properties_on_visible                     (visible)
#
class GenericProperty < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  store_attribute :extra_generic_property_details, :generic_property_gen_data, :json, default: {}

  belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true

  has_one :geo_cluster, class_name: 'GeoCluster', primary_key: 'uuid', foreign_key: 'benchmark_property_uuid'

  has_one :median_postcode_area, class_name: 'PostcodeArea', primary_key: 'uuid', foreign_key: 'median_generic_property_uuid'
  belongs_to :postcode_area, class_name: 'PostcodeArea', primary_key: 'uuid',
                             foreign_key: 'postcode_area_uuid', optional: true
  # counter_culture :postcode_area, column_name: 'generic_properties_count'

  has_many :generic_property_photos, -> { order 'gp_photo_position_in_list asc' },
           class_name: 'GenericPropertyPhoto', primary_key: 'uuid',
           foreign_key: 'generic_property_uuid', dependent: :destroy
  # has_many :ordered_visible_generic_property_photos, lambda {
  #   not_flag_is_hidden.order('gp_photo_position_in_list asc')
  #   # where(visible_for_sale_listing: true).order("gp_photo_position_in_list asc")
  # }, class_name: 'GenericPropertyPhoto', primary_key: 'uuid',
  #    foreign_key: 'generic_property_uuid', dependent: :destroy

  has_many :generic_property_sections, -> { order 'gp_section_position_in_list asc' },
           class_name: 'GenericPropertySection', primary_key: 'uuid',
           foreign_key: 'generic_property_uuid', dependent: :destroy

  # has_many :realty_preference_generic_properties, # -> { order 'gp_section_position_in_list asc' },
  #          class_name: 'RealtyPreferenceGenericProperty', primary_key: 'uuid',
  #          foreign_key: 'generic_property_uuid', dependent: :destroy
  # has_many :realty_preferences, through: :realty_preference_generic_properties

  has_many :sold_transactions, primary_key: 'uuid', foreign_key: 'generic_property_uuid'

  # enum representativeness_score: {
  #   not_representative: 0, # Synthetic listing does not reflect real market trends at all
  #   poorly_representative: 1,           # Major discrepancies between synthetic data and real market data
  #   somewhat_representative: 2,         # Captures some trends but is noticeably off in key areas
  #   fairly_representative: 3,           # Represents the market reasonably well but with minor inaccuracies
  #   highly_representative: 4,           # Closely aligns with real market trends and data
  #   perfectly_representative: 5         # Nearly indistinguishable from real market data
  # }

  enum leasehold_or_freehold: { leasehold: 0, freehold: 1 }

  enum generic_property_type_uk: {
    detached_house: 0,
    semi_detached_house: 1,
    terraced_house: 2,
    bungalow: 3,
    flat_apartment: 4,
    maisonette: 5,
    studio: 6,
    cottage: 7,
    townhouse: 8,
    park_home: 9,
    new_build: 10,
    land: 11,
    commercial_property: 12,
    office_space: 13,
    retail_space: 14,
    industrial_unit: 15,
    mixed_use: 16,
    investment_property: 17
  }
  # monetize :price_sale_current_cents, with_model_currency: :generic_property_currency

  monetize :reasonable_sale_price_cents # , with_model_currency: :generic_property_currency
  monetize :high_sale_price_cents, with_model_currency: :generic_property_currency
  monetize :low_sale_price_cents, with_model_currency: :generic_property_currency
  monetize :highest_sale_price_cents, with_model_currency: :generic_property_currency
  monetize :lowest_sale_price_cents, with_model_currency: :generic_property_currency
  monetize :likely_service_charge_yearly_cents, with_model_currency: :generic_property_currency

  monetize :potential_rental_monthly_cents, with_model_currency: :generic_property_currency
  monetize :potential_rental_daily_cents, with_model_currency: :generic_property_currency

  # monetize :high_sale_price, with_model_currency: :generic_property_currency
  # monetize :low_sale_price, with_model_currency: :generic_property_currency
  # monetize :highest_sale_price, with_model_currency: :generic_property_currency
  # monetize :lowest_sale_price, with_model_currency: :generic_property_currency
  # monetize :likely_service_charge_yearly, with_model_currency: :generic_property_currency

  # monetize :potential_rental_monthly, with_model_currency: :generic_property_currency
  # monetize :potential_rental_daily, with_model_currency: :generic_property_currency

  def as_json_for_display(options = nil)
    as_json(
      { only: %w[
          uuid design_style
          generic_property_title generic_property_description
          no_of_toilets no_of_garages
          no_of_bathrooms no_of_bedrooms
          area_unit plot_area constructed_area
          street_number postal_code
          primary_neighborhood
          latitude longitude city region country street_name
        ],
        # formatted_display_price
        # formatted_constructed_area
        methods: %w[] }.merge(
          options || {}
        )
    )
  end
end
