# == Schema Information
#
# Table name: contextual_records
#
#  id                      :bigint           not null, primary key
#  aasm_state              :string
#  agency_tenant_uuid      :uuid
#  contxt_flags            :integer          default(0), not null
#  contxt_outcode          :string
#  contxt_postcode         :string
#  contxt_type             :integer          default(0), not null
#  discarded_at            :datetime
#  extra_contxt_details    :jsonb
#  latest_scrape_item_uuid :uuid
#  raw_contxt              :text
#  record_source_url       :string
#  translations            :jsonb
#  uuid                    :uuid
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#
# Indexes
#
#  index_contextual_records_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_contextual_records_on_contxt_flags        (contxt_flags)
#  index_contextual_records_on_contxt_type         (contxt_type)
#  index_contextual_records_on_discarded_at        (discarded_at)
#  index_contextual_records_on_uuid                (uuid)
#
class ContextualRecord < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  has_many :dossier_asset_contextual_records, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid'
  has_many :dossier_assets, through: :dossier_asset_contextual_records
  has_one :scrape_item, primary_key: 'latest_scrape_item_uuid', foreign_key: 'uuid'

  # include FlagShihTzu
  # has_flags 1 => :record_is_getthedata,
  #           :column => 'contxt_flags'
end
