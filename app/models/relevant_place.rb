# == Schema Information
#
# Table name: relevant_places
#
#  id                         :bigint           not null, primary key
#  aasm_state                 :string
#  added_by_system            :boolean          default(TRUE)
#  added_by_user              :boolean          default(FALSE)
#  agency_tenant_uuid         :uuid
#  awaiting_osm_update        :boolean          default(TRUE)
#  city                       :string
#  country                    :string
#  discarded_at               :datetime
#  other_place_source         :string
#  place_coords               :geography        point, 4326
#  postal_code                :string
#  province                   :string
#  region                     :string
#  related_urls               :jsonb
#  relevancy_importance       :integer          default(0), not null
#  relevant_for               :integer          default(0), not null
#  relevant_place_description :string
#  relevant_place_details     :jsonb
#  relevant_place_flags       :integer          default(0), not null
#  relevant_place_geometry    :jsonb
#  relevant_place_latitude    :float
#  relevant_place_longitude   :float
#  relevant_place_slug        :string
#  relevant_place_tags        :string           default([]), is an Array
#  relevant_place_title       :string
#  street_name                :string
#  street_number              :string
#  translations               :jsonb
#  uuid                       :uuid
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  google_place_id            :string
#  osm_id                     :string
#  other_place_id             :string
#
# Indexes
#
#  idx_on_relevant_place_latitude_relevant_place_longi_4d264488e9  (relevant_place_latitude,relevant_place_longitude)
#  index_relevant_places_on_agency_tenant_uuid                     (agency_tenant_uuid)
#  index_relevant_places_on_discarded_at                           (discarded_at)
#  index_relevant_places_on_place_coords                           (place_coords) USING gist
#  index_relevant_places_on_relevant_place_flags                   (relevant_place_flags)
#  index_relevant_places_on_relevant_place_slug                    (relevant_place_slug)
#  index_relevant_places_on_uuid                                   (uuid)
#
class RelevantPlace < ApplicationRecord
  include Json::RelevantPlaceJson
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # store_attribute :extra_cluster_details, :neighborhood_cons, :json, default: {}
  has_many :geo_cluster_places, foreign_key: 'relevant_place_uuid', primary_key: :uuid
  has_many :geo_clusters, through: :geo_cluster_places

  reverse_geocoded_by :relevant_place_latitude, :relevant_place_longitude # , address: :street_address
  # adds methods like:
  # RelevantPlace.last.nearbys(0.3).length
  # RelevantPlace.last.distance_from([40.714,-100.234])

  # def self.find_or_create_from_postcode_areas(postcode_areas)
  def associate_with_geo_cluster(geo_cluster)
    geo_cluster.geo_cluster_places.find_or_create_by!(
      relevant_place_uuid: uuid
    )
  end

  def self.update_or_create_from_g_places_data(g_places_data, geo_cluster)
    places_res = []
    # _latitude, _longitude, _radius = 2000, _types = %w[grocery_or_supermarket hospital park restaurant school transit_station], _api_key = ENV['GOOGLE_PLACES_API_KEY'])
    if g_places_data['status'] == 'OK'
      g_places_data['results'].each do |place|
        relevant_place_latitude = place['geometry']['location']['lat']
        relevant_place_longitude = place['geometry']['location']['lng']

        cluster_latitude = geo_cluster.center_latitude
        cluster_longitude = geo_cluster.center_longitude
        distance_in_mi = Geocoder::Calculations.distance_between(
          [relevant_place_latitude, relevant_place_longitude],
          [cluster_latitude, cluster_longitude],
          # If you want kilometers, pass :km as an argument
          units: :mi
        )

        attributes = {
          google_place_id: place['place_id'],
          relevant_place_latitude:, # place['geometry']['location']['lat'],
          relevant_place_longitude:, # place['geometry']['location']['lng'],
          relevant_place_title: place['name'],
          street_number: place['plus_code']&.dig('compound_code')&.split(' ')&.first, # This might be approximate or not available
          street_name: place['vicinity'],
          relevant_place_description: place['types'].join(', '),
          relevant_place_details: place
          # relevant_place_details: {
          #   rating: place['rating'],
          #   user_ratings_total: place['user_ratings_total']
          # }
        }
        # Find or initialize by place_id since it's unique
        existing_place = RelevantPlace.find_or_initialize_by(google_place_id: place['place_id'])
        existing_place.update(attributes)
        cluster_place = existing_place.associate_with_geo_cluster(geo_cluster)
        place_within_cluster = geo_cluster.point_in_cluster?(relevant_place_latitude, relevant_place_longitude)
        mile_or_miles = distance_in_mi.round(1) == 1 ? 'mile' : 'miles'
        formatted_distance = "#{distance_in_mi.round(1)} #{mile_or_miles} away"
        cluster_place.update(
          distance_between: formatted_distance,
          distance_between_value: distance_in_mi,
          distance_unit: 'mi',
          place_within_cluster:,
          place_within_one_mile: distance_in_mi <= 1
        )
        places_res << existing_place
      end
    else
      Rails.logger.error("Google Places API returned status: #{g_places_data['status']}")
    end
    places_res
  end

  def self.update_or_create_from_overpass_data(overpass_data, geo_cluster)
    places_res = []

    # Map node IDs to their coordinates for easy lookup
    node_coordinates = {}
    overpass_data.each do |element|
      node_coordinates[element['id']] = { lat: element['lat'], lon: element['lon'] } if element['type'] == 'node'
    end

    # Process each element in the Overpass data
    overpass_data.each do |element|
      next unless element['type'] == 'way' # || element['type'] == 'node'

      if element['type'] == 'way'
        # Calculate the centroid of the way if it's a polygon
        latitudes = element['nodes'].map { |node_id| node_coordinates.dig(node_id, :lat) }.compact
        longitudes = element['nodes'].map { |node_id| node_coordinates.dig(node_id, :lon) }.compact

        next if latitudes.empty? || longitudes.empty?

        relevant_place_latitude = latitudes.sum / latitudes.size
        relevant_place_longitude = longitudes.sum / longitudes.size
        # elsif element['type'] == 'node'
        #   relevant_place_latitude = element['lat']
        #   relevant_place_longitude = element['lon']
      end

      cluster_latitude = geo_cluster.center_latitude
      cluster_longitude = geo_cluster.center_longitude
      distance_in_mi = Geocoder::Calculations.distance_between(
        [relevant_place_latitude, relevant_place_longitude],
        [cluster_latitude, cluster_longitude],
        units: :mi
      )

      attributes = {
        other_place_id: element['id'],
        relevant_place_latitude:,
        relevant_place_longitude:,
        relevant_place_title: element.dig('tags', 'name'),
        street_number: nil, # Overpass data might not have street number
        street_name: element.dig('tags', 'addr:street'),
        relevant_place_description: element['tags']&.map { |k, v| "#{k}: #{v}" }&.join(', '),
        relevant_place_details: element
      }

      # Find or initialize by other_place_id since it's unique
      existing_place = RelevantPlace.find_or_initialize_by(other_place_id: element['id'])
      existing_place.update(attributes)
      cluster_place = existing_place.associate_with_geo_cluster(geo_cluster)
      place_within_cluster = geo_cluster.point_in_cluster?(relevant_place_latitude, relevant_place_longitude)
      mile_or_miles = distance_in_mi.round(1) == 1 ? 'mile' : 'miles'
      formatted_distance = "#{distance_in_mi.round(1)} #{mile_or_miles} away"
      cluster_place.update(
        distance_between: formatted_distance,
        distance_between_value: distance_in_mi,
        distance_unit: 'mi',
        place_within_cluster:,
        place_within_one_mile: distance_in_mi <= 1
      )
      # can have a task that will use awaiting_osm_update to decide if it should
      # run something like:
      # results = Geocoder.search([RelevantPlace.last.relevant_place_latitude, RelevantPlace.last.relevant_place_longitude])
      places_res << existing_place
    end

    places_res
  end

  # def self.as_detailed_json
  #   map(&:as_detailed_json)
  # end
end
