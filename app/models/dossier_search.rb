# == Schema Information
#
# Table name: dossier_searches
#
#  id                        :bigint           not null, primary key
#  agency_tenant_uuid        :uuid
#  discarded_at              :datetime
#  dossier_search_aasm_state :string
#  dossier_search_details    :jsonb
#  dossier_search_flags      :integer          default(0), not null
#  is_most_useful            :boolean          default(FALSE)
#  realty_dossier_uuid       :uuid
#  realty_search_query_uuid  :uuid
#  uuid                      :uuid
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#
# Indexes
#
#  index_dossier_searches_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_dossier_searches_on_discarded_at              (discarded_at)
#  index_dossier_searches_on_dossier_search_flags      (dossier_search_flags)
#  index_dossier_searches_on_realty_dossier_uuid       (realty_dossier_uuid)
#  index_dossier_searches_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_dossier_searches_on_uuid                      (uuid)
#
class DossierSearch < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # belongs_to :sold_transaction, class_name: 'SoldTran<PERSON>ction', foreign_key: 'sold_transaction_uuid', primary_key: :uuid, optional: false
  # has_one :postcode_area, through: :sold_transaction # , source: :postcode_area

  belongs_to :realty_dossier, class_name: 'RealtyDossier', foreign_key: 'realty_dossier_uuid', primary_key: :uuid, optional: true
  belongs_to :realty_search_query, primary_key: 'uuid', foreign_key: 'realty_search_query_uuid', optional: true

  validates :realty_dossier_uuid, presence: true
  validates :realty_search_query_uuid, presence: true
  # validates :postcode_cluster_slug, presence: true, uniqueness: true
  # validates :percent_diff_to_cluster_price, presence: true

  # Ensure the combination of realty_dossier_uuid and realty_search_query_uuid is unique
  validates :realty_dossier_uuid, uniqueness: { scope: :realty_search_query_uuid, message: 'and realty_search_query combination must be unique' }

  validates :is_most_useful, uniqueness: { scope: :realty_dossier_uuid }, if: -> { is_most_useful }

  # before_save :ensure_only_one_most_useful

  # def ensure_only_one_most_useful
  #   return unless is_most_useful

  #   DossierSearch.where(realty_dossier_uuid: realty_dossier_uuid)
  #                .where.not(id: id)
  #                .update_all(is_most_useful: false)
  # end

  # could also enforce above with a unique index:
  # class AddUniqueIndexToDossierSearches < ActiveRecord::Migration[6.0]
  #   def change
  #     add_index :dossier_searches, [:realty_dossier_uuid], unique: true, where: "is_most_useful = true", name: "index_unique_most_useful_dossier_search"
  #   end
  # end
end
