# == Schema Information
#
# Table name: uprn_details
#
#  id                           :bigint           not null, primary key
#  agency_tenant_uuid           :uuid
#  bua22cd                      :string
#  buasd11cd                    :string
#  ced23cd                      :string
#  ctry23cd                     :string
#  cty23cd                      :string
#  discarded_at                 :datetime
#  eer20cd                      :string
#  epc_details_count            :integer          default(0), not null
#  geog                         :geography        point, 4326
#  geom                         :geometry         geometry, 27700
#  grid_gb1_e                   :integer
#  grid_gb1_n                   :integer
#  hlth19cd                     :string
#  imd19ind                     :integer
#  itl21cd                      :string
#  lad23cd                      :string
#  lep21cd1                     :string
#  lep21cd2                     :string
#  lsoa21cd                     :string
#  msoa21cd                     :string
#  npark16cd                    :string
#  oa21cd                       :string
#  oac21ind                     :string
#  parncp23cd                   :string
#  pcds                         :string
#  pcon24cd                     :string
#  pfa21cd                      :string
#  realty_asset_uuid            :uuid
#  rgn23cd                      :string
#  ruc21ind                     :string
#  sicbl22cd                    :string
#  sold_transaction_epcs_count  :integer          default(0), not null
#  ttwa15cd                     :string
#  uprn                         :string
#  uprn_extra_details           :jsonb
#  uprn_latitude                :float
#  uprn_longitude               :float
#  uprn_possible_address        :string
#  uprn_possible_address_source :string
#  uprn_possible_paon           :string
#  uprn_possible_saon           :string
#  uprn_possible_street         :string
#  wd23cd                       :string
#  wz11cd                       :string
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#
# Indexes
#
#  index_uprn_details_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_uprn_details_on_discarded_at        (discarded_at)
#  index_uprn_details_on_geog                (geog) USING gist
#  index_uprn_details_on_geom                (geom) USING gist
#  index_uprn_details_on_realty_asset_uuid   (realty_asset_uuid)
#  index_uprn_details_on_uprn                (uprn) UNIQUE
#
class UprnDetail < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # include Discard::Model
  # has_paper_trail

  # I probably should get rid of realty_asset_uuid as I'm using below instead
  has_one :realty_asset, foreign_key: 'ra_uprn', primary_key: 'uprn'
  has_many :sold_transactions, foreign_key: 'st_uprn', primary_key: 'uprn'
  has_many :epc_details, foreign_key: 'uprn', primary_key: 'uprn'
  # belongs_to :epc_detail, class_name: 'EpcDetail', foreign_key: 'uprn', primary_key: 'uprn', optional: true
  # belongs_to :sold_transaction, class_name: 'EpcDetail', foreign_key: 'uprn', primary_key: 'uprn', optional: true

  # scope :real, -> { where(is_synthetic_transaction: false) }
  validates :uprn, uniqueness: { message: 'must be unique' }

  reverse_geocoded_by :uprn_latitude, :uprn_longitude # , address: :street_address
end
