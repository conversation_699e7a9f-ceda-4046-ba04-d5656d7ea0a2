# == Schema Information
#
# Table name: pwb_props
#
#  id                                            :integer          not null, primary key
#  active_from                                   :datetime
#  agency_tenant_uuid                            :uuid
#  agency_tenant_uuid                            :uuid
#  archived                                      :boolean          default(FALSE)
#  area_unit                                     :integer          default(0)
#  available_to_rent_from                        :datetime
#  available_to_rent_till                        :datetime
#  city                                          :string
#  commission_cents                              :bigint           default(0), not null
#  commission_currency                           :string           default("EUR"), not null
#  constructed_area                              :float            default(0.0), not null
#  count_bathrooms                               :float            default(0.0), not null
#  count_bedrooms                                :integer          default(0), not null
#  count_garages                                 :integer          default(0), not null
#  count_toilets                                 :integer          default(0), not null
#  country                                       :string
#  currency                                      :string
#  deleted_at                                    :datetime
#  description                                   :text             default(NULL)
#  energy_performance                            :float
#  energy_rating                                 :integer
#  flags                                         :integer          default(0), not null
#  for_rent_long_term                            :boolean          default(FALSE)
#  for_rent_short_term                           :boolean          default(FALSE)
#  for_sale                                      :boolean          default(FALSE)
#  furnished                                     :boolean          default(FALSE)
#  hide_map                                      :boolean          default(FALSE)
#  highlighted                                   :boolean          default(FALSE)
#  latitude                                      :float
#  longitude                                     :float
#  obscure_map                                   :boolean          default(FALSE)
#  plot_area                                     :float            default(0.0), not null
#  portals_enabled                               :boolean          default(FALSE)
#  postal_code                                   :string
#  price_rental_monthly_current_cents            :bigint           default(0), not null
#  price_rental_monthly_current_currency         :string           default("EUR"), not null
#  price_rental_monthly_for_search_cents         :bigint           default(0), not null
#  price_rental_monthly_for_search_currency      :string           default("EUR"), not null
#  price_rental_monthly_high_season_cents        :bigint           default(0), not null
#  price_rental_monthly_high_season_currency     :string           default("EUR"), not null
#  price_rental_monthly_low_season_cents         :bigint           default(0), not null
#  price_rental_monthly_low_season_currency      :string           default("EUR"), not null
#  price_rental_monthly_original_cents           :bigint           default(0), not null
#  price_rental_monthly_original_currency        :string           default("EUR"), not null
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  price_sale_original_cents                     :bigint           default(0), not null
#  price_sale_original_currency                  :string           default("EUR"), not null
#  prop_origin_key                               :string           default(""), not null
#  prop_state_key                                :string           default(""), not null
#  prop_type_key                                 :string           default(""), not null
#  province                                      :string
#  reference                                     :string
#  region                                        :string
#  reserved                                      :boolean          default(FALSE)
#  service_charge_yearly_cents                   :bigint           default(0), not null
#  service_charge_yearly_currency                :string           default("EUR"), not null
#  sold                                          :boolean          default(FALSE)
#  street_address                                :string
#  street_name                                   :string
#  street_number                                 :string
#  title                                         :string           default(NULL)
#  uuid                                          :uuid
#  visible                                       :boolean          default(FALSE)
#  year_construction                             :integer          default(0), not null
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#  prop_id                                       :integer          not null
#
# Indexes
#
#  index_pwb_props_on_archived                            (archived)
#  index_pwb_props_on_flags                               (flags)
#  index_pwb_props_on_for_rent_long_term                  (for_rent_long_term)
#  index_pwb_props_on_for_rent_short_term                 (for_rent_short_term)
#  index_pwb_props_on_for_sale                            (for_sale)
#  index_pwb_props_on_highlighted                         (highlighted)
#  index_pwb_props_on_latitude_and_longitude              (latitude,longitude)
#  index_pwb_props_on_price_rental_monthly_current_cents  (price_rental_monthly_current_cents)
#  index_pwb_props_on_price_sale_current_cents            (price_sale_current_cents)
#  index_pwb_props_on_reference                           (reference)
#  index_pwb_props_on_uuid                                (uuid)
#  index_pwb_props_on_visible                             (visible)
#
module Pwb
  class Prop < ApplicationRecord
    translates :title, :description
    globalize_accessors locales: I18n.available_locales

    # May 2025 - Because below is added in a later migration
    # I get an error in the 20161120122914_translate_props migration if it is enabled
    # enum area_unit: { sqmt: 0, sqft: 1 }
    # attribute :area_unit, :integer

    # This entire class can be deleted anyway

    # geocoded_by :address, :lookup => lambda{ |obj| obj.geocoder_lookup }
    # reverse_geocoded_by :latitude, :longitude do |obj,results|
    geocoded_by :geocodeable_address do |obj, results|
      if geo = results.first
        obj.longitude = geo.longitude
        obj.latitude = geo.latitude
        obj.city = geo.city
        obj.street_address = geo.street
        # Oct 2024 - below 3 no longer available on geo object
        # obj.street_number = geo.street_number
        # obj.street_address = geo.street_address
        # obj.street_name = geo.street_name
        obj.postal_code = geo.postal_code
        obj.province = geo.province
        obj.region = geo.state
        obj.country = geo.country
        # TODO: - add neighborhood (google spelling)
      end
    end

    after_validation :geocode

    # below needed to avoid "... is not an attribute known to Active Record" warnings
    attribute :title
    attribute :description

    # Use EUR as model level currency
    # register_currency :eur

    # monetize :precio_venta, with_model_currency: :currency, :as => "sales_price", :allow_nil => true
    monetize :price_sale_current_cents, with_model_currency: :currency, allow_nil: true
    monetize :price_sale_original_cents, with_model_currency: :currency
    monetize :price_rental_monthly_current_cents, with_model_currency: :currency
    monetize :price_rental_monthly_original_cents, with_model_currency: :currency
    monetize :price_rental_monthly_low_season_cents, with_model_currency: :currency
    monetize :price_rental_monthly_high_season_cents, with_model_currency: :currency
    monetize :price_rental_monthly_standard_season_cents, with_model_currency: :currency
    monetize :price_rental_monthly_for_search_cents, with_model_currency: :currency

    monetize :commission_cents, with_model_currency: :currency
    monetize :service_charge_yearly_cents, with_model_currency: :currency
    # monetize :price_in_a_range_cents, with_model_currency: :currency, :allow_nil => true,
    # :numericality => {
    #   :greater_than_or_equal_to => 0,
    #   :less_than_or_equal_to => 10000
    # }

    # TODO: - Ensure admin client can warn of uniqueness errors
    # and enable below:
    # validates :reference, :uniqueness => { case_sensitive: false }

    belongs_to :agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', optional: true

    counter_culture :agency_tenant,
                    column_name: 'realty_assets_count',
                    foreign_key: 'agency_tenant_uuid',
                    primary_key: 'uuid'

    has_many :prop_photos, -> { order 'sort_order asc' }
    has_many :features

    scope :for_rent, -> { where('for_rent_short_term OR for_rent_long_term') }
    # couldn't do above if for_rent_short_term was a flatshihtzu boolean
    scope :for_sale, -> { where for_sale: true }
    scope :visible, -> { where visible: true }

    scope :in_zone, ->(key) { where zone_key: key }
    scope :in_locality, ->(key) { where locality_key: key }

    scope :property_type, ->(property_type) { where prop_type_key: property_type }
    scope :property_state, ->(property_state) { where prop_state_key: property_state }
    # scope :property_type, -> (property_type) { where property_type: property_type }
    # scope :property_state, -> (property_state) { where property_state: property_state }
    # below scopes used for searching
    scope :for_rent_price_from, lambda { |minimum_price|
                                  where('price_rental_monthly_for_search_cents >= ?', minimum_price.to_s)
                                }
    scope :for_rent_price_till, lambda { |maximum_price|
                                  where('price_rental_monthly_for_search_cents <= ?', maximum_price.to_s)
                                }
    scope :for_sale_price_from, ->(minimum_price) { where('price_sale_current_cents >= ?', minimum_price.to_s) }
    scope :for_sale_price_till, ->(maximum_price) { where('price_sale_current_cents <= ?', maximum_price.to_s) }
    scope :count_bathrooms, ->(min_count_bathrooms) { where('count_bathrooms >= ?', min_count_bathrooms.to_s) }
    scope :count_bedrooms, ->(min_count_bedrooms) { where('count_bedrooms >= ?', min_count_bedrooms.to_s) }
    # June 2022 TODO - replace above with below
    scope :bathrooms_from, ->(min_count_bathrooms) { where('count_bathrooms >= ?', min_count_bathrooms.to_s) }
    scope :bedrooms_from, ->(min_count_bedrooms) { where('count_bedrooms >= ?', min_count_bedrooms.to_s) }
    # scope :starts_with, -> (name) { where("name like ?", "#{name}%")}
    # scope :pending, joins(:admin_request_status).where('admin_request_statuses.name = ?','Pending Approval')

    def geocodeable_address
      # [street, city, state, country].compact.join(', ')
      street_address.to_s + ' , ' + city.to_s + ' , ' + province.to_s + ' , ' + postal_code.to_s
    end

    def has_garage
      count_garages && (count_garages > 0)
    end

    def for_rent
      for_rent_short_term || for_rent_long_term
    end

    def show_map
      if latitude.present? && longitude.present?
        !hide_map
      else
        false
      end
    end

    # Getter
    def get_features
      Hash[features.map { |key, _value| [key.feature_key, true] }]
      # http://stackoverflow.com/questions/39567/what-is-the-best-way-to-convert-an-array-to-a-hash-in-ruby
      # returns something like {"terraza"=>true, "alarma"=>true, "gotele"=>true, "sueloMarmol"=>true}
      # - much easier to use on the client side admin page
    end

    # Setter- called by update_extras in properties controller
    # expects a hash with keys like "cl.casafactory.fieldLabels.extras.alarma"
    # each with a value of true or false
    def set_features=(features_json)
      # return unless features_json.class == Hash
      features_json.keys.each do |feature_key|
        # TODO: - create feature_key if its missing
        if ['true', true].include?(features_json[feature_key])
          features.find_or_create_by(feature_key:)
        else
          features.where(feature_key:).delete_all
        end
      end
    end

    # below will return a translated (and sorted acc to translation)
    # list of extras for property
    def extras_for_display
      merged_extras = []
      get_features.keys.each do |extra|
        # extras_field_key = "fieldLabels.extras.#{extra}"
        translated_option_key = I18n.t extra
        merged_extras.push translated_option_key

        # below check to ensure the field has not been deleted as
        # an available extra
        # quite an edge case - not entirely sure its worthwhile
        # if extras_field_configs[extras_field_key]
        #   translated_option_key = I18n.t extras_field_key
        #   merged_extras.push translated_option_key
        # end
      end
      merged_extras.sort { |w1, w2| w1.casecmp(w2) }
      # above ensures sort is case insensitive
      # by default sort will add lowercased items to end of array
      # http://stackoverflow.com/questions/17799871/how-do-i-alphabetize-an-array-ignoring-case
      # return merged_extras.sort
    end

    def ordered_photo(number)
      # allows me to pick an individual image according to an order
      prop_photos[number - 1] if prop_photos.length >= number
    end

    def primary_image_url
      if prop_photos.length > 0
        ordered_photo(1).image.url
      else
        ''
      end
    end

    # def ordered_photo_url(number)
    #   # allows me to pick an individual image according to an order
    #   unless prop_photos.length >= number
    #     return "https://placeholdit.imgix.net/~text?txtsize=38&txt=&w=550&h=400&txttrack=0"
    #   end
    #   prop_photos[number - 1].image.url
    # end

    def url_friendly_title
      # used in constructing seo friendly url
      if title && title.length > 2
        title.parameterize
      else
        'show'
      end
    end

    def contextual_show_path(rent_or_sale)
      rent_or_sale ||= for_rent ? 'for_rent' : 'for_sale'
      if rent_or_sale == 'for_rent'
        Rails.application.routes.url_helpers.prop_show_for_rent_path(locale: I18n.locale, id:,
                                                                     url_friendly_title:)
      else
        Rails.application.routes.url_helpers.prop_show_for_sale_path(locale: I18n.locale, id:,
                                                                     url_friendly_title:)
      end
    end

    def contextual_price(rent_or_sale)
      rent_or_sale ||= for_rent ? 'for_rent' : 'for_sale'
      if rent_or_sale == 'for_rent'
        # || rent_or_sale == "forRent"
        price_rental_monthly_for_search
      # contextual_price = self.rental_price
      else
        price_sale_current
      end

      # .zero? ? nil : contextual_price.format(:no_cents => true)
    end

    # will return nil if price is 0
    def contextual_price_with_currency(rent_or_sale)
      contextual_price = self.contextual_price rent_or_sale

      if contextual_price.zero?
        nil
      else
        contextual_price.format(no_cents: true)
      end
      # return contextual_price.zero? ? nil : contextual_price.format(:no_cents => true)
    end

    def rental_price
      # deliberately checking short_term first
      # so that it overrides long_term price if both are set
      rental_price = lowest_short_term_price || 0 if for_rent_short_term
      rental_price = price_rental_monthly_current || 0 unless rental_price && rental_price > 0
      return nil unless rental_price && rental_price > 0

      rental_price
    end

    def lowest_short_term_price
      prices_array = [price_rental_monthly_low_season, price_rental_monthly_standard_season,
                      price_rental_monthly_high_season]
      # remove any prices that are 0:
      prices_array.reject! { |a| a.cents < 1 }
      prices_array.min
    end

    def self.properties_search(**search_filtering_params)
      currency_string = search_filtering_params[:currency] || 'usd'
      currency = Money::Currency.find currency_string

      search_results = if search_filtering_params[:sale_or_rental] == 'rental'
                         Pwb::Prop.visible.for_rent
                       else
                         Pwb::Prop.visible.for_sale
                       end
      search_filtering_params.each do |key, value|
        # empty_values = ["propertyTypes."]
        next if value == 'none' || key == :sale_or_rental || key == :currency

        price_fields = %i[for_sale_price_from for_sale_price_till for_rent_price_from for_rent_price_till]
        if price_fields.include? key
          # above needed as some currencies like Chilean peso
          # don't have the cents field multiplied by 100
          value = value.gsub(/\D/, '').to_i * currency.subunit_to_unit
          # search_results = search_results.public_send(key, value) if value.present?
        end
        search_results = search_results.public_send(key, value) if value.present?
      end
      search_results
    end
    before_save :set_rental_search_price
    after_create :set_defaults

    private

    def set_defaults
      # This is pretty ugly - need to create a service object
      # with DI as soon as I can:
      current_website = Website.unique_instance
      # default_currency = Website.last.present? ? Website.last.default_currency : nil
      if current_website.default_currency.present?
        self.currency = current_website.default_currency
        save
      end
      return unless current_website.default_area_unit.present?

      self.area_unit = current_website.default_area_unit
      save
    end

    # called from before_save
    def set_rental_search_price
      # below for setting a value that I can use for searcing and ordering rental properties
      self.price_rental_monthly_for_search = rental_price
    end
  end
end
