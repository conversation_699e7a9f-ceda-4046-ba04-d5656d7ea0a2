# == Schema Information
#
# Table name: llm_interaction_associations
#
#  id                               :bigint           not null, primary key
#  agency_tenant_uuid               :uuid             not null
#  associable_type                  :string           not null
#  association_metadata             :jsonb
#  association_type                 :string           not null
#  discarded_at                     :datetime
#  llm_interaction_assoc_aasm_state :string
#  llm_interaction_assoc_flags      :integer          default(0), not null
#  llm_interaction_uuid             :uuid             not null
#  translations                     :jsonb
#  uuid                             :uuid
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  associable_id                    :uuid             not null
#
# Indexes
#
#  idx_on_llm_interaction_assoc_flags_d5d80b59f6               (llm_interaction_assoc_flags)
#  index_llm_interaction_associations_on_agency_tenant_uuid    (agency_tenant_uuid)
#  index_llm_interaction_associations_on_association_type      (association_type)
#  index_llm_interaction_associations_on_discarded_at          (discarded_at)
#  index_llm_interaction_associations_on_llm_interaction_uuid  (llm_interaction_uuid)
#  index_llm_interaction_associations_on_uuid                  (uuid)
#
class LlmInteractionAssociation < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  include Discard::Model

  belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid'
  belongs_to :associable, polymorphic: true, primary_key: 'uuid'

  def sale_listing
    # GPT advices against this though I like it
    associable if associable_type == 'SaleListing'
  end

  validates :association_type, presence: true

  include FlagShihTzu
  has_flags(
    1 => :is_primary,
    column: 'llm_interaction_assoc_flags'
  )

  # Store other metadata
  store_attribute :association_metadata, :confidence_score, :integer
  store_attribute :association_metadata, :notes, :string

  # Add a default flag value
  after_initialize :set_default_flags, if: :new_record?

  private

  def set_default_flags
    self.llm_interaction_assoc_flags ||= 0 # Default flags to 0 (no flags set)
  end
end
