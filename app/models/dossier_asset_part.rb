# == Schema Information
#
# Table name: dossier_asset_parts
#
#  id                           :bigint           not null, primary key
#  agency_tenant_uuid           :uuid
#  asset_part_condition         :string
#  asset_part_description       :string
#  asset_part_details           :jsonb
#  asset_part_flags             :integer          default(0), not null
#  asset_part_main_color        :string
#  asset_part_secondary_color   :string
#  asset_part_significant_items :text             default([]), is an Array
#  asset_part_slug              :string
#  asset_part_style             :string
#  asset_part_title             :string
#  asset_part_type              :integer          default("unknown")
#  asset_part_unique_features   :jsonb
#  dap_area_sq_feet             :string
#  dap_area_sq_meters           :string
#  discarded_at                 :datetime
#  dossier_asset_uuid           :uuid
#  is_heated                    :boolean          default(FALSE)
#  is_outside                   :boolean          default(FALSE)
#  realty_asset_photo_uuids     :text             default([]), is an Array
#  realty_asset_photos_count    :integer          default(0)
#  realty_asset_uuid            :uuid
#  realty_dossier_uuid          :uuid
#  translations                 :jsonb
#  uuid                         :uuid
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#
# Indexes
#
#  index_dossier_asset_parts_on_agency_tenant_uuid   (agency_tenant_uuid)
#  index_dossier_asset_parts_on_asset_part_flags     (asset_part_flags)
#  index_dossier_asset_parts_on_discarded_at         (discarded_at)
#  index_dossier_asset_parts_on_dossier_asset_uuid   (dossier_asset_uuid)
#  index_dossier_asset_parts_on_realty_asset_uuid    (realty_asset_uuid)
#  index_dossier_asset_parts_on_realty_dossier_uuid  (realty_dossier_uuid)
#  index_dossier_asset_parts_on_uuid                 (uuid)
#
class DossierAssetPart < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  include Discard::Model

  # store_attribute :asset_part_details, :realty_asset_photo_uuids, :json, default: []
  store_attribute :asset_part_details, :raw_llm_json, :json, default: {}
  store_attribute :asset_part_details, :asset_part_size, :string, default: ''
  store_attribute :asset_part_details, :asset_part_area, :string, default: ''

  # belongs_to :sold_transaction, class_name: 'SoldTransaction', foreign_key: 'sold_transaction_uuid', primary_key: :uuid, optional: false

  belongs_to :dossier_asset, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid', optional: true
  has_one :realty_asset, through: :dossier_asset # ,
  has_one :realty_dossier, through: :dossier_asset # ,
  # belongs_to :realty_dossier, class_name: 'RealtyDossier', foreign_key: 'realty_dossier_uuid', primary_key: :uuid, optional: true
  # belongs_to :realty_asset, primary_key: 'uuid', foreign_key: 'realty_asset_uuid', optional: true

  def asset_part_photos_json
    if realty_asset_photo_uuids.nil?
      []
    else
      # puts realty_asset_photo_uuids
      realty_asset.asset_photos.where(
        uuid: realty_asset_photo_uuids
      ).as_json(only: %w[
                  id
                  photo_title photo_description
                  photo_ai_desc realty_asset_photo_tags photo_slug
                  flag_is_hidden photo_title image_details sale_listing_uuid
                  file_size height width content_type sort_order uuid
                ],
                methods: %w[image_details raw_ai_analysis])
    end
  end

  def asset_part_photos_json_summary_for_llm
    if realty_asset_photo_uuids.nil?
      []
    else
      # puts realty_asset_photo_uuids
      realty_asset.asset_photos.where(
        uuid: realty_asset_photo_uuids
      ).as_json(only: %w[
                  id photo_title photo_description
                  photo_ai_desc realty_asset_photo_tags
                ])
      # below contains too much superflous data for the LLM
      # methods: %w[full_image_url raw_ai_analysis])
    end
  end

  def asset_part_photos_json_summary
    if realty_asset_photo_uuids.nil?
      []
    else
      # puts realty_asset_photo_uuids
      realty_asset.asset_photos.where(
        uuid: realty_asset_photo_uuids
      ).as_json(only: %w[
                  id photo_title photo_description
                  photo_ai_desc realty_asset_photo_tags photo_slug
                ],
                methods: %w[full_image_url raw_ai_analysis])
    end
  end

  def asset_part_photos
    if realty_asset_photo_uuids.nil?
      []
    else
      # puts realty_asset_photo_uuids
      realty_asset.asset_photos.where(
        uuid: realty_asset_photo_uuids
      )
    end
  end

  # can access options below as DossierAssetPart.asset_part_types.keys
  enum asset_part_type: {
    # Interior Rooms
    unknown: 0,
    bedroom: 1,
    kitchen: 2,
    living_room: 3,
    dining_room: 4,
    hallway: 5,
    bathroom: 6,
    utility_room: 7,
    conservatory: 8,
    loft: 9,
    basement: 10,
    ensuite_bathroom: 11,
    dressing_room: 12,
    reception_room: 13, # Alternative to living_room for formal spaces
    office: 14,         # Home office space
    pantry: 15,
    laundry_room: 16,
    study: 17,
    walk_in_closet: 18,
    home_theater: 19,
    sunroom: 20,
    gym: 21,
    nursery: 22,       # For baby/child room
    guest_room: 23,    # Specific guest bedroom
    landing: 24,       # Staircase landing area
    open_plan_area: 25, # Combined living/kitchen/dining

    # Exterior Areas
    front_of_building: 26, # Facade or front exterior
    back_of_building: 27,  # Rear exterior
    side_of_building: 28,  # Side exterior
    garden: 29,
    balcony: 30,
    patio: 31,
    deck: 32,
    porch: 33,
    driveway: 34,
    garage: 35,
    shed: 36,             # Outbuilding or storage shed
    pool_area: 37,        # Swimming pool (indoor or outdoor)
    terrace: 38,          # Rooftop or elevated flat area

    # Contextual Views
    view: 39,             # Scenic view from property (e.g., mountain, sea)
    street_view: 40,      # View of the street from the property
    neighborhood: 41,     # Surrounding area or nearby amenities
    aerial_view: 42,      # Drone or overhead shot of property
    entrance: 43,         # Main entryway (interior or exterior)
    staircase: 44,        # Dedicated staircase shot

    # Miscellaneous
    storage_room: 45, # General storage (not a walk-in closet)
    # 12 May 2025 - added below 2 after they were in the LLM response
    # despite instructions to the contrary
    front_garden: 46,
    rear_garden: 47,
    other: 99 # Catch-all for uncategorized photos
  }

  # # Override the setter method
  def asset_part_type=(value)
    valid_values = self.class.asset_part_types.keys
    super(valid_values.include?(value.to_s) ? value : 'other')
  end

  # # Ensure invalid values are set to "other" before validation
  # before_validation :set_default_asset_part_type

  # private

  # def set_default_asset_part_type
  #   # Skip if nil (unless presence is required)
  #   return if asset_part_type.nil?

  #   valid_values = self.class.asset_part_types.keys
  #   return if valid_values.include?(asset_part_type)

  #   self.asset_part_type = 'other'
  # end
end
