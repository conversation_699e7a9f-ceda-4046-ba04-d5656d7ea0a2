# == Schema Information
#
# Table name: dossier_info_sources
#
#  id                                         :bigint           not null, primary key
#  agency_tenant_uuid                         :uuid
#  associated_listing_uuid                    :uuid
#  discarded_at                               :datetime
#  dossier_asset_uuid                         :uuid
#  dossier_info_source_aasm_state             :string
#  dossier_info_source_details                :jsonb
#  dossier_info_source_flags                  :integer          default(0), not null
#  dossier_info_source_rating                 :integer          default(0), not null
#  is_for_most_comparable_property_to_primary :boolean          default(FALSE)
#  is_source_for_primary_property             :boolean          default(FALSE)
#  llm_interaction_uuid                       :uuid
#  property_is_for_rent                       :boolean          default(FALSE)
#  property_is_for_sale                       :boolean          default(TRUE)
#  realty_asset_photo_uuid                    :uuid
#  realty_dossier_uuid                        :uuid
#  realty_search_query_uuid                   :uuid
#  source_is_epc                              :boolean          default(FALSE)
#  source_is_listing                          :boolean          default(TRUE)
#  source_is_photo                            :boolean          default(FALSE)
#  source_is_sold_transaction                 :boolean          default(FALSE)
#  uuid                                       :uuid
#  created_at                                 :datetime         not null
#  updated_at                                 :datetime         not null
#
# Indexes
#
#  index_dossier_info_sources_on_agency_tenant_uuid          (agency_tenant_uuid)
#  index_dossier_info_sources_on_associated_listing_uuid     (associated_listing_uuid)
#  index_dossier_info_sources_on_discarded_at                (discarded_at)
#  index_dossier_info_sources_on_dossier_asset_uuid          (dossier_asset_uuid)
#  index_dossier_info_sources_on_dossier_info_source_flags   (dossier_info_source_flags)
#  index_dossier_info_sources_on_dossier_info_source_rating  (dossier_info_source_rating)
#  index_dossier_info_sources_on_realty_asset_photo_uuid     (realty_asset_photo_uuid)
#  index_dossier_info_sources_on_realty_dossier_uuid         (realty_dossier_uuid)
#  index_dossier_info_sources_on_uuid                        (uuid)
#
class DossierInfoSourceFromPhoto < DossierInfoSource
  # default_scope { where(source_is_photo: true) }

  # store_attribute :dossier_info_source_details, :description_medium, :string, default: ''
  # # store_attribute :dossier_info_source_details, :reasoning_content, :json, default: {}
  # # store_attribute :dossier_info_source_details, :property_inventory, :json, default: {}
  # store_attribute :dossier_info_source_details, :sections_or_rooms_details, :json, default: {}

  # # belongs_to :sold_transaction, class_name: 'SoldTransaction', foreign_key: 'sold_transaction_uuid', primary_key: :uuid, optional: false
  # # has_one :postcode_area, through: :sold_transaction # , source: :postcode_area

  # # validates :realty_dossier_uuid, presence: true

  # # Ensure the combination of realty_dossier_uuid and associated_listing_uuid is unique
  # # validates :realty_dossier_uuid, uniqueness: { scope: :associated_listing_uuid, message: 'and realty_dossier combination must be unique' }
  # # validates :is_most_comparable, uniqueness: { scope: :realty_dossier_uuid }, if: -> { is_most_comparable }

  default_scope { where(source_is_photo: true) }

  store_attribute :dossier_info_source_details, :description_medium, :string, default: ''
  store_attribute :dossier_info_source_details, :main_section_or_room_details, :json, default: {}
  store_attribute :dossier_info_source_details, :other_section_or_room_details, :json, default: []

  belongs_to :realty_dossier, foreign_key: :realty_dossier_uuid, primary_key: :uuid, optional: true
  belongs_to :target_photo, foreign_key: :realty_asset_photo_uuid, primary_key: :uuid,
                            class_name: 'RealtyAssetPhoto', optional: true

  def analysed_photo_summary
    {
      realty_asset_photo_uuid: target_photo&.uuid,
      main_section_or_room_details: main_section_or_room_details,
      other_section_or_room_details: other_section_or_room_details
    }
  end

  def self.find_or_create_from_ai_interaction(llm_interaction, photo_analysed, dossier_item)
    primary_dossier_asset_uuid = dossier_item.primary_dossier_asset.uuid
    return unless llm_interaction.chosen_response.present?

    chosen_response_json = JSON.parse(llm_interaction.chosen_response)

    photo_info_source = DossierInfoSourceFromPhoto.find_or_create_by(
      llm_interaction_uuid: llm_interaction.uuid,
      dossier_asset_uuid: primary_dossier_asset_uuid,
      realty_dossier_uuid: dossier_item.uuid,
      agency_tenant_uuid: llm_interaction.agency_tenant_uuid,
      source_is_photo: true
    )

    other_section_or_room_details = []
    main_section_or_room_details = {}

    chosen_response_json['sections_or_rooms_in_photo'].each do |cbl_analysis|
      if cbl_analysis['is_main_section_or_room']
        main_section_or_room_details = cbl_analysis
      else
        other_section_or_room_details << cbl_analysis
      end
    end

    gpa = chosen_response_json['general_photo_analysis']
    photo_slug = "#{gpa['photo_catchy_title'].parameterize}-#{photo_analysed.uuid[0...6]}"
    photo_analysed.update!(
      flag_is_parsed_by_llm: true,
      photo_title: gpa['photo_catchy_title'],
      photo_description: gpa['photo_general_description'],
      photo_ai_desc: gpa['photo_general_description'],
      realty_asset_photo_tags: gpa['photo_tags'],
      photo_slug: photo_slug,
      raw_ai_analysis: chosen_response_json
    )

    photo_info_source.update!(
      # dossier_asset_uuid: primary_dossier_asset_uuid,
      main_section_or_room_details: main_section_or_room_details,
      other_section_or_room_details: other_section_or_room_details,
      description_medium: gpa['photo_general_description']
    )

    photo_info_source
  end
end
