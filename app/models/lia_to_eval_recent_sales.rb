# == Schema Information
#
# Table name: llm_interaction_associations
#
#  id                               :bigint           not null, primary key
#  agency_tenant_uuid               :uuid             not null
#  associable_type                  :string           not null
#  association_metadata             :jsonb
#  association_type                 :string           not null
#  discarded_at                     :datetime
#  llm_interaction_assoc_aasm_state :string
#  llm_interaction_assoc_flags      :integer          default(0), not null
#  llm_interaction_uuid             :uuid             not null
#  translations                     :jsonb
#  uuid                             :uuid
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  associable_id                    :uuid             not null
#
# Indexes
#
#  idx_on_llm_interaction_assoc_flags_d5d80b59f6               (llm_interaction_assoc_flags)
#  index_llm_interaction_associations_on_agency_tenant_uuid    (agency_tenant_uuid)
#  index_llm_interaction_associations_on_association_type      (association_type)
#  index_llm_interaction_associations_on_discarded_at          (discarded_at)
#  index_llm_interaction_associations_on_llm_interaction_uuid  (llm_interaction_uuid)
#  index_llm_interaction_associations_on_uuid                  (uuid)
#
class LiaToEvalRecentSales < LlmInteractionAssociation
  # Mar 11 2025
  # This class is used to persist the results of an LlmInteraction
  # to evaluate recent sales
  # belongs_to :sale_listing, foreign_key: :associable_id, primary_key: :uuid, optional: true
  belongs_to :realty_dossier, foreign_key: :associable_id, primary_key: :uuid, optional: true

  default_scope { where(association_type: 'to_eval_recent_sales') }

  store_attribute :association_metadata, :uuid_of_most_similar_st_epc, :string, default: ''
  # store_attribute :association_metadata, :id_of_most_similar_sold_transaction, :integer, default: 0
  # store_attribute :association_metadata, :ids_of_other_similar_sold_transactions, :array, default: []
  store_attribute :association_metadata, :uuids_of_other_similar_st_epcs, :json, default: []
  store_attribute :association_metadata, :estimated_fair_value_price, :decimal, default: 0.0
  store_attribute :association_metadata, :is_asking_price_competitive_or_overpriced, :string, default: ''
  store_attribute :association_metadata, :reasoning_content, :json, default: {}

  # belongs_to :sold_transaction_epc, primary_key: 'uuid', foreign_key: 'uuid_of_most_similar_st_epc', optional: true
  # Above returns nil but does not throw an error either

  def summary_console_print
    attributes.except('full_prompt', 'full_response', 'chosen_response')
  end

  def most_similar_sold_transaction_epc
    SoldTransactionEpc.find_by_uuid(uuid_of_most_similar_st_epc)
  end

  def other_similar_sold_transactions_epcs
    SoldTransactionEpc.where(uuid: uuids_of_other_similar_st_epcs)
  end

  def self.find_or_create_from_llm_interaction(
    llm_interaction, dossier_sale_listing, dossier_item
  )
    return unless llm_interaction.chosen_response.present?

    chosen_response_json = JSON.parse(llm_interaction.chosen_response)
    # self.id_of_most_similar_sold_transaction = chosen_response_json["id_of_most_similar_sold_transaction"]
    association_type = 'to_eval_recent_sales'

    responsible_association = LiaToEvalRecentSales.find_or_create_by(
      llm_interaction: llm_interaction,
      associable: dossier_item,
      association_type: association_type,
      # association_metadata: metadata,
      agency_tenant_uuid: llm_interaction.agency_tenant_uuid
    )
    responsible_association.update!(
      uuid_of_most_similar_st_epc: chosen_response_json['uuid_of_most_similar_sold_transaction'],
      uuids_of_other_similar_st_epcs: chosen_response_json['uuids_of_other_similar_sold_transactions'], # ids_of_other_similar_sold_transactions
      estimated_fair_value_price: chosen_response_json['estimated_fair_value_price'],
      is_asking_price_competitive_or_overpriced: chosen_response_json['is_asking_price_competitive_or_overpriced'],
      reasoning_content: chosen_response_json['reasoning_content']
    )

    # March 2025 - perhaps the cols below should be in the realty_dossier item
    dossier_sale_listing.update!(
      catchy_title: chosen_response_json['catchy_title'],
      description_short: chosen_response_json['description_short'],
      description_medium: chosen_response_json['description_medium'],
      description_bullet_points: chosen_response_json['description_bullet_points'],
      description_long: chosen_response_json['description_long']
    )
    responsible_association
  end

  private

  def associable_type_valid?
    associable_type == 'SaleListing'
  end

  validate :associable_type_valid?, if: :new_record?
end
