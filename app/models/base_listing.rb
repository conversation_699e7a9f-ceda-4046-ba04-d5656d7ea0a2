class BaseListing < ApplicationRecord
  # acts_as_tenant(:agency, :foreign_key => "agency_uuid", :primary_key => "uuid", counter_cache: true)
  # Feb 2021  - above won't work as it will assume counter_cache col to be base_listings_count
  self.abstract_class = true

  delegate :count_toilets, :count_garages, :count_bedrooms,
           :area_unit, :plot_area, :constructed_area, :year_construction,
           :latitude, :longitude, :city, :region, :country, :street_name,
           :neighborhood,
           :formatted_constructed_area,
           :street_number, :postal_code, :street_address,
           :count_bathrooms, to: :realty_asset

  # # # Getter
  # def get_features
  #   Hash[features.map { |key, _value| [key.feature_key, true] }]
  #   # http://stackoverflow.com/questions/39567/what-is-the-best-way-to-convert-an-array-to-a-hash-in-ruby
  #   # returns something like {"terraza"=>true, "alarma"=>true, "gotele"=>true, "sueloMarmol"=>true}
  #   # - much easier to use on the client side admin page
  # end

  # # Setter- called by update_extras in properties controller
  # # expects a hash with keys like "cl.casafactory.fieldLabels.extras.alarma"
  # # each with a value of true or false
  def set_features=(features_json)
    # return unless features_json.class == Hash
    features_json.keys.each do |ref_slug|
      # TODO: - create ref_slug if its missing
      if ['true', true].include?(features_json[ref_slug])
        # if the feature identified by the ref_slug has been set to true
        # make sure it exists for the listing
        features.find_or_create_by(ref_slug:)
      else
        # else delete all instances (there should only be one)
        # for the listing
        features.where(ref_slug:).delete_all
      end
    end
  end

  def self.find_by_localized_slug(locale, slug)
    # perhaps use parameterize for below
    slug = I18n.transliterate(slug).downcase
    Mobility.with_locale(locale) do
      i18n.find_by(slug:)
    end
  end

  def listing_display_url
    bvh_api_base = 'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales'
    bvh_site_base = 'https://www.buenavistahomes.eu/en/s/properties'
    return unique_url.gsub(bvh_api_base, bvh_site_base) if unique_url.include?(bvh_api_base)

    unique_url
  end

  def vendor_specific_url(vendor_name)
    if %w[buenavista costaspecialist].include?(vendor_name)
      bvh_api_base = 'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales'
      bvh_site_base = 'https://www.buenavistahomes.eu/en/s/properties'
      bvh_site_base = 'https://www.costaspecialist.nl/en' if vendor_name == 'costaspecialist'
      return unique_url.gsub(bvh_api_base, bvh_site_base) if unique_url.include?(bvh_api_base)
    end

    unique_url
  end

  def preview_url(client_base_url = nil)
    # TODO: - use a flag on the photo instance
    # or something along those lines to figure out which image
    # to use for previews (or perhaps that has to come from the page object
    # - once all listings come with an associated page object)
    return 'https://via.placeholder.com/300x200/09f.png/fff%20C/' unless listing_photos.length > 0

    preview_url = listing_photos[0].image_details['url'] || ''
    is_relative_url = preview_url[0] == '/'
    return client_base_url + preview_url if is_relative_url && client_base_url

    preview_url
  end

  # # jan 2022 - prefer preview_url to thumb_image_url
  # def thumb_image_url(client_base_url = nil)
  #   return 'https://via.placeholder.com/300x200/09f.png/fff%20C/' unless listing_photos.length > 0
  #   return client_base_url + listing_photos[0].image.small_fill.url if client_base_url

  #   listing_photos[0].image.small_fill.url
  # end

  def primary_image_url
    if listing_photos.length.positive?
      # listing_photos[0].image.url
      # july 2025 - above method must have been very old as I stopped using .image
      # a long time ago

      listing_photos[0].image_details['url']
    else
      'https://via.placeholder.com/300x200/09f.png/fff%20C/'
    end
  end

  def set_mobility_field(mob_field_name, mob_field_value, locale)
    send("#{mob_field_name}_backend").write(locale, mob_field_value)
    begin
      save!
    rescue Exception => e
      puts 'error at set_mobility_field'
      puts "#{e.message} for #{mob_field_value}"
    end
  end

  # # below will return a translated (and sorted acc to translation)
  # # list of extras for property
  # def extras_for_display
  #   merged_extras = []
  #   get_features.keys.each do |feature_key|
  #     if feature_key == "features.airCon"
  #       # have to hard code this to fix some
  #       # duplicates in production
  #       next
  #     end
  #     # extras_field_key = "fieldLabels.extras.#{feature_key}"
  #     translated_feature = I18n.t feature_key
  #     # merged_extras.push {label: translated_feature, className: feature_key }
  #     # above does not work..
  #     details = { label: translated_feature, className: feature_key.split(".").last }
  #     # TODO - add display_class col to features table so I
  #     # don't have to use feature_key
  #     merged_extras.push details
  #   end

  #   return merged_extras.sort_by { |k| k[:label] }
  #   # merged_extras.sort{ |w1, w2| w1.casecmp(w2) }
  #   # above ensures sort is case insensitive
  #   # by default sort will add lowercased items to end of array
  #   # http://stackoverflow.com/questions/17799871/how-do-i-alphabetize-an-array-ignoring-case
  #   # return merged_extras.sort
  # end

  # def self.class_mob_atts
  #   # for strong params in controller
  #   exp_mob_attr_names = []
  #   # Website.unique_instance.supported_locales_short.each do |locale|
  #   I18n.available_locales.each do |locale|
  #     SaleListing.mobility_attributes.each do |attr|
  #       exp_mob_attr_names.push "#{attr}_#{locale}"
  #     end
  #   end
  #   exp_mob_attr_names
  # end
end
