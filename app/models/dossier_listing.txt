# == Schema Information
#
# Table name: dossier_listings
#
#  id                         :bigint           not null, primary key
#  agency_tenant_uuid         :uuid
#  associated_listing_uuid    :uuid
#  discarded_at               :datetime
#  dossier_asset_uuid         :uuid
#  dossier_listing_aasm_state :string
#  dossier_listing_details    :jsonb
#  dossier_listing_flags      :integer          default(0), not null
#  dossier_listing_rating     :integer          default(0), not null
#  is_most_comparable         :boolean          default(FALSE)
#  is_rental_listing          :boolean          default(FALSE)
#  is_sale_listing            :boolean          default(TRUE)
#  realty_dossier_uuid        :uuid
#  realty_search_query_uuid   :uuid
#  uuid                       :uuid
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
# Indexes
#
#  index_dossier_listings_on_agency_tenant_uuid       (agency_tenant_uuid)
#  index_dossier_listings_on_associated_listing_uuid  (associated_listing_uuid)
#  index_dossier_listings_on_discarded_at             (discarded_at)
#  index_dossier_listings_on_dossier_listing_flags    (dossier_listing_flags)
#  index_dossier_listings_on_dossier_listing_rating   (dossier_listing_rating)
#  index_dossier_listings_on_realty_dossier_uuid      (realty_dossier_uuid)
#  index_dossier_listings_on_uuid                     (uuid)
#
class DossierListing < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # belongs_to :sold_transaction, class_name: 'SoldTransaction', foreign_key: 'sold_transaction_uuid', primary_key: :uuid, optional: false
  # has_one :postcode_area, through: :sold_transaction # , source: :postcode_area

  belongs_to :dossier_asset, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid', optional: true

  belongs_to :realty_dossier, class_name: 'RealtyDossier', foreign_key: 'realty_dossier_uuid', primary_key: :uuid, optional: true
  belongs_to :realty_search_query, primary_key: 'uuid', foreign_key: 'realty_search_query_uuid', optional: true
  belongs_to :sale_listing, class_name: 'SaleListing', primary_key: 'uuid',
                            foreign_key: 'associated_listing_uuid', optional: true

  validates :realty_dossier_uuid, presence: true
  validates :associated_listing_uuid, presence: true
  # validates :postcode_cluster_slug, presence: true, uniqueness: true
  # validates :percent_diff_to_cluster_price, presence: true

  # Ensure the combination of realty_dossier_uuid and associated_listing_uuid is unique
  validates :realty_dossier_uuid, uniqueness: { scope: :associated_listing_uuid, message: 'and realty_dossier combination must be unique' }

  validates :is_most_comparable, uniqueness: { scope: :realty_dossier_uuid }, if: -> { is_most_comparable }

  def is_source_for_primary_property
    is_rental_listing
  end
end
