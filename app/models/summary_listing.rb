# == Schema Information
#
# Table name: summary_listings
#
#  id                              :bigint           not null, primary key
#  agency_tenant_uuid              :uuid             not null
#  discarded_at                    :datetime
#  full_listing_url                :string
#  full_listing_uuid               :uuid
#  realty_agent_details            :jsonb
#  realty_asset_uuid               :uuid
#  realty_search_query_uuid        :uuid
#  scrape_item_uuid                :uuid
#  summary_listing_aasm_state      :string
#  summary_listing_bathrooms_count :integer
#  summary_listing_bedrooms_count  :integer
#  summary_listing_details         :jsonb
#  summary_listing_flags           :integer          default(0), not null
#  summary_listing_import_url      :string
#  summary_listing_latitude        :float
#  summary_listing_long_address    :string
#  summary_listing_longitude       :float
#  summary_listing_postcode        :string
#  summary_listing_price           :string
#  summary_listing_source_site     :integer          default(0), not null
#  summary_listing_uprn            :string
#  translations                    :jsonb
#  uuid                            :uuid
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#
# Indexes
#
#  index_summary_listings_on_discarded_at              (discarded_at)
#  index_summary_listings_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_summary_listings_on_summary_listing_flags     (summary_listing_flags)
#  index_summary_listings_on_uuid                      (uuid)
#
class SummaryListing < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # include Discard::Model
  # has_paper_trail

  store_attribute :summary_listing_details, :summary_listing_reference, :string, default: 'unknown_ref'
  store_attribute :summary_listing_details, :agent_details, :json, default: {}

  belongs_to :scrape_item, primary_key: 'uuid', foreign_key: 'scrape_item_uuid',
                           optional: true, class_name: 'ScrapeItem'
  counter_culture :scrape_item, column_name: 'summary_listings_count'
  belongs_to :realty_search_query, primary_key: 'uuid', foreign_key: 'realty_search_query_uuid',
                                   optional: true, class_name: 'RealtySearchQuery'
  counter_culture :realty_search_query, column_name: 'summary_listings_count'

  #  in most cases below will not be filled in
  belongs_to :realty_asset, primary_key: 'uuid', foreign_key: 'realty_asset_uuid',
                            optional: true, class_name: 'RealtyAsset'

  # def self.create_from_summary_hash(property_hash)
  #   # TODO: - error if property_hash[:full_listing_url] empty
  #   summary_sale_listing = SummaryListing.find_or_create_by(
  #     # full_listing_url: unique_url
  #     full_listing_url: property_hash[:full_listing_url]
  #     # full_listing_uuid: SecureRandom.uuid
  #   )
  #   summary_sale_listing.update!(property_hash.except(:agent_details))
  #   summary_sale_listing.update!(
  #     {
  #       agent_details: property_hash[:agent_details]
  #       # raw_summary_details: property_hash
  #     }
  #   )
  #   summary_sale_listing
  # end
end
