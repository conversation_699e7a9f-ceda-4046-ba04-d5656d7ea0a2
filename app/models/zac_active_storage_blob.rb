# == Schema Information
#
# Table name: active_storage_blobs
#
#  id           :bigint           not null, primary key
#  byte_size    :bigint           not null
#  checksum     :string
#  content_type :string
#  filename     :string           not null
#  key          :string           not null
#  metadata     :text
#  service_name :string           not null
#  created_at   :datetime         not null
#
# Indexes
#
#  index_active_storage_blobs_on_key  (key) UNIQUE
#
class ZacActiveStorageBlob < ActiveStorage::Blob
  # created to get around issues with namespacing in administrate gem
  # belongs_to :zac_active_storage_blob, primary_key: 'id', foreign_key: 'blob_id', optional: true
  # has_one_attached :image
  # has_one :zac_active_storage_attachment, foreign_key: 'record_id', primary_key: 'id'
end
