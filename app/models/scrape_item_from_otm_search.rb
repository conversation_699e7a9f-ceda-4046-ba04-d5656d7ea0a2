# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromOtmSearch < ScrapeItem
  default_scope { scrape_is_onthemarket.where(is_realty_search_scrape: true) }

  def self.find_or_create_for_otm_search(retrieval_end_point)
    # I might soon need to figure out some logic for deciding
    # when and if to create a new scrape item
    # Perhaps if the existing one is more than a few days old...
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: true
    )
    scrape_item.update!(scrape_is_onthemarket: true, is_realty_search_scrape: true)
    ScrapeItemFromOtmSearch.find(scrape_item.id)
  end

  def raw_contents_as_json
    JSON.parse(full_content_before_js)
  rescue JSON::ParserError => e
    # the full error message is too long to be useful
    puts 'Failed to parse JSON..' # #{e.message}"
    nil
  end

  def summary_sale_listings_from_scrape_item
    raw_contents_as_json['properties'].each do |onthemarket_hash|
      standardised_hash = get_property_hash_from_summary_json(
        onthemarket_hash
      )
      standardised_hash[:summary_listing_import_url] = scrapable_url
      # Find or create the SummaryListing based on full_listing_url
      summary_sale_listing = SummaryListing.find_or_initialize_by(
        full_listing_url: standardised_hash[:full_listing_url],
        scrape_item_uuid: uuid
      )

      # Update the attributes of the found or newly created SummaryListing
      summary_sale_listing.assign_attributes(standardised_hash)
      summary_sale_listing.realty_search_query_uuid = realty_search_query_uuid

      if summary_sale_listing.save!
        puts "summary_sale_listing: #{summary_sale_listing.inspect}"
      else
        puts "Failed to save/update SummaryListing: #{summary_sale_listing.errors.full_messages}"
      end
    end
  end

  private

  # Below is for the JSON summary object that comes from the OTM search path
  # 1 mar 2025: below def is to do with scraping a single SUMMARY item
  # from the search results page which does not have very much info
  def get_property_hash_from_summary_json(onthemarket_hash)
    # onthemarket_hash = onthemarket_hash.symbolize_keys!
    # if onthemarket_hash
    #   property_hash = {}
    #   property_hash['title'] = onthemarket_hash[:"property-title"]
    #   property_hash['reference'] = onthemarket_hash[:id]
    #   property_hash['import_url'] = "https://www.onthemarket.com/details/#{onthemarket_hash[:id]}/"
    #   property_hash['address'] = onthemarket_hash[:display_address]
    #   property_hash['price_sale_current'] = onthemarket_hash[:price].to_s.gsub(/[^\d]/, '') || '0'
    #   property_hash['count_bathrooms'] = '0' # Default value as bathrooms are not provided in the new structure
    #   property_hash['count_bedrooms'] = onthemarket_hash[:bedrooms].to_s || '0'
    #   property_hash['constructed_area'] = '0' # Default value as constructed_area is not provided in the new structure
    #   property_hash['listing_image_urls'] = onthemarket_hash[:images].map { |img| img['default'] } if onthemarket_hash[:images]
    #   property_hash['longitude'] = onthemarket_hash[:location]['lon'] if onthemarket_hash[:location]
    #   property_hash['latitude'] = onthemarket_hash[:location]['lat'] if onthemarket_hash[:location]
    # end

    onthemarket_hash = onthemarket_hash.symbolize_keys

    return {} unless onthemarket_hash

    property_hash = {
      agent_details: onthemarket_hash[:agent],
      realty_agent_details: onthemarket_hash[:agent],
      summary_listing_reference: onthemarket_hash[:id],
      # below seems to be outcode...
      summary_listing_postcode: onthemarket_hash[:postcode1],
      summary_listing_long_address: onthemarket_hash[:display_address],
      summary_listing_price: onthemarket_hash[:price].to_s.gsub(/[^\d]/, ''),
      summary_listing_bedrooms_count: onthemarket_hash[:bedrooms].to_i,
      summary_listing_bathrooms_count: onthemarket_hash[:bathrooms].to_i,
      summary_listing_latitude: onthemarket_hash.dig(:location, 'lat'),
      summary_listing_longitude: onthemarket_hash.dig(:location, 'lon'),
      full_listing_url: "https://www.onthemarket.com/details/#{onthemarket_hash[:id]}/",
      summary_listing_details: {
        title: onthemarket_hash[:"property-title"],
        reference: onthemarket_hash[:id],
        images: onthemarket_hash[:images]&.map { |img| img['default'] }
      },
      # summary_listing_bathrooms_count: 0, # Default value
      summary_listing_source_site: 1 # Assuming onthemarket is source site 1. Adjust as needed.

      # property_hash['area_unit'] = 'sqmt'
      # property_hash['country'] = 'England'
      # property_hash['currency'] = 'GBP'
      # property_hash['price_sale_current_currency'] = 'GBP'
    }
    property_hash
  end
end
