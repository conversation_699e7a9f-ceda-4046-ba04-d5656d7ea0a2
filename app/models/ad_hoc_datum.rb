# == Schema Information
#
# Table name: ad_hoc_data
#
#  id                        :bigint           not null, primary key
#  ad_hoc_class_slug         :string           default(""), not null
#  ad_hoc_data_enum          :integer          default(0), not null
#  ad_hoc_data_flags         :integer          default(0), not null
#  ad_hoc_data_item_slug     :string           default(""), not null
#  ad_hoc_data_tags          :string           default(""), not null
#  ad_hoc_foreign_class_slug :string           default(""), not null
#  ad_hoc_foreign_item_slug  :string           default(""), not null
#  ad_hoc_meta               :jsonb            not null
#  ad_hoc_sub_class_slug     :string           default(""), not null
#  agency_tenant_uuid        :uuid
#  agency_uuid               :uuid
#  discarded_at              :datetime
#  json_representation       :jsonb            not null
#  site_visitor_token        :string
#  translations              :jsonb
#  uuid                      :uuid
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#
# Indexes
#
#  index_ad_hoc_data_on_ad_hoc_class_slug          (ad_hoc_class_slug)
#  index_ad_hoc_data_on_ad_hoc_data_flags          (ad_hoc_data_flags)
#  index_ad_hoc_data_on_ad_hoc_data_item_slug      (ad_hoc_data_item_slug)
#  index_ad_hoc_data_on_ad_hoc_foreign_class_slug  (ad_hoc_foreign_class_slug)
#  index_ad_hoc_data_on_ad_hoc_sub_class_slug      (ad_hoc_sub_class_slug)
#  index_ad_hoc_data_on_agency_tenant_uuid         (agency_tenant_uuid)
#  index_ad_hoc_data_on_discarded_at               (discarded_at)
#  index_ad_hoc_data_on_json_representation        (json_representation) USING gin
#  index_ad_hoc_data_on_site_visitor_token         (site_visitor_token)
#  index_ad_hoc_data_on_uuid                       (uuid)
#
class AdHocDatum < ApplicationRecord
  # acts_as_tenant(:"Pwb::Agency")
  # acts_as_tenant(:agency, :foreign_key => "agency_uuid", :primary_key => "uuid", counter_cache: false)

  # Ensure the ad_hoc_data_slug column is present for STI
  # validates :ad_hoc_data_slug, presence: true, uniqueness: true

  store_attribute :json_representation, :object_values, :json, default: {}
  # Define a JSON column to store unique data
  # store :object_values, coder: JSON

  # enum target_item_type: { is_query: 0, is_listing: 1}

  # include Discard::Model
  #   # include FlagShihTzug
  # extend Mobility
  # # translates :results_title, :results_text

  # Implement Singleton pattern
  def self.instance
    # ActsAsTenant.current_tenant = Agency.unique_instance
    first_or_create!
  end
end
