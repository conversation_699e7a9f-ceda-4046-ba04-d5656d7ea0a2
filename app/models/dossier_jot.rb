# == Schema Information
#
# Table name: dossier_jots
#
#  id                             :bigint           not null, primary key
#  agency_tenant_uuid             :uuid
#  archived                       :boolean          default(FALSE), not null
#  discarded_at                   :datetime
#  dossier_asset_uuid             :uuid
#  dossier_assets_comparison_uuid :uuid
#  dossier_jot_aasm_state         :string
#  dossier_jot_flags              :integer          default(0), not null
#  dossier_task_uuid              :uuid
#  extra_uuid                     :uuid
#  is_photo_specific              :boolean          default(FALSE), not null
#  is_primary_listing_jot         :boolean          default(FALSE)
#  is_query                       :boolean          default(FALSE), not null
#  jot_creator_name               :string
#  jot_creator_token              :string
#  jot_details                    :jsonb
#  jot_text                       :text
#  jot_title                      :string
#  photo_uuids                    :string           default([]), is an Array
#  primary_photo_uuid             :uuid
#  rating_aspect_uuid             :uuid
#  realty_dossier_uuid            :uuid
#  scoot_uuid                     :uuid
#  uuid                           :uuid             not null
#  visible_to_all                 :boolean          default(FALSE), not null
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#
# Indexes
#
#  index_dossier_jots_on_agency_tenant_uuid              (agency_tenant_uuid)
#  index_dossier_jots_on_discarded_at                    (discarded_at)
#  index_dossier_jots_on_dossier_assets_comparison_uuid  (dossier_assets_comparison_uuid)
#  index_dossier_jots_on_dossier_jot_flags               (dossier_jot_flags)
#  index_dossier_jots_on_realty_dossier_uuid             (realty_dossier_uuid)
#  index_dossier_jots_on_uuid                            (uuid)
#
class DossierJot < ApplicationRecord
  include Discard::Model

  acts_as_tenant :agency_tenant,
                 foreign_key: 'agency_tenant_uuid',
                 primary_key: 'uuid',
                 counter_cache: false

  belongs_to :realty_dossier,
             class_name: 'RealtyDossier',
             foreign_key: 'realty_dossier_uuid',
             primary_key: 'uuid',
             optional: false

  belongs_to :primary_photo,
             class_name: 'RealtyAssetPhoto',
             foreign_key: 'primary_photo_uuid',
             primary_key: 'uuid',
             optional: true

  # store_attribute :jot_details, :comparison_id, :string, default: ''
  # store_attribute :jot_details, :is_primary, :boolean, default: true
  # store_attribute :jot_details, :pictures, :json, default: []

  validates :realty_dossier_uuid, presence: true
  # validates :title, presence: true
end
