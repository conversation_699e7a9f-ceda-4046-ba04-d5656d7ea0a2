# == Schema Information
#
# Table name: dossier_assets_comparisons
#
#  id                        :bigint           not null, primary key
#  agency_tenant_uuid        :uuid
#  assets_parts_comparison   :jsonb
#  buyer_guidance            :jsonb            not null
#  comparison_extra_details  :jsonb
#  comparison_slug           :string
#  comparison_title          :string
#  detailed_comparison       :jsonb            not null
#  discarded_at              :datetime
#  first_dossier_asset_uuid  :uuid
#  market_analysis           :jsonb            not null
#  property_insights         :jsonb            not null
#  realty_dossier_uuid       :uuid
#  second_dossier_asset_uuid :uuid
#  translations              :jsonb
#  user_notes_on_comparison  :jsonb
#  uuid                      :uuid
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#
# Indexes
#
#  index_dossier_assets_comparisons_on_agency_tenant_uuid         (agency_tenant_uuid)
#  index_dossier_assets_comparisons_on_discarded_at               (discarded_at)
#  index_dossier_assets_comparisons_on_first_dossier_asset_uuid   (first_dossier_asset_uuid)
#  index_dossier_assets_comparisons_on_realty_dossier_uuid        (realty_dossier_uuid)
#  index_dossier_assets_comparisons_on_second_dossier_asset_uuid  (second_dossier_asset_uuid)
#  index_dossier_assets_comparisons_on_uuid                       (uuid)
#
class DossierAssetsComparison < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  include Discard::Model

  belongs_to :first_dossier_asset, primary_key: 'uuid', class_name: 'DossierAsset',
                                   foreign_key: 'first_dossier_asset_uuid', optional: true
  has_many :first_asset_parts, through: :first_dossier_asset,
                               source: :dossier_asset_parts

  belongs_to :second_dossier_asset, primary_key: 'uuid', class_name: 'DossierAsset',
                                    foreign_key: 'second_dossier_asset_uuid', optional: true
  has_many :second_asset_parts, through: :second_dossier_asset,
                                source: :dossier_asset_parts

  belongs_to :realty_dossier, primary_key: 'uuid', class_name: 'RealtyDossier',
                              foreign_key: 'realty_dossier_uuid', optional: true

  def first_sale_listing
    first_dossier_asset&.default_sale_listing
  end

  def second_sale_listing
    second_dossier_asset&.default_sale_listing
  end

  def un_allocated_photos
    # TODO: - implement and use this on fe
    []
  end

  # May 2025 - below from when I wanted to use ...
  # on the front end to display different parts of the asset next to each otheri
  # def full_comparisons
  #   comparison_d = {}
  #   assets_parts_comparison['comparisons'].map do |comparison|
  #     comparison_slug = "#{comparison['first_property_part']}-vs-#{comparison['second_property_part']}"
  #     asset_part_a = first_dossier_asset.dossier_asset_parts.where(
  #       asset_part_slug: comparison['first_property_part']
  #     )&.first
  #     asset_part_b = second_dossier_asset.dossier_asset_parts.where(
  #       asset_part_slug: comparison['second_property_part']
  #     )&.first
  #     fuller_comparison = { comparison_text: comparison['comparison_text'] }
  #     fuller_comparison[:part_a] = if asset_part_a
  #                                    {
  #                                      id: asset_part_a.id,
  #                                      asset_part_slug: asset_part_a.asset_part_slug,
  #                                      asset_part_title: asset_part_a.asset_part_title,
  #                                      asset_part_description: asset_part_a.asset_part_description,
  #                                      asset_part_photos_json_summary: asset_part_a.asset_part_photos_json_summary,
  #                                      asset_part_type: asset_part_a.asset_part_type,
  #                                      asset_part_main_color: asset_part_a.asset_part_main_color,
  #                                      asset_part_secondary_color: asset_part_a.asset_part_secondary_color,
  #                                      asset_part_condition: asset_part_a.asset_part_condition,
  #                                      asset_part_style: asset_part_a.asset_part_style,
  #                                      asset_part_significant_items: asset_part_a.asset_part_significant_items,
  #                                      asset_part_unique_features: asset_part_a.asset_part_unique_features,
  #                                      asset_part_details: asset_part_a.asset_part_details
  #                                    }
  #                                  else
  #                                    {}
  #                                  end
  #     fuller_comparison[:part_b] = if asset_part_b
  #                                    {
  #                                      id: asset_part_b.id,
  #                                      asset_part_slug: asset_part_b.asset_part_slug,
  #                                      asset_part_title: asset_part_b.asset_part_title,
  #                                      asset_part_description: asset_part_b.asset_part_description,
  #                                      asset_part_photos_json_summary: asset_part_b.asset_part_photos_json_summary,
  #                                      asset_part_type: asset_part_b.asset_part_type,
  #                                      asset_part_main_color: asset_part_b.asset_part_main_color,
  #                                      asset_part_secondary_color: asset_part_b.asset_part_secondary_color,
  #                                      asset_part_condition: asset_part_b.asset_part_condition,
  #                                      asset_part_style: asset_part_b.asset_part_style,
  #                                      asset_part_significant_items: asset_part_b.asset_part_significant_items,
  #                                      asset_part_unique_features: asset_part_b.asset_part_unique_features,
  #                                      asset_part_details: asset_part_b.asset_part_details
  #                                    }
  #                                  else
  #                                    {}
  #                                  end
  #     comparison_d[comparison_slug] = fuller_comparison
  #   end
  #   comparison_d
  # end

  def self.create_or_update_from_llm_response(json_response, comparison_options = {})
    data = json_response.is_a?(String) ? JSON.parse(json_response) : json_response

    # Extract necessary attributes
    comparison_slug = comparison_options[:comparison_slug] || "property-comparison-#{Time.now.to_i}"
    comparison_title = data.dig('overall_property_comparison', 'comparison_title') || 'Property Comparison'
    realty_dossier_uuid = comparison_options[:realty_dossier_uuid]
    realty_dossier = RealtyDossier.find_by_uuid(realty_dossier_uuid)
    first_dossier_asset_uuid = realty_dossier.primary_dossier_asset.uuid
    second_dossier_asset_uuid = comparison_options[:second_dossier_asset_uuid]

    comparison = find_by(comparison_slug: comparison_slug) || new

    # Structure the enhanced data
    detailed_comparison = {
      overall: data['overall_property_comparison'],
      room_comparisons: data['rooms_or_sections_comparisons']
    }

    property_insights = {
      first_property: data.dig('property_details', 'first_property') || {},
      second_property: data.dig('property_details', 'second_property') || {}
    }

    buyer_guidance = {
      questions: data.dig('overall_property_comparison', 'questions_for_seller') || {},
      tasks: data.dig('overall_property_comparison', 'recommended_tasks') || {},
      recommendation: data['recommendation'] || {}
    }

    comparison.assign_attributes(
      realty_dossier_uuid: realty_dossier_uuid,
      first_dossier_asset_uuid: first_dossier_asset_uuid,
      second_dossier_asset_uuid: second_dossier_asset_uuid,
      comparison_slug: comparison_slug,
      comparison_title: comparison_title,
      detailed_comparison: detailed_comparison,
      property_insights: property_insights,
      market_analysis: data['market_analysis'] || {},
      buyer_guidance: buyer_guidance,
      uuid: comparison.uuid || SecureRandom.uuid
    )

    comparison.save!
    comparison
  end
end
