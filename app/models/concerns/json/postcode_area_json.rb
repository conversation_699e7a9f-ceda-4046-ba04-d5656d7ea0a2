# app/models/concerns/sold_transaction_summary.rb
module Json::PostcodeAreaJson
  extend ActiveSupport::Concern

  def as_ai_gen_json(options = {})
    # deliberatly not including postcode_area_geojson
    as_json(options.merge(
              only: %i[
                uuid postal_code
                post_town built_up_area index_of_multiple_deprivation
                average_household_income predominant_property_type
                uk_grid_reference area_population area_households
              ],
              # methods: %i[formatted_sold_price short_formatted_sold_price],
              include: {}
            ))
  end
end
