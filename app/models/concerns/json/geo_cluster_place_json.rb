# app/models/concerns/sold_transaction_summary.rb
module Json::GeoClusterPlaceJson
  extend ActiveSupport::Concern

  def as_ai_gen_json(options = {})
    as_json(options.merge(
              only: %i[
                place_within_one_mile place_within_cluster distance_between
                id uuid
              ],
              methods: %i[
                google_place_id
                relevant_place_title relevant_place_slug
                relevant_place_description relevant_place_latitude relevant_place_longitude
              ],
              include: {}
            ))
  end
end
