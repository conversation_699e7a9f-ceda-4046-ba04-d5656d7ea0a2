# app/models/concerns/sold_transaction_summary.rb
module Json::RelevantPlaceJson
  extend ActiveSupport::Concern

  def as_ai_gen_json(_options = {})
    as_json(options.merge(
              only: %i[
                id uuid relevant_place_title relevant_place_slug
                relevant_place_description relevant_place_latitude relevant_place_longitude
              ],
              methods: [],
              include: {
                geo_cluster_places: {
                  only: %i[place_within_one_mile place_within_cluster distance_between] # Add the fields you want here
                }
              }
            ))
  end

  def as_detailed_json(options = {})
    as_json(options.merge(
              only: %i[
                id uuid relevant_place_flags relevant_place_title relevant_place_slug
                relevant_place_description relevant_place_tags agency_tenant_uuid
                google_place_id other_place_id relevant_place_latitude relevant_place_longitude
                created_at updated_at street_number street_name postal_code province
                city region country relevant_place_details
              ],
              methods: [],
              include: {
                geo_cluster_places: {}
              }
            ))
  end

  def as_summary_json(options = {})
    as_json(options.merge(
              only: %i[
                id uuid relevant_place_title relevant_place_slug
                relevant_place_description relevant_place_latitude relevant_place_longitude
              ],
              methods: [],
              include: {
                geo_cluster_places: {
                  only: %i[place_within_one_mile place_within_cluster distance_between] # Add the fields you want here
                }
              }
            ))
  end
end
