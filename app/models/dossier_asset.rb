# == Schema Information
#
# Table name: dossier_assets
#
#  id                            :bigint           not null, primary key
#  agency_tenant_uuid            :uuid
#  ai_comparability_score        :integer          default(0)
#  asset_condition               :string
#  asset_description             :string
#  asset_details                 :jsonb
#  asset_flags                   :integer          default(0), not null
#  asset_main_color              :string
#  asset_secondary_color         :string
#  asset_significant_items       :text             default([]), is an Array
#  asset_slug                    :string
#  asset_style                   :string
#  asset_title                   :string
#  asset_unique_features         :jsonb
#  discarded_at                  :datetime
#  dossier_asset_parts_count     :integer          default(0)
#  info_sources_count            :integer          default(0)
#  is_good_comparable_to_primary :boolean          default(FALSE)
#  is_most_comparable_to_primary :boolean          default(FALSE)
#  is_primary_dossier_asset      :boolean          default(FALSE)
#  origin_sale_listing_uuid      :uuid
#  property_is_for_rent          :boolean          default(FALSE)
#  property_is_for_sale          :boolean          default(TRUE)
#  realty_asset_uuid             :uuid
#  realty_dossier_uuid           :uuid
#  translations                  :jsonb
#  user_comparability_score      :integer          default(0)
#  user_notes_on_asset           :jsonb
#  uuid                          :uuid
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#
# Indexes
#
#  index_dossier_assets_on_agency_tenant_uuid   (agency_tenant_uuid)
#  index_dossier_assets_on_asset_flags          (asset_flags)
#  index_dossier_assets_on_discarded_at         (discarded_at)
#  index_dossier_assets_on_realty_asset_uuid    (realty_asset_uuid)
#  index_dossier_assets_on_realty_dossier_uuid  (realty_dossier_uuid)
#  index_dossier_assets_on_uuid                 (uuid)
#
class DossierAsset < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  extend Mobility
  translates :ai_gen_da_title, :ai_gen_da_description

  # store_attribute :asset_part_details, :raw_llm_json, :json, default: {}
  # store_attribute :asset_part_details, :asset_part_size, :string, default: ''
  # store_attribute :asset_part_details, :asset_part_area, :string, default: ''

  store_attribute :asset_details, :description_medium, :string, default: ''

  store_attribute :asset_details, :walk_through_description, :string, default: ''
  # store_attribute :asset_details, :uuids_of_other_similar_st_epcs, :json, default: []
  store_attribute :asset_details, :estimated_fair_value_price, :decimal, default: 0.0
  store_attribute :asset_details, :is_asking_price_competitive_or_overpriced, :string, default: ''
  store_attribute :asset_details, :reasoning_content, :json, default: {}
  store_attribute :asset_details, :property_inventory, :json, default: {}

  store_attribute :asset_details, :summary_sections_or_rooms, :json, default: {}
  # below will save sections_or_rooms as I add them thorugh analysing photos
  store_attribute :asset_details, :cumulative_sections_or_rooms, :json, default: {}

  # belongs_to :sold_transaction, class_name: 'SoldTransaction', foreign_key: 'sold_transaction_uuid', primary_key: :uuid, optional: false
  # has_one :postcode_area, through: :sold_transaction # , source: :postcode_area

  belongs_to :realty_dossier, class_name: 'RealtyDossier', foreign_key: 'realty_dossier_uuid',
                              # important that optional below is false
                              primary_key: :uuid, optional: false
  counter_culture :realty_dossier, column_name: 'dossier_assets_count'

  belongs_to :origin_sale_listing, class_name: 'SaleListing',
                                   primary_key: 'uuid', foreign_key: 'origin_sale_listing_uuid', optional: true
  # counter_culture :origin_sale_listing, column_name: 'dossier_assets_count'

  # has_one :origin_realty_asset, class_name: 'RealtyAsset',
  #                               through: :origin_sale_listing,
  #                               source: :realty_asset

  belongs_to :realty_asset,
             primary_key: 'uuid', foreign_key: 'realty_asset_uuid',
             class_name: 'RealtyAsset', optional: true
  # counter_culture :realty_asset, column_name: 'dossier_assets_count'

  # def realty_asset
  #   origin_realty_asset || deprecated_realty_asset
  # end

  has_many :asset_photos, through: :realty_asset # , source: :
  # above seems to provide asset_photo_ids which returns uuids
  # adding below so I can do sanity checks
  def asset_photo_short_ids
    asset_photos.kept.pluck :id
  end

  delegate :count_toilets, :count_garages, :count_bedrooms,
           :area_unit, :plot_area, :constructed_area, :year_construction,
           :latitude, :longitude, :city, :region, :country, :street_name,
           :neighborhood,
           :formatted_constructed_area,
           :street_number, :postal_code, :street_address,
           :count_bathrooms, to: :realty_asset, allow_nil: true

  has_many :found_sale_listings,
           class_name: 'SaleListing',
           through: :realty_asset,
           source: :sale_listings

  has_many :photo_info_sources,
           #  as: :associable,
           primary_key: 'uuid',
           class_name: 'DossierInfoSourceFromPhoto',
           foreign_key: 'dossier_asset_uuid'

  has_many :dossier_asset_contextual_records, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid'
  has_many :contextual_records, through: :dossier_asset_contextual_records, class_name: 'ContextualRecord'
  has_many :neighbourhood_records, through: :dossier_asset_contextual_records, class_name: 'ContextualRecordFromGetthedata'
  has_one :dossier_asset_contextual_record, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid'
  has_one :main_neighbourhood_record, through: :dossier_asset_contextual_record,
                                      source: :neighbourhood_record, class_name: 'ContextualRecordFromGetthedata'

  has_many :dossier_asset_parts, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid'
  has_many :info_sources, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid', class_name: 'DossierInfoSource'

  validates :realty_asset_uuid, uniqueness: { scope: %i[realty_dossier_uuid is_primary_dossier_asset], message: 'Primary asset already exists for this dossier' }
  validates :realty_dossier_uuid, presence: true

  validates :is_most_comparable_to_primary, uniqueness: { scope: :realty_dossier_uuid }, if: -> { is_most_comparable_to_primary }

  def initial_listing_summary_for_ai
    ilsfa = ''
    ilsfa += "This is a summary of the listing for the property at #{realty_asset.street_address}.\n"
    ilsfa += "The property is located in #{realty_asset.city}, #{realty_asset.region}, #{realty_asset.country}.\n"
    ilsfa += "Title: #{default_sale_listing.title}.\n"
    ilsfa += "Description: #{default_sale_listing.description}.\n"
    # ilsfa+= "The property is listed for #{default_sale_listing&.price}.\n"
    # ilsfa+= "The property is #{realty_asset.property_significant_items}.\n"
    # ilsfa+= "The property is #{realty_asset.property_main_color}.\n"
    # ilsfa+= "The property is #{realty_asset.property_unique_features}.\n"
    # ilsfa+= "The property is #{realty_asset.property_significant_items}.\n"
    # ilsfa+= "The property is #{realty_asset.property_style}.\n"
    ilsfa
  end

  def default_sale_listing
    origin_sale_listing || found_sale_listings&.first
  end
  # 13 may - finally replaced the below with the above
  # # 6 may 2025 -  below is a terrible idea!!
  # # If a realty asset gets a new sale listing, that could suddenly
  # # become the default_sale_listing....
  # def default_sale_listing
  #   # Made worse by this being used in realty_dossier_instance.primary_sale_listing
  #   sale_listings&.first
  #   # original idea ws that in the future we can have multiple sale listings for a single asset
  #   # for now we just return the first one
  # end

  def asset_title_calc
    asset_title || default_sale_listing&.title
  end

  def create_and_save_composite_photo_plan_b
    service = PlaywrightBrowser::LocalScreenshots.new
    # destination_filename = "dossier_#{realty_dossier.id}_listing_#{default_sale_listing.id}"

    # retrieval_endpoint = "http://damp-violet.lvh.me:9100/rawdossiers/#{dossier.uuid}/listing_photos/#{sale_listing.uuid}"
    retrieval_endpoint = 'http://damp-violet.lvh.me:3333/api_public/v4/dossiers/show_image'
    retrieval_endpoint += "/#{realty_dossier.uuid}/listing/#{default_sale_listing.uuid}/plan_b"

    screenshot_path = service.simple_capture(retrieval_endpoint, composite_photo_file_path_b)
    puts "Plan b screen saved to: #{screenshot_path}" if screenshot_path
    screenshot_path
  end

  def composite_photo_file_path_b
    destination_directory = Rails.root.join('app', 'assets', 'images', 'h2c', 'screens_for_ai', 'plan_b')
    FileUtils.mkdir_p(destination_directory) # Create directory if it doesn't exist
    destination_directory.join(composite_photo_file_name)
  end

  def composite_photo_file_default_path
    destination_directory = Rails.root.join('app', 'assets', 'images', 'h2c', 'screens_for_ai')
    FileUtils.mkdir_p(destination_directory) # Create directory if it doesn't exist
    destination_directory.join(composite_photo_file_name)
  end

  # def composite_photo_url_plan_b
  #   "/assets/h2c/screens_for_ai/plan_b/#{composite_photo_file_name}"
  # end

  def create_and_save_composite_photo
    # wed 9 apr
    # big problem with below is that it currently can only be run locally..
    service = PlaywrightBrowser::LocalScreenshots.new
    # tried below but didn't work well
    # service = FerrumBrowser::LocalScreenshots.new

    retrieval_endpoint = 'http://damp-violet.lvh.me:3333/api_public/v4/dossiers/show_image'
    retrieval_endpoint += "/#{realty_dossier.uuid}/listing/#{default_sale_listing.uuid}"

    screenshot_path = service.simple_capture(retrieval_endpoint, composite_photo_file_default_path)
    puts "Main screen saved to: #{screenshot_path}" if screenshot_path
    screenshot_path

    # screenshot_path = service.capture_dossier_asset_images(realty_dossier, default_sale_listing)
    # puts "Screenshot saved to: #{screenshot_path}" if screenshot_path
    # screenshot_path
  end

  def composite_photo_file_name
    file_extension = 'jpeg'
    "dossier_#{realty_dossier.id}_listing_#{default_sale_listing&.id}.#{file_extension}"
  end

  def composite_photo_url
    # this is used by list_for_superwiser.jbuilder
    # should rename composite_photos_image_url
    # as it is the actual image rather than the html for it
    "/assets/h2c/screens_for_ai/#{composite_photo_file_name}"
  end

  def analyse_composite_photo_plan_b(force_plan_b_photos: false)
    if force_plan_b_photos
      # images_to_return = []
      default_sale_listing.listing_photos.each do |listing_photo|
        # images_to_return << listing_photo if
        listing_photo.photo_title = 'wrong'
        listing_photo.save!
      end
    end
    raise 'No plan b photos' if default_sale_listing&.plan_b_images.blank?

    create_and_save_composite_photo_plan_b unless composite_photo_file_path_b.exist?

    unless composite_photo_file_path_b.exist?
      puts "Plan b composite image file not found - screenshot fail?: #{composite_photo_file_path_b}"
      return
    end

    photos_evaluator = Ai::PropertyInsights::CompositePhotoDescriptionAnalyser.new(client_to_use: 'gemini-vision')
    analysis_params = {
      composite_photo_file_path: composite_photo_file_path_b,
      dossier_item: realty_dossier,
      target_dossier_asset: self,
      is_plan_b: true,
      max_photos_to_analyse: 30
    }
    analysis = photos_evaluator.analyse_composite_url(
      analysis_params
      # composite_photo_file_path_b, realty_dossier, self
    )
    analysis
  end

  # Analyse listing photos in batches if more than 20; otherwise in a single composite.
  # Returns an array of LLM interaction results (one per batch).
  def analyse_composite_photo
    listing_photos = default_sale_listing&.listing_photos&.kept
    raise 'No photos to analyze' if listing_photos.blank?

    batch_size = 10
    # 4 may 2025 - changed batch size from 20 to 10
    # seemed to make a difference when combined with switching from
    # o4-mini to app/controllers/api/realty_dossiers_controller.rb
    total_photos = listing_photos.size
    service = PlaywrightBrowser::LocalScreenshots.new
    photos_evaluator = Ai::PropertyInsights::CompositePhotoDescriptionAnalyser.new(client_to_use: 'gemini-vision')
    interactions = []

    # Base endpoint for composite screenshot generation
    base_endpoint = 'http://damp-violet.lvh.me:3333/api_public/v4/dossiers/show_image'
    endpoint_path = "/#{realty_dossier.uuid}/listing/#{default_sale_listing.uuid}"

    if total_photos <= batch_size
      # Single batch: full composite
      create_and_save_composite_photo unless composite_photo_file_default_path.exist?
      unless composite_photo_file_default_path.exist?
        puts "Composite image file not found - screenshot may have failed: #{composite_photo_file_default_path}"
        return interactions
      end
      acceptable_image_ids_for_llm_call = default_sale_listing.ordered_batch_photo_ids(offset: 0, limit: total_photos)

      analysis_params = {
        acceptable_image_ids_for_llm_call: acceptable_image_ids_for_llm_call,
        composite_photo_file_path: composite_photo_file_default_path,
        dossier_item: realty_dossier,
        target_dossier_asset: self,
        max_photos_to_analyse: total_photos,
        batch_offset: 0,
        batch_limit: total_photos,
        screenshot_dimensions: service.viewport,
        screenshot_url: composite_photo_url,
        llm_model: 'gemini-vision'
      }
      interactions << photos_evaluator.analyse_composite_url(analysis_params)
    else
      # Multiple batches
      (0...total_photos).step(batch_size) do |offset|
        limit = [batch_size, total_photos - offset].min
        batch_path = composite_photo_file_path_for_batch(offset, limit)
        # Capture batch if not already present
        unless batch_path.exist?
          url = "#{base_endpoint}#{endpoint_path}?offset=#{offset}&limit=#{limit}"
          service.simple_capture(url, batch_path)
        end
        acceptable_image_ids_for_llm_call = default_sale_listing.ordered_batch_photo_ids(offset: offset, limit: limit)
        analysis_params = {
          acceptable_image_ids_for_llm_call: acceptable_image_ids_for_llm_call,
          composite_photo_file_path: batch_path,
          dossier_item: realty_dossier,
          target_dossier_asset: self,
          max_photos_to_analyse: limit,
          batch_offset: offset,
          batch_limit: limit,
          screenshot_dimensions: service.viewport,
          screenshot_url: composite_photo_url_for_batch(offset, limit),
          llm_model: 'gemini-vision'
        }
        interactions << photos_evaluator.analyse_composite_url(analysis_params)
      end
    end

    interactions
  rescue StandardError => e
    Rails.logger.error("Photo analysis failed for dossier #{realty_dossier.id}: #{e.message}")
    raise
  end

  # Pathname for a batched composite image, based on offset and limit
  def composite_photo_file_path_for_batch(offset, limit)
    default_path = composite_photo_file_default_path
    ext = default_path.extname
    base = default_path.basename(ext).to_s
    batch_filename = "#{base}_offset#{offset}_limit#{limit}#{ext}"
    default_path.parent.join(batch_filename)
  end

  # URL for a batched composite image, for embedding/public links
  def composite_photo_url_for_batch(offset, limit)
    batch_filename = composite_photo_file_path_for_batch(offset, limit).basename.to_s
    "/assets/h2c/screens_for_ai/#{batch_filename}"
  end

  def analyse_photos(max_photos_to_analyze = 2)
    # could discard photos that are rather small or in some way inadequate here
    raise 'No photos to analyze' if realty_dossier.primary_sale_listing&.listing_photos.blank?

    photos_evaluator = Ai::PropertyInsights::LlmDossierPhotosEvaluator.new(client_to_use: 'gemini-vision')
    # dossier_item = dossier_asset.realty_dossier

    realty_asset_photos = realty_dossier.primary_sale_listing.listing_photos

    @existing_rooms = [] # Store existing rooms for this analysis session
    realty_asset_photos.limit(max_photos_to_analyze).each do |photo|
      analysis = photos_evaluator.process_single_photo(
        realty_dossier, photo, existing_rooms: @existing_rooms
      )
      update_existing_rooms(analysis, photo)
    rescue StandardError => e
      log_error("Failed to process photo #{photo.id} for dossier #{realty_dossier.id}", e)
    end # Return updated rooms array

    # photos_evaluator.analyze(self, max_photos_to_analyze)
  rescue StandardError => e
    Rails.logger.error("Photo analysis failed for dossier #{realty_dossier.id}: #{e.message}")
    raise
  end

  # Updates the existing rooms array based on analysis
  def update_existing_rooms(analysis, photo)
    msrd = analysis&.main_section_or_room_details
    if msrd.blank?
      Rails.logger.error("No main section or room details found in analysis for dossier #{realty_dossier.id}")
      nil
    else
      descriptive_title = msrd['descriptive_title']
      asset_part_slug = msrd['descriptive_title'].parameterize
      if msrd['matches_existing_room'] == 'new_room_section'
        self.cumulative_sections_or_rooms ||= {}
        self.cumulative_sections_or_rooms[descriptive_title.parameterize] = msrd.except('matches_existing_room', 'is_main_section_or_room')
        save!
      else
        asset_part_slug = msrd['matches_existing_room'].downcase
      end
      relevant_dossier_asset_part = dossier_asset_parts.find_or_create_by(
        asset_part_slug: asset_part_slug # sections_or_rooms_title.parameterize
      )
      realty_asset_photo_uuids = relevant_dossier_asset_part.realty_asset_photo_uuids
      realty_asset_photo_uuids.push(photo.uuid) unless realty_asset_photo_uuids.include?(photo.uuid)
      # realty_asset_photo_uuids.uniq!
      unique_features = msrd['unique_features'] || {}
      # asset_part_size = msrd['size'] || ''
      asset_part_type = msrd['section_or_room_type'].downcase || 'unknown'
      valid_values = DossierAssetPart.asset_part_types.keys
      asset_part_type = "new_pt_#{asset_part_type}" unless valid_values.include?(asset_part_type)
      relevant_dossier_asset_part.update!(
        # once I migrate realty_asset_photos_count will add below:
        # realty_asset_photos_count: realty_asset_photo_uuids.count,
        realty_asset_photo_uuids: realty_asset_photo_uuids,
        asset_part_unique_features: unique_features,
        raw_llm_json: msrd,
        # asset_part_size: asset_part_size,
        asset_part_title: descriptive_title,
        asset_part_type: asset_part_type
      )
    end
  end

  def classify_property_sections
    classifier = Ai::PropertyInsights::SectionsClassifier.new(client_to_use: 'gemini')
    # analysis_params = {
    #   dossier_item: realty_dossier,
    #   target_dossier_asset: self,
    # }
    analysis = classifier.classify_rooms(
      realty_dossier, self
    )
    analysis
  end
end
