# == Schema Information
#
# Table name: sale_listings
#
#  id                             :bigint           not null, primary key
#  agency_tenant_uuid             :uuid
#  agency_uuid                    :uuid
#  archived                       :boolean          default(FALSE)
#  catchy_title                   :string           default("")
#  cloned_from_uuid               :string
#  commission_cents               :bigint           default(0), not null
#  commission_currency            :string           default("EUR"), not null
#  currency                       :string
#  description_bullet_points      :jsonb
#  description_long               :text             default("")
#  description_medium             :string           default("")
#  description_short              :string           default("")
#  design_style                   :string
#  details_of_rooms               :jsonb
#  discarded_at                   :datetime
#  extra_sale_details             :jsonb
#  furnished                      :boolean          default(FALSE)
#  hide_map                       :boolean          default(FALSE)
#  highlighted                    :boolean          default(FALSE)
#  host_on_create                 :string           default("unknown_host"), not null
#  import_url                     :string
#  is_ai_generated_listing        :boolean          default(FALSE)
#  listing_pages_count            :integer          default(0), not null
#  listing_slug                   :string
#  listing_tags                   :string           default([]), is an Array
#  llm_interaction_uuid           :uuid
#  llm_interactions_count         :integer          default(0)
#  main_video_url                 :string
#  obscure_map                    :boolean          default(FALSE)
#  page_section_listings_count    :integer          default(0), not null
#  position_in_list               :integer
#  price_sale_current_cents       :bigint           default(0), not null
#  price_sale_current_currency    :string           default("EUR"), not null
#  price_sale_original_cents      :bigint           default(0), not null
#  price_sale_original_currency   :string           default("EUR"), not null
#  property_board_items_count     :integer          default(0), not null
#  publish_from                   :datetime
#  publish_till                   :datetime
#  realty_asset_uuid              :uuid
#  reference                      :string
#  related_urls                   :jsonb
#  reserved                       :boolean          default(FALSE)
#  sale_listing_features          :jsonb
#  sale_listing_flags             :integer          default(0), not null
#  sale_listing_gen_prompt        :text
#  service_charge_yearly_cents    :bigint           default(0), not null
#  service_charge_yearly_currency :string           default("EUR"), not null
#  site_visitor_token             :string
#  sl_photos_count                :integer          default(0), not null
#  sl_uprn                        :string
#  translations                   :jsonb
#  unique_url                     :string           default("")
#  user_uuid                      :uuid
#  uuid                           :uuid
#  versions_count                 :integer          default(0), not null
#  visible                        :boolean          default(FALSE)
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  psq_visit_id                   :bigint
#  realty_asset_id                :integer
#
# Indexes
#
#  index_sale_listings_on_agency_tenant_uuid         (agency_tenant_uuid)
#  index_sale_listings_on_discarded_at               (discarded_at)
#  index_sale_listings_on_highlighted                (highlighted)
#  index_sale_listings_on_is_ai_generated_listing    (is_ai_generated_listing)
#  index_sale_listings_on_listing_slug               (listing_slug)
#  index_sale_listings_on_price_sale_current_cents   (price_sale_current_cents)
#  index_sale_listings_on_price_sale_original_cents  (price_sale_original_cents)
#  index_sale_listings_on_realty_asset_uuid          (realty_asset_uuid)
#  index_sale_listings_on_reference                  (reference)
#  index_sale_listings_on_sale_listing_flags         (sale_listing_flags)
#  index_sale_listings_on_uuid                       (uuid)
#  index_sale_listings_on_visible                    (visible)
#
class SaleListing < BaseListing
  self.table_name = 'sale_listings'
  # Above to solve error when I tried to access sale_listing via administrate:
  # base_listing  has no table configured. Set one with BaseListing.table_name=

  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid')
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: true)
  include Discard::Model
  has_paper_trail

  has_many :originating_dossier_assets,
           class_name: 'DossierAsset',
           primary_key: 'uuid', foreign_key: 'origin_sale_listing_uuid'

  # # using obscure_map below till I migrate is_summary_listing to a boolean
  # default_scope { where(obscure_map: false) }

  # visitable :psq_visit
  # # below only needed for counter_cache - above would normally take care of r/n
  # belongs_to :visit, foreign_key: 'psq_visit_id', optional: true, class_name: 'Ahoy::Visit', counter_cache: true
  # belongs_to :site_visitor, foreign_key: 'site_visitor_token', primary_key: 'ahoy_visitor_token',
  #                           optional: true, class_name: 'SiteVisitor', counter_cache: false

  store_attribute :extra_sale_details, :raw_otm_data, :json, default: {}
  # 18 mar 2025 - above is a temporary thing to enable
  # me to dig deeper into otm data
  store_attribute :extra_sale_details, :buyer_context, :json, default: {}
  # idea of above is to store advice from an llm regarding neighbourhood_details,
  #  pros, cons etc...
  store_attribute :extra_sale_details, :section_descriptions, :json, default: {}

  # Nov 2024 - I now have a sale_listing_features jsonb col..
  # store_attribute :extra_sale_details, :sale_listing_features, :json, default: {}

  def sl_uprn
    ''
    # feb 2025 TODO - add sl_uprn via migration
    # and maybe rl_uprn (for rental listings...)
  end

  # Getter for sale_listing_features
  def sale_listing_features
    super || {}
  end

  # Setter for sale_listing_features
  def sale_listing_features=(value)
    value = value.is_a?(Hash) ? value : {}
    super(value)
  end

  # Add a list of features to the existing sale_listing_features
  def add_features(features)
    features_array = validate_features_list(features)

    new_features = features_array.each_with_object({}) do |feature, hash|
      key = feature.to_s.parameterize(separator: '-') # Converts to key-friendly format
      hash[key.to_sym] = {
        label: feature,
        feature_present: true
      }
    end
    # new_features = features_array.index_with(true) # Convert list to hash { feature: true }
    current_features = sale_listing_features
    updated_features = current_features.merge(new_features)
    self.sale_listing_features = updated_features
  end

  # Remove a list of features from the existing sale_listing_features
  def remove_features(features)
    features_array = validate_features_list(features)
    current_features = sale_listing_features
    updated_features = current_features.except(*features_array)
    self.sale_listing_features = updated_features
  end

  before_validation :fix_nil_values, on: %i[create update]
  # https://karolgalanciak.com/blog/2017/10/29/the-case-against-exotic-usage-of-before-validate-callbacks/
  # Might consider alternative solution as per above later
  def fix_nil_values
    return unless self['price_sale_current_currency'].nil?

    self['price_sale_current_currency'] = 'USD'
  end

  extend Mobility
  translates :slug, :title, :title_meta, :description, :description_meta,
             :ai_gen_title, :ai_gen_description
  #  :title_summary, :description_summary
  # #touch: true
  # mobility gem provides a uniqueness validator
  # validates :slug, uniqueness: true
  # which will work with acts_as_tenant using:
  validates_uniqueness_to_tenant :slug, uniqueness: true
  # validates_uniqueness_of :import_url, allow_nil: true, scope: :agency_tenant_uuid
  # March 2024 - decided that above can be duplicated - eg.....
  # below will has a hash suffix added to make each one unique:
  validates_uniqueness_of :unique_url, allow_nil: true, scope: :agency_tenant_uuid
  # Feb 2023 - below validation seems dumbarse - why would I would to use a mobility slug field??
  # Will have to revisit when I am sure changing it will not break something else
  # validates :slug, presence: true #, uniqueness: true
  # Apr 2023 - removed above validation

  # has_many :purchase_evaluations, class_name: 'PurchaseEvaluation', foreign_key: 'sale_listing_uuid',
  #                                 primary_key: 'uuid'

  belongs_to :realty_asset, primary_key: 'uuid', foreign_key: 'realty_asset_uuid', optional: true
  has_many :price_estimates, primary_key: 'uuid', foreign_key: 'listing_uuid'

  def price_estimates_summary
    price_estimates.as_json(
      only: %w[uuid estimated_price_cents listing_uuid estimate_currency estimate_title
               price_at_time_of_estimate_cents estimate_details
               estimate_text estimator_name is_ai_estimate is_for_sale_listing
               is_for_rental_listing percentage_above_or_below created_at updated_at],
      methods: %w[formatted_estimated_price]
    )
  end
  # ... other SaleListing attributes and methods ...

  # SaleListing.last.add_llm_interaction(LlmInteraction.last, 'prediction', { is_primary: true })

  # below 3 were suggested by chatgpt- might use later
  # def add_llm_interaction(llm_interaction, association_type, metadata = {})
  #   llm_interaction_associations.create!(
  #     llm_interaction: llm_interaction,
  #     association_type: association_type,
  #     association_metadata: metadata,
  #     agency_tenant_uuid: agency_tenant_uuid
  #   )
  # end

  # def get_llm_interactions_by_type(association_type)
  #   llm_interactions.joins(:llm_interaction_associations).where(llm_interaction_associations: { association_type: association_type, associable_type: 'SaleListing', associable_uuid: uuid })
  # end

  # def get_primary_llm_interaction
  #   llm_interactions.joins(:llm_interaction_associations).where(llm_interaction_associations: { associable_type: 'SaleListing', associable_uuid: uuid, association_metadata: { is_primary: true } }).first
  # end

  # has_many :llm_interaction_associations, as: :associable, primary_key: 'uuid'
  # has_many :llm_interactions, through: :llm_interaction_associations, source: :llm_interaction

  # has_many :dossier_listings, primary_key: 'uuid', foreign_key: 'associated_listing_uuid'

  has_many :llm_interaction_with_sale_listings, primary_key: 'uuid', foreign_key: 'sale_listing_uuid'

  def llm_interaction_with_sale_listing
    raise 'There should only be one llm_interaction_with_sale_listing' if llm_interaction_with_sale_listings.kept.count > 1

    llm_interaction_with_sale_listings.kept.first
  end

  def update_new_llm_interaction(new_llm_interaction)
    # need to discard all other interactions with the same sale_listing_uuid
    # and only keep the latest one
    llm_interaction_with_sale_listings.discard_all
    #     # 12 mar 2025 - below does too much heavy lifting
    new_llm_interaction.store_sale_listing_data
    new_llm_interaction.reload
    # reload above needed to ensure undiscard works
    new_llm_interaction.undiscard
  end

  has_many :scrape_items, primary_key: 'uuid', foreign_key: 'sale_listing_uuid'
  # # May 2023: won't be using this going forward but re-enabling it
  # # for backward compatibility:
  # has_one :scrape, primary_key: 'uuid', foreign_key: 'sale_listing_uuid'

  # June 2022 - will probably make sense to have ListingPhoto
  # model instead of using RealtyAssetPhoto below
  has_many :listing_photos, -> { order 'sort_order asc' },
           class_name: 'RealtyAssetPhoto', primary_key: 'uuid', foreign_key: 'sale_listing_uuid'
  has_many :ordered_visible_listing_photos, lambda {
    not_flag_is_hidden.order('sort_order asc')
    # where(visible_for_sale_listing: true).order("sort_order asc")
  }, class_name: 'RealtyAssetPhoto', primary_key: 'uuid', foreign_key: 'sale_listing_uuid'

  # has_many :features, class_name: 'FeatureRef', foreign_key: 'sale_listing_uuid', primary_key: 'uuid'
  # has_many :feature_field_keys, through: :features, source: :feature_field_key # source refers to the association that I'll be reaching into

  # # Having many listing and rendering pages so that there can be the choice
  # # to for example render the same listing differently in a different context
  # has_many :listing_pages,
  #          foreign_key: 'listing_uuid',
  #          primary_key: 'uuid'
  # # Nov 2021 - the listing_page relationship is now polymorphic so no need to use sale_listing_uuid as fk
  # # The listable col on listing_page will say which type of listing this is
  # has_one :primary_listing_page, lambda {
  #                                  where is_primary: true
  #                                }, class_name: 'ListingPage', foreign_key: 'listing_uuid', primary_key: 'uuid'

  # # Mar 2022 - listing_pages are the join b/n rendering_pages and listings
  # # - was a dumb idea - should have joined directly to pages
  # # Nov 2021: Above and below for where a listing is to be rendered in a customized way
  # # Started working on it as a solution for SinglePropertyPages but hope to
  # # make more general use of it in PropertyWebBuilder too...
  # has_many :rendering_pages, through: :listing_pages, source: :page
  # has_one :primary_rendering_page, through: :primary_listing_page, source: :page

  # Moved below to base_listing:
  # has_many :page_section_listings, :as => :listable,
  #                                  foreign_key: "listing_uuid",
  #                                  primary_key: "uuid",
  #                                  foreign_type: "listable_type"
  # has_many :page_sections, :through => :page_section_listings

  # with_model_currency below allows the currency for
  # all currency fields to be set for a model
  # instance from the currency field
  monetize :price_sale_current_cents, with_model_currency: :currency
  monetize :price_sale_original_cents, with_model_currency: :currency
  monetize :commission_cents, with_model_currency: :currency
  monetize :service_charge_yearly_cents, with_model_currency: :currency

  def listings_model_name
    # better plural to match with routing endpoints
    'sale_listings'
  end

  def listing_geo_json
    return realty_asset.asset_polygon if realty_asset&.asset_polygon && realty_asset.asset_polygon['geometry']

    nil
    #       # RGeo::Cartesian.factory(srid: 4326).point(realty_asset.longitude, realty_asset.latitude)
  end

  def has_been_successfully_populated?
    # better logic to be added later
    # Purpose is to indicate to listing creator that
    # there is no need to rescrape this listing
    price_sale_current_cents.positive?
  end

  def formatted_display_price
    price_sale_current.format
  end

  def formatted_price_per_area_unit
    suffix = ' ㎡'
    suffix = ' sq ft' if realty_asset.area_unit == 'sqft'
    # constructed_area is a float - round(0) removes decimal places
    constructed_area.round(0).to_s + suffix
  end

  def price_per_area_unit_label
    price_per_area_unit_label = 'Price per ㎡'
    price_per_area_unit_label = 'Price per sq ft' if realty_asset.area_unit == 'sqft'
    price_per_area_unit_label
  end

  # def listing_show_url
  #   # slug_or_uuid = slug || uuid
  #   return Rails.application.routes.url_helpers.sale_listing_with_asset_path(slug:, locale: I18n.locale) if slug

  #   '/listing_slug_error'
  # end

  # def edit_url(admin_ui_locale)
  #   # @current_edit_path = url_for(edit_sale_listing_path(@realty_asset_uuid, locale: I18n.locale))
  #   Rails.application.routes.url_helpers.edit_sale_listing_path(
  #     uuid: realty_asset.uuid,
  #     locale: admin_ui_locale,
  #     content_locale: I18n.locale
  #   )
  # end

  def retrieve_meta_tags(url_base, canonical_url, site_name)
    meta_locale = 'es_ES'
    meta_locale = 'en_GB' if I18n.locale.to_s == 'en'
    full_image_url = if primary_image_url.present?
                       url_base + primary_image_url
                     else
                       ''
                     end
    meta_tags = [
      { title: },
      { description: },
      { canonical: canonical_url }
    ]
    meta_tags.push({
                     og: {
                       description: description_meta,
                       title: title_meta,
                       type: 'article',
                       url: canonical_url,
                       locale: meta_locale,
                       site_name:,
                       image: [
                         {
                           _: full_image_url,
                           width: 600,
                           height: 400
                         }
                       ]
                       # fb:page_id
                     }
                   })
    meta_tags.push({
                     twitter: {
                       card: 'summary_large_image',
                       description: description_meta,
                       title: title_meta,
                       image: {
                         _: full_image_url,
                         width: 100,
                         height: 100
                       },
                       creator: site_name,
                       site: site_name
                     }
                   })
    meta_tags
  end

  def self.class_mob_atts
    # for strong params in controller
    exp_mob_attr_names = []
    # Website.unique_instance.supported_locales_short.each do |locale|
    I18n.available_locales.each do |locale|
      SaleListing.mobility_attributes.each do |attr|
        exp_mob_attr_names.push "#{attr}_#{locale}"
      end
    end
    exp_mob_attr_names
  end

  def as_json_for_display(options = nil)
    as_json(
      { only: %w[
          uuid, design_style
        ],
        # section_descriptions
        methods: %w[title section_descriptions description
                    formatted_display_price
                    formatted_constructed_area
                    count_toilets count_garages
                    count_bathrooms count_bedrooms
                    area_unit plot_area constructed_area
                    street_number postal_code street_address
                    neighborhood
                    latitude longitude city region country street_name

                    year_construction] }.merge(
                      options || {}
                    )
    )
  end
  # dec 2022: - can't remember why I had below but it was causing
  # issues when creating a sale_listing for h2c
  # Issue was that realty_asset_uuid had already been taken...
  # validates :realty_asset_uuid, uniqueness: { scope: :import_url }

  before_destroy :destroy_realty_asset_if_unused

  def plan_b_images
    # 13 apr 2025 - for when LLM initially fails in describing a photo
    images_to_return = []
    listing_photos.each do |listing_photo|
      images_to_return << listing_photo if listing_photo.photo_title == 'wrong'
    end
    images_to_return
  end

  def image_descriptions
    descriptions_to_return = []
    listing_photos.each do |listing_photo|
      descriptions_to_return << listing_photo.details['raw_ai_analysis']
      #  if listing_photo.photo_title == 'wrong'
    end
    descriptions_to_return
  end

  def ordered_batch_photo_ids(offset: 0, limit: 10)
    offset = offset.to_i.clamp(0, Float::INFINITY)
    limit = limit.to_i.clamp(1, 100) # Cap limit at 100
    # listing_photos.kept.order(:id).offset(offset).limit(limit).pluck(:id)
    # grok really wanted me to keep above as it is supposed to be more efficient
    # Considering I have few photos and below was correct in my test I will go for it
    listing_photos.kept.pluck(:id).sort.slice(offset, limit)
  rescue ActiveRecord::StatementInvalid => e
    Rails.logger.error("Failed to fetch photo IDs: #{e.message}")
    []
  end

  private

  # Validate that the input is an array of symbols or strings
  def validate_features_list(features)
    raise ArgumentError, "Expected an array, got #{features.class}" unless features.is_a?(Array)

    features.map do |feature|
      unless feature.is_a?(String) || feature.is_a?(Symbol)
        raise ArgumentError,
              "Invalid feature: #{feature}. Expected a symbol or string."
      end

      feature.to_sym
    end
  end

  def destroy_realty_asset_if_unused
    return unless realty_asset.present?

    # Check if there are other sale listings associated with this realty_asset
    return unless realty_asset.rental_listings.none? && realty_asset.sale_listings.where.not(id:).none?

    realty_asset.destroy
  end
end
