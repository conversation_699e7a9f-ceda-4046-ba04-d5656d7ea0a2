module ApiMgmt::V4
  class GameMakerMgmtController < ApplicationController
    skip_before_action :verify_authenticity_token

    def init_game_with_prepped_listing_hash
      vendor_name = params[:vendor_name]
      scoot_subdomain = params[:scoot_subdomain]
      # retrieval_portal = params[:retrieval_portal]
      retrieval_end_point = params[:retrieval_end_point]
      game_bg_image_url = params[:game_bg_image_url]
      game_title = params[:game_title] || 'Price Guessing Game'
      game_description = params[:game_description] || 'Price Guessing Game'
      realty_game_slug = params[:realty_game_slug] || game_title.parameterize # 'regular-game'
      # 8 july 2025 - below was resulting in realty_game_slug being ignored
      # realty_game_slug = game_title.parameterize if game_title
      # scrape_item_data = params[:scrape_item_data]
      extracted_asset_data = realty_asset_params # params[:extracted_asset_data]
      extracted_listing_data = extracted_listing_data_params # params[:extracted_listing_data]
      # extracted_image_urls = params[:extracted_image_urls]

      # scrape_item_data = params[:scrape_item_data] || params[:scrape_item]

      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

      scoot = Scoot.find_or_create_by!(scoot_subdomain: scoot_subdomain)
      scoot.update!(
        supports_multiple_games: true,
        should_show_out_links: true
      )

      realty_game = scoot.realty_games.find_or_create_by!(
        realty_game_slug: realty_game_slug
      )
      #      game_bg_image_url: 'https://upload.wikimedia.org/wikipedia/commons/3/31/Sheffield_City.jpg',
      # game_title: rg3_title,
      # game_description: rg3_description,
      # game_default_locale: rg3_title.parameterize

      realty_game.update!(
        one_off_mgmt_code: params[:one_off_mgmt_code],
        is_one_off_game: params[:is_one_off_game],
        game_source_portal: vendor_name || 'vendor_missing',
        game_title: game_title,
        game_description: game_description,
        game_bg_image_url: game_bg_image_url,
        #  'A regular game to validate the RealtyGameListingCreator service',
        game_default_currency: params[:game_default_currency].presence || 'EUR',
        game_default_country: 'UK',
        # currently game_default_locale is serving as a workaround
        # for a global game slug
        game_default_locale: realty_game_slug,
        game_global_slug: realty_game_slug,
        game_start_at: 1.hour.ago,
        game_end_at: 1.week.from_now
      )

      listing_for_game = create_listing_from_prepped_content(
        extracted_asset_data, extracted_listing_data,
        params[:extracted_image_urls], retrieval_end_point
      )
      # listing_for_game.update!(visible: true)
      # Will have to figure out how to update realty_game_ids on scoot
      # hpg_scoot.update(
      #   realty_game_ids: [2, 3]
      # )
      Creators::RealtyGameListingCreator.new.create_game_listing_from_existing_listing(
        # uuid, url, portal, scrape_item_data
        realty_game.uuid, listing_for_game
      )

      render json: {
        realty_game_id: realty_game.id,
        realty_game_uuid: realty_game.uuid,
        listing_for_game_id: listing_for_game.id,
        listing_for_game_uuid: listing_for_game.uuid
      }, status: :ok
    rescue StandardError => e
      message_to_return = "First backtrace #{e.backtrace.first} Failed to init game with pre-scraped content: #{e.message}"
      logger.error("Failed to init game with pre-scraped content: #{e.message}")
      logger.error("First backtrace #{e.backtrace.first}")

      render json: { error: message_to_return }, status: :internal_server_error
    end

    # # New endpoint that adds a listing to an existing game using pre-scraped content
    def add_prepped_listing_to_game
      realty_game_id = params[:realty_game_id]
      # retrieval_portal = params[:retrieval_portal]
      retrieval_end_point = params[:retrieval_end_point]
      # scrape_item_data = params[:scrape_item_data]

      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

      # extracted_asset_data = realty_asset_params # params[:extracted_asset_data]
      # extracted_listing_data = extracted_listing_data_params # params[:extracted_listing_data]
      # extracted_image_urls = params[:extracted_image_urls]

      listing_for_game = create_listing_from_prepped_content(
        realty_asset_params, extracted_listing_data_params,
        params[:extracted_image_urls], retrieval_end_point
      )
      # listing_for_game.update!(visible: true)

      realty_game = RealtyGame.find(realty_game_id)

      Creators::RealtyGameListingCreator.new.create_game_listing_from_existing_listing(
        # uuid, url, portal, scrape_item_data
        realty_game.uuid, listing_for_game
      )

      # realty_game.add_listing_from_pre_scraped_content(retrieval_end_point, retrieval_portal, scrape_item_data)

      render json: {
        message: "listing #{listing_for_game.id} added to game #{realty_game.id}",
        realty_game_id: realty_game.id,
        realty_game_uuid: realty_game.uuid,
        listing_for_game_id: listing_for_game.id,
        listing_for_game_uuid: listing_for_game.uuid
      }, status: :ok
    rescue StandardError => e
      logger.error("Failed to add pre-scraped listing: #{e.message}")
      render json: { error: e.message }, status: :internal_server_error
    end

    def create_listing_from_prepped_content(
      extracted_asset_data, extracted_listing_data,
      extracted_image_urls, retrieval_end_point
    )
      # url, portal, scrape_item_data)
      standardised_listing_hash = {}
      standardised_listing_hash['import_url'] = retrieval_end_point
      standardised_listing_hash['asset_data'] = extracted_asset_data
      standardised_listing_hash['listing_data'] = extracted_listing_data
      standardised_listing_hash['listing_data']['import_url'] = retrieval_end_point
      standardised_listing_hash['asset_data']['import_url'] = retrieval_end_point
      standardised_listing_hash['listing_image_urls'] = extracted_image_urls
      # request_host = 'guise_listing_server'
      creator = Creators::Full::FullListingAndAssetCreator.new

      sale_listing = creator.create_from_standardised_hash(
        standardised_listing_hash
        # request_host
      )
      sale_listing
    end

    private

    def extracted_listing_data_params
      params.require(:extracted_listing_data).permit(
        # def realty_asset_params
        #   params.require(:extracted_asset_data).permit(
        :title,
        :description,
        :archived,
        :commission_cents,
        :commission_currency,
        :currency,
        :design_style,
        :details_of_rooms,
        :discarded_at,
        :extra_sale_details,
        :furnished,
        :hide_map,
        :highlighted,
        :host_on_create,
        :is_ai_generated_listing,
        :listing_pages_count,
        :listing_slug,
        :listing_tags,
        :main_video_url,
        :obscure_map,
        :page_section_listings_count,
        :position_in_list,
        :price_sale_current_cents,
        :price_sale_current_currency,
        :price_sale_original_cents,
        :price_sale_original_currency,
        :property_board_items_count,
        :publish_from,
        :publish_till,
        :reference,
        :related_urls,
        :reserved,
        :sale_listing_features,
        :sale_listing_flags,
        :sale_listing_gen_prompt,
        :service_charge_yearly_cents,
        :service_charge_yearly_currency,
        :site_visitor_token,
        :sl_photos_count,
        :visible,
        :import_url
      )
    end

    # def extracted_listing_data_params
    #   params.require(:extracted_listing_data).permit(
    def realty_asset_params
      params.require(:extracted_asset_data).permit(
        'title',
        'categories',
        'city',
        'city_search_key',
        'constructed_area',
        'count_bathrooms',
        'count_bedrooms',
        'count_garages',
        'count_toilets',
        'country',
        'description',
        'details',
        'discarded_at',
        'energy_performance',
        'energy_rating',
        'floor',
        'has_rental_listings',
        'has_sale_listings',
        'has_sold_transactions',
        'host_on_create',
        'latitude',
        'longitude',
        'neighborhood',
        'neighborhood_search_key',
        'plot_area',
        'postal_code',
        'prop_origin_key',
        'prop_state_key',
        'prop_type_key',
        'province',
        'ra_photos_count',
        'realty_asset_flags',
        'realty_asset_tags',
        'reference',
        'region',
        'rental_listings_count',
        'sale_listings_count',
        'site_visitor_token',
        'sold_transactions_count',
        'street_address',
        'street_number',
        'year_construction',
        'import_url'
      )
    end

    def set_scoot
      incoming_subdomain = request.subdomain.presence || 'default'
      @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)

      return if @scoot

      render json: { error: 'Scoot not found' }, status: :not_found
    end
  end
end
