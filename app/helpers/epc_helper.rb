module EpcHelper
  BAND_POSITIONS = {
    'A' => 25,
    'B' => 75,
    'C' => 125,
    'D' => 175,
    'E' => 225,
    'F' => 275,
    'G' => 325
  }

  RATING_BANDS = {
    'A' => { min: 92 },
    'B' => { min: 81, max: 91 },
    'C' => { min: 69, max: 80 },
    'D' => { min: 55, max: 68 },
    'E' => { min: 39, max: 54 },
    'F' => { min: 21, max: 38 },
    'G' => { min: 1, max: 20 }
  }

  def get_rating_band(rating)
    RATING_BANDS.each do |band, range|
      return band if rating >= range[:min] && (range[:max].nil? || rating <= range[:max])
    end
    'G' # Default to lowest band
  end

  def current_rating_band
    get_rating_band(@epc_data[:current_rating])
  end

  def potential_rating_band
    get_rating_band(@epc_data[:potential_rating])
  end

  def current_rating_letter
    current_rating_band
  end

  def potential_rating_letter
    potential_rating_band
  end

  def current_y
    BAND_POSITIONS[current_rating_band] || 0
  end

  def potential_y
    BAND_POSITIONS[potential_rating_band] || 0
  end

  def current_x
    415
  end

  def potential_x
    515
  end

  def current_band_class
    "band-#{current_rating_band}"
  end

  def potential_band_class
    "band-#{potential_rating_band}"
  end
end
