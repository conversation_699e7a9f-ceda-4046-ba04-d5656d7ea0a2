class SoldTransactionPriceModel < Eps::Base
  def build
    s_transactions = SoldTransaction.all

    # Train the model
    data = s_transactions.map { |v| features(v) }
    model = Eps::Model.new(data, target: :sold_price_cents, split: :sold_date)
    puts model.summary

    begin
      # Save to file
      File.write(model_file, model.to_pmml)
    rescue StandardError => e
      puts "Error generating PMML: #{e.message}"
      puts "Backtrace: #{e.backtrace.join("\n")}"
      raise e # Re-raise the exception if you want it to halt execution
    end

    # Ensure reloads from file
    @model = nil
  end

  def predict(s_transaction)
    model.predict(features(s_transaction))
  end

  private

  def features(s_transaction)
    {
      st_tenure: s_transaction.st_tenure || 'Unknown',
      st_property_type: s_transaction.st_property_type || 'Unknown',
      st_postal_code: s_transaction.st_postal_code || 'Unknown',
      st_age_of_property: s_transaction.st_age_of_property || 'Unknown',
      # bedrooms: s_transaction.count_bedrooms || 0,
      # bathrooms: s_transaction.count_bathrooms || 0,
      # toilets: s_transaction.count_toilets || 0,
      # garages: s_transaction.count_garages || 0,
      # city_id: s_transaction.city&.to_s || 'Unknown', # Use safe navigation
      month: s_transaction.sold_date&.strftime('%b') || 'Unknown',
      sold_date: s_transaction.sold_date,
      sold_price_cents: s_transaction.sold_price_cents
      # property_type: s_transaction.st_property_type || 'Unknown',
      # age_of_property: s_transaction.year_construction ? (s_transaction.sold_date.year - s_transaction.year_construction) : nil,
      # latitude: s_transaction.latitude || 'Unknown',
      # longitude: s_transaction.longitude || 'Unknown',
      # constructed_area: s_transaction.constructed_area || 'Unknown',
      # plot_area: s_transaction.plot_area || 'Unknown',
      # neighborhood: s_transaction.neighborhood || 'Unknown',
      # is_new_home: s_transaction.new_home,
      # is_auction: s_transaction.auction,
      # is_furnished: s_transaction.furnished
    }
  end

  def model
    @model ||= Eps::Model.load_pmml(File.read(model_file))
  end

  def model_file
    # File.join(__dir__, 'price_model.pmml')
    File.join(Rails.root, 'app', 'ml_pmmls', 'sold_transaction_price_model.pmml')
  end
end
