class SoldTransactionEpcPriceModel < Eps::Base
  MODEL_FILENAME_PREFIX = 'sold_transaction_epc_price_model'.freeze

  class << self
    def manager
      @manager ||= MlModelManager.new(MODEL_FILENAME_PREFIX)
    end

    def build(s_transactions_with_epcs, version, meta_info = {})
      model = new
      model_summary, evaluation_metrics = model.build(s_transactions_with_epcs, version) # Return both summary and metrics
      puts model_summary
      meta_info[:version] = version
      meta_info[:model_summary] = model_summary
      meta_info[:evaluation_metrics] = evaluation_metrics # Store evaluation metrics
      manager.add_model(version, model, meta_info)
    end

    def predict(version, s_transaction_with_epc)
      manager.predict(version, s_transaction_with_epc)
    end

    def get_meta(version)
      manager.get_meta(version)
    end
  end

  def build(s_transactions_with_epcs, version)
    # Step 1: Prepare and preprocess data
    training_data = s_transactions_with_epcs.map { |v| features(v) }
    # training_data = preprocess_data(training_data) # Handle missing values and outliers

    # Step 2: Split data into training and validation sets (e.g., 80/20 split)
    training_data, validation_data = split_data(training_data, split_ratio: 0.8)

    # Step 3: Train the model
    puts "Training data stats: #{training_data.size} records"
    puts "Validation data stats: #{validation_data.size} records"
    # puts "Sold date stats: #{training_data.pluck(:sold_date).tally.sort}"
    model = Eps::Model.new(training_data, target: :sold_price_sterling, split: :sold_month_and_year)
    model_summary = model.summary

    # Step 4: Save the model to a file, using version in filename
    begin
      File.write(model_file(version), model.to_pmml)
    rescue StandardError => e
      Rails.logger.error("Error generating PMML for version #{version}: #{e.message}")
      Rails.logger.error("Backtrace: #{e.backtrace.join("\n")}")
      raise e
    end

    # Step 5: Evaluate the model on validation data
    evaluation_metrics = evaluate_model(model, validation_data)

    @model = nil # Reset the model instance variable
    [model_summary, evaluation_metrics] # Return both summary and metrics
  end

  def predict(s_transaction_with_epc)
    model.predict(features(s_transaction_with_epc))
  end

  private

  def features(s_transaction_with_epc)
    # Enhanced feature engineering
    sold_date = s_transaction_with_epc.sold_date
    {
      st_tenure: s_transaction_with_epc.st_tenure || 'Missing', # Explicitly handle missing values
      st_property_type: s_transaction_with_epc.st_property_type || 'Missing',
      st_postal_code: s_transaction_with_epc.st_postal_code || 'Missing',
      st_age_of_property: s_transaction_with_epc.st_age_of_property || 'Missing',

      total_floor_area: s_transaction_with_epc.total_floor_area || 'Missing',
      number_heated_rooms: s_transaction_with_epc.number_heated_rooms || 'Missing',
      number_habitable_rooms: s_transaction_with_epc.number_habitable_rooms, # Keep as nil if missing
      construction_age_band: s_transaction_with_epc.construction_age_band || 'Missing',
      current_energy_rating: s_transaction_with_epc.current_energy_rating || 'Missing',

      # Temporal features
      # sold_month: sold_date&.strftime('%b') || 'Missing',
      sold_year: sold_date&.strftime('%Y') || 'Missing',
      sold_month_and_year: sold_date&.strftime('%m/%Y') || 'Missing',
      sold_date: sold_date&.strftime || 'MissingDate',

      # # Interaction features
      # floor_area_per_room: (s_transaction_with_epc.total_floor_area.to_f / s_transaction_with_epc.number_habitable_rooms),
      # energy_rating_and_area: "#{s_transaction_with_epc.current_energy_rating}_#{s_transaction_with_epc.total_floor_area.to_f.round}",

      # Target variable
      sold_price_sterling: s_transaction_with_epc.sold_price_cents / 100.0
      # sold_price_cents: s_transaction_with_epc.sold_price_cents
    }
  end

  def preprocess_data(data)
    # Step 1: Handle missing values
    data.map do |record|
      record[:number_habitable_rooms] ||= data.map { |r| r[:number_habitable_rooms] }.compact.mean # Impute with mean
      record[:total_floor_area] = record[:total_floor_area] == 'Missing' ? data.map { |r| r[:total_floor_area] }.compact.mean : record[:total_floor_area]
      record
    end

    # # Step 2: Remove outliers (example: remove extreme sold_price_cents)
    # price_threshold = data.map { |r| r[:sold_price_cents] }.percentile(99) # Top 1% as threshold
    # data = data.select { |r| r[:sold_price_cents] <= price_threshold && r[:sold_price_cents] > 0 }

    # # Step 3: Apply transformations (e.g., log transform sold_price_sterling)
    # data.each { |r| r[:transformed_sold_price] = Math.log(r[:sold_price_sterling]) if r[:sold_price_sterling].positive? }
    # data
  end

  def split_data(data, split_ratio: 0.8)
    data.shuffle! # Randomize data
    split_index = (data.size * split_ratio).to_i
    [data[0...split_index], data[split_index..-1]]
  end

  def evaluate_model(model, validation_data)
    predictions = validation_data.map { |v| model.predict(v) }
    actuals = validation_data.map { |v| v[:sold_price_sterling] }

    # Calculate evaluation metrics
    mae = predictions.zip(actuals).map { |p, a| (p - a).abs }.sum / predictions.size
    rmse = Math.sqrt(predictions.zip(actuals).map { |p, a| (p - a)**2 }.sum / predictions.size)
    r2 = 'TODO'
    # r2 = 1 - (predictions.zip(actuals).map { |p, a| (p - a)**2 }.sum / actuals.map { |a| (a - actuals.mean)**2 }.sum)
    puts "predictions: #{predictions}"
    puts "actuals: #{actuals}"
    { mae: mae, rmse: rmse, r2: r2 }
  end

  def model
    @model ||= Eps::Model.load_pmml(File.read(model_file(@version)))
  end

  def model_file(version)
    File.join(Rails.root, 'app', 'ml_pmmls', "#{MODEL_FILENAME_PREFIX}_#{version}.pmml")
  end

  # Add a version attribute for storing the model version
  attr_accessor :version
end
