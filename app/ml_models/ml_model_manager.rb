require 'json'
# This model suggested by grok AI
class MlModelManager
  def initialize(model_filename_prefix)
    @model_filename_prefix = model_filename_prefix
    @models = {}
    @base_path = File.join(Rails.root, 'app', 'ml_pmmls')
    Dir.mkdir(@base_path) unless Dir.exist?(@base_path)
  end

  def add_model(version, model, meta_info = {})
    @models[version] = { model: model, meta: meta_info }

    # Save metadata to file
    meta_file_path = File.join(@base_path, "#{@model_filename_prefix}_#{version}_metainfo.json")
    File.write(meta_file_path, meta_info.to_json)
  end

  def get_model(version)
    @models[version][:model] || raise("Model version #{version} not found")
  end

  def get_meta(version)
    if @models[version]
      @models[version][:meta] # If already loaded, return from memory
    else
      meta_file_path = File.join(@base_path, "#{version}.json")
      raise "Metadata for model version #{version} not found" unless File.exist?(meta_file_path)

      meta_info = JSON.parse(File.read(meta_file_path))
      @models[version] = { meta: meta_info } # Load into memory
      meta_info

    end
  end

  def predict(version, s_transaction_with_epc)
    model = get_model(version)
    model.predict(s_transaction_with_epc)
  end
end
