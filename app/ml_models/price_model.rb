class PriceModel < Eps::Base
  def build
    houses = SoldTransaction.all

    # Train the model
    data = houses.map { |v| features(v) }
    model = Eps::Model.new(data, target: :sold_price_cents, split: :sold_date)
    puts model.summary

    begin
      # Save to file
      File.write(model_file, model.to_pmml)
    rescue StandardError => e
      puts "Error generating PMML: #{e.message}"
      puts "Backtrace: #{e.backtrace.join("\n")}"
      raise e # Re-raise the exception if you want it to halt execution
    end

    # Ensure reloads from file
    @model = nil
  end

  def predict(house)
    model.predict(features(house))
  end

  private

  def features(house)
    {
      # bedrooms: house.count_bedrooms || 0,
      # bathrooms: house.count_bathrooms || 0,
      # toilets: house.count_toilets || 0,
      # garages: house.count_garages || 0,
      city_id: house.city&.to_s || 'Unknown', # Use safe navigation
      month: house.sold_date&.strftime('%b') || 'Unknown',
      sold_date: house.sold_date,
      sold_price_cents: house.sold_price_cents
      # property_type: house.st_property_type || 'Unknown',
      # age_of_property: house.year_construction ? (house.sold_date.year - house.year_construction) : nil,
      # latitude: house.latitude || 'Unknown',
      # longitude: house.longitude || 'Unknown',
      # constructed_area: house.constructed_area || 'Unknown',
      # plot_area: house.plot_area || 'Unknown',
      # neighborhood: house.neighborhood || 'Unknown',
      # is_new_home: house.new_home,
      # is_auction: house.auction,
      # is_furnished: house.furnished
    }
  end

  def model
    @model ||= Eps::Model.load_pmml(File.read(model_file))
  end

  def model_file
    # File.join(__dir__, 'price_model.pmml')
    File.join(Rails.root, 'app', 'ml_pmmls', 'price_model.pmml')
  end
end
